export default {
	template: {
		url: '/setting/template/info',
		auth: false,
		method: 'POST',
	},
	appInfo: {
		url: '/setting/app/info',
		auth: false,
		method: 'GET',
	},
	uploadInfo: {
		url: '/setting/oss/uploadInfo',
		auth: false,
		method: 'GET',
	},
	login: {
		wechat:{
			url: '/user/login/wechat',
			auth: false,
			method: 'GET',
		},
		mp:{
			url:'/user/login/mp',
			auth: false,
			method: 'POST',
		},
		app:{
			url:'/user/login/app',
			auth: false,
			method: 'POST',
		},
		account:{
			url:'/user/login/login',
			auth: false,
			method: 'POST',
		},
		myinfo:{
			url:'/user/user/myinfo',
			auth: true,
			method: 'GET',
		},
		refresh:{
			url:'/user/login/refresh',
			auth: false,
			method: 'POST',
		},
	},
	wechat: {
		info:{
			url:'/wechat/sys/basicInfo',
			auth: false,
			method: 'GET',
		},
		jsapi:{
			config:{
				url: '/wechat/call/jsapi_config',
				auth: false,
				method: 'POST',
			},
			location:{
				url:'/wechat/other/location',
				auth: false,
				method: 'POST',
			},
		},
		mp:{
			getPhoneNumber:{
				url: '/wechat/mp/getPhoneNumber',
				auth: false,
				method: 'POST',
			},
			scanStart:{
				url: '/wechat/mp/qrcodeStart',
				auth: true,
				method: 'GET',
			},
			scanAuth:{
				url: '/wechat/mp/qrcodeAuth',
				auth: true,
				method: 'GET',
			}
		},
	},
	sms: {
		validCode:{
			url:'/sms/sms/validCode',
			auth: true,
			method: 'POST',
		},
		sendValidCode:{
			url:'/sms/sms/sendValidCode',
			auth: true,
			method: 'POST',
		},
	},
	user: {
		updateMyPhone:{
			url:'/user/user/updateMyPhone',
			auth: true,
			method: 'POST',
		},
		updateMyHeadAndName:{
			url:'/user/user/updateMyHeadAndName',
			auth: true,
			method: 'POST',
		},
	},
	locationInfo:{
		url:'/setting/qqMap/location_info',
		auth: true,
		method: 'GET',
	}
}

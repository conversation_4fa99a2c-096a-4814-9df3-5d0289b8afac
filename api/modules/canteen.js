export default{
	info:{
		key:{
			url: '/homecare/canteen/mobile/card_key_info',
			auth: true,
			method: 'GET',
		}
	},
	account:{
		listpg:{
			url: '/homecare/canteen/mobile/account_listpg',
			auth: true,
			method: 'GET',
		},
		info:{
			url: '/homecare/canteen/mobile/account_info',
			auth: true,
			method: 'GET',
		},
		open:{
			url: '/homecare/canteen/mobile/account_open',
			auth: true,
			method: 'POST',
		},
		cardOpen:{
			url: '/homecare/canteen/mobile/card_open',
			auth: true,
			method: 'POST',
		},
	},
	deposit:{
		cardInfo:{
			url: '/homecare/canteen/mobile/card_recharge_info',
			auth: true,
			method: 'GET',
		},
		listpg:{
			url: '/homecare/canteen/mobile/deposit_listpg',
			auth: true,
			method: 'GET',
		},
		deposit:{
			url: '/homecare/canteen/mobile/deposit',
			auth: true,
			method: 'POST',
		},
	},
	refund:{
		cardInfo:{
			url: '/homecare/canteen/mobile/card_recharge_info',
			auth: true,
			method: 'GET',
		},
		listpg:{
			url: '/homecare/canteen/mobile/refund_listpg',
			auth: true,
			method: 'GET',
		},
		refund:{
			url: '/homecare/canteen/mobile/refund',
			auth: true,
			method: 'POST',
		},
	},
	consumer:{
		listpg:{
			url: '/homecare/canteen/mobile/consumer_listpg',
			auth: true,
			method: 'GET',
		},
	}
}
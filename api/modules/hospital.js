export default {
	shop: {
		list: {
			url: '/consumer/shop/list',
			auth: false,
			method: 'POST',
		},
		pageList: {
			url: '/hospital/shop/listpg',
			auth: false,
			method: 'GET',
		},
		info: {
			url: '/hospital/shop/info',
			auth: false,
			method: 'GET',
		},
		infoByProject: {
			url: '/hospital/shop/infoByProjectId',
			auth: false,
			method: 'GET',
		},
		hotList:{
			url: '/hospital/shop/listByHot',
			auth: false,
			method: 'GET',
		},
		project: {
			list: {
				url: '/hospital/shop/projectList',
				auth: false,
				method: 'GET',
			},
			info: {
				url: '/hospital/shop/projectInfo',
				auth: false,
				method: 'GET',
			},
		},
		agreement: {
			pz: {
				url: '/hospital/protocol/infoByCode',
				auth: false,
				method: 'GET',
			}
		},
	},
	index: {
		swiper: {
			url: '/hospital/swipers/list',
			auth: false,
			method: 'get'
		},
		navMenu: {
			url: '/hospital/navMenu/list',
			auth: false,
			method: 'get'
		},
	},
	order: {
		create: {
			url: '/hospitalOrder/reserve/createOrder',
			auth: true,
			method: 'post'
		},
		info: {
			url: '/hospitalOrder/reserve/orderInfo',
			auth: true,
			method: 'get'
		},
		pay: {
			url: '/hospitalOrder/reserve/orderPay',
			auth: true,
			method: 'post'
		},
		payQuery:{
			url: '/hospitalOrder/reserve/orderPayQuery',
			auth: true,
			method: 'post'
		},
		payPause:{
			url: '/hospitalOrder/reserve/orderPause',
			auth: true,
			method: 'post'
		},
		mylist:{
			url:'/hospitalOrder/info/myOrderListpg',
			auth:true,
			method:'get'
		},
		myInfo:{
			url:'/hospitalOrder/info/myOrderInfo',
			auth:true,
			method:'get'
		},
		myStateCount:{
			url:'/hospitalOrder/statistics/orderStateCount',
			auth:true,
			method:'get'
		},
		noticeTemplate:{
			url:'/wechat/mp/getTemplateByCode',
			auth:false,
			method:'post'
		},
	},
	city:{
		list:{
			url: '/hospital/city/list',
			auth: false,
			method: 'GET',
		},
		area:{
			list:{
				url: '/hospital/city/areaList',
				auth: false,
				method: 'GET',
			},
		},
		all:{
			url: '/hospital/city/provinceCityList',
			auth: false,
			method: 'GET',
		},
	},
	project: {
		base:{
			info:{
				url: '/hospital/base/projectInfo',
				auth: false,
				method: 'GET',
			},
			detail:{
				url: '/hospital/base/projectDetaillist',
				auth: false,
				method: 'GET',
			},
		},
		price:{
			byHospital:{
				url: '/hospital/shop/projectInfoByBase',
				auth: false,
				method: 'GET',
				
			},
		},
	},
	employee: {
		operate:{
			pageList: {
				url: '/hospital/employeeOperate/listpg',
				auth: true,
				method: 'GET',
			},
			audit:{
				url: '/hospital/employeeOperate/audit',
				auth: true,
				method: 'POST',
			},
			show:{
				url: '/hospital/employeeOperate/show',
				auth: true,
				method: 'POST',
			},
			delete:{
				url: '/hospital/employeeOperate/delete',
				auth: true,
				method: 'POST',
			},
		},
		register:{
			url: '/hospital/employee/register',
			auth: true,
			method: 'POST',
		},
		myInfo:{
			url: '/hospital/employee/myInfo',
			auth: true,
			method: 'GET',
		},
		checkMyIsEmployee:{
			url: '/hospital/check/myIsEmployee',
			auth: true,
			method: 'GET',
		},
		receiveWork:{
			url: '/hospital/employee/receiveWork',
			auth: true,
			method: 'GET',
		},
		order:{
			list:{
				url: '/hospitalorder/employee/employeeOrderListpg',
				auth: true,
				method: 'GET',
			},
			agree:{
				url: '/hospitalorder/employee/orderEmployeeAgree',
				auth: true,
				method: 'GET',
			},
			over:{
				url: '/hospitalorder/employee/orderEmployeeOver',
				auth: true,
				method: 'GET',
			}
		}
	},
	setting: {
		phone:{
			url: '/hospital/sys/telInfo',
			auth: false,
			method: 'GET',
		}
	},
}
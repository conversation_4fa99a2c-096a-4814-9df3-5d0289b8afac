<template>
	<ut-page class="main-page">
		<ut-top class="top" bg-color="#fff" @topHeight="topHeight">
			<area-hospital ref="area"
			:colors="colors" 
			:my="[]" 
			:hot="hotHospital"
			:list="searchHospital"
			:title="title"
			:searchData="searchData"
			:city="city"
			:province="province"
			@scan="scan" 
			@citySelect="citySelect" 
			@communitySelect="hospitalSelect"
			@search="search"
			></area-hospital>
		</ut-top>
		<view class="item-body">
			<u-swiper :list="swipers" :height="180" @click="click"></u-swiper>
		</view>
		
		<view class="item-body menu">
			<ut-menu :list="menuData" shape="circle" :row-count="4" :image-size="88"></ut-menu>
		</view>

		<view class="item-body" >
			<index-hospital :list="hospitalList"></index-hospital>
		</view>
			
		<!-- <view class="padding-tb-xl margin-bottom-lg"></view>	 -->
<!-- 		<view class="tip" v-show="isMore">滑动加载更多</view>
		<view class="tip" v-show="isLoading">正在加载</view>
		<view class="tip" v-show="isOver">加载完成</view> -->
	</ut-page>
</template>

<script>
	let app = getApp();
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex'

	import share from '@/common/share';
	
	import {BASE_URL,APP_TITLE} from "@/config"
	import * as Token from '@/common/store/modules/token.js';

	import areaHospital from './components/area-hospital.vue'
	import areaSearch from '@/pages/xp/index/area-search.vue'
	import indexMenu from '@/pages/xp/index/index-menu.vue'
	import indexHospital from '@/pages/xp/index/index-hospital.vue'
	export default {
		components: {
			areaHospital,
			areaSearch,
			indexMenu,
			indexHospital,
		},
		options: { //小程序样式穿透
			styleIsolation: 'shared'
		},
		data() {
			return {
				colors:'',	
				title:APP_TITLE,
				h: 0,
				s: 0,			
				isLoaded:false,
				isLoading: false,
				isOver: false,
				isMore: false,
				pageReq: {
					pageindex: 1,
					pagesize: 20,
				},
				swipers:[],
				menuData: [],
				hospitalList: [],
				hotHospital:[],
				searchHospital:[],				
				searchData: [],
				cityData: {},
				province: [],
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				city: state => state.init.city,
				userInfo: state => state.user.info,
				locationAddress: state => state.init.locationAddress,
			}),

		},
		onLoad: async function(options) {
			await this.$onLaunched;
			// #ifdef APP-PLUS
			this.bottoms = '0' //在APP下 规格弹窗的位置发生变化
			// #endif
			
			this.getLocation()
			this.pageLoad()
		},
		onPullDownRefresh() {
			// uni.showToast({
			// 	title:'重新加载'
			// })
			// this.pageReq.pageindex=1
			this.pageLoad()
			// setTimeout(function() {
			// 	uni.stopPullDownRefresh();
			// }, 1000);
		},

		onShow() {

		},
		onHide() {

		},
		async onPageScroll(e) {

		},
		methods: {
			...mapActions(['setLocationAddress', 'setCity']),
			topHeight(h, s) {
				this.h = h
				this.s = s
			},
			pageLoad() {
				this.getArea()
				this.getSwipers()
				this.getNavMenu()
				//this.getHospitalList()
			},
			loadData(){
				this.getHospitalList()
				this.getHotHospital()
			},
			getArea() {
				this.$ut.api("hospital/city/all", {
					commKey: this.commKey
				}).then(res => {
					this.province = res.data
				})
			
			},
			getSwipers() {
				this.swipers = []
				this.$ut.api("hospital/index/swiper", {
					commKey: this.commKey
				}).then(res => {
					if (res.data) {
						res.data.forEach(o => {
							this.swipers.push(o.img + "&width=750")
						})
					}
				})
			},
			getNavMenu() {
				this.menuData = []
				this.$ut.api("hospital/index/navMenu", {
					commKey: this.commKey
				}).then(res => {
					if (res.data) {
						res.data.forEach(o => {
							this.menuData.push({
								image: o.imgHead,
								title: o.title,
								path: o.url,
								cmd:o.cmdCode,
							});
						})
					}
				})
			},
			getHospitalList() {
				this.isLoading = true
				let location=''
				if(this.locationAddress){
					if(this.locationAddress.location && this.locationAddress.location.longitude){
						location=this.locationAddress.location.longitude+','+this.locationAddress.location.latitude
					}
				}
				this.$ut.api("hospital/shop/pageList", {
					commKey: this.commKey,
					cityCode:this.city.code,
					location:location,
					//cityId:
					pageindex: this.pageReq.pageindex,
					pagesize: this.pageReq.pagesize,
				}).then(res => {
					if (res.data) {
						if (this.pageReq.pageindex == 1) this.hospitalList = []
			
						if (res.data.info) {
							this.hospitalList = this.hospitalList.concat(res.data.info);
						}
			
						if (this.pageReq.pageindex >= res.data.page) {
							this.isOver = true
						}
			
						this.isLoading = false
					}
			
				})
			},
			getHotHospital(){
				this.$ut.api("hospital/shop/hotList", {
					commKey: this.commKey,
					cityCode:this.city.code,
				}).then(res => {
					this.isLoaded=true
					this.hotHospital = res.data
				})
			},
			search(data){
				this.$ut.api("hospital/shop/hotList", {
					commKey: this.commKey,
					cityCode:this.city.code,
					key:data.key,
				}).then(res => {
					this.searchHospital = res.data
				})
				
			},
			getLocation() {
				if(this.locationAddress && this.locationAddress.location && this.locationAddress.location.longitude ){
					this.loadData()
					return
				} 
				this.$wxsdk.getLocationToAddress().then(res => {
					this.setLocationAddress(res)
					
					if (res.info) {
						this.setCity(res.info.city)
						this.loadData()
					}else{
						this.loadData()
					}
				}).catch(err => {
					this.loadData()
				})
			},
			hospitalSelect(item){
				// console.log(item)
				this.$tools.routerTo("/pagesC/hospital/hospital",{id:item.id})
			},
			citySelect(item){
				this.getHospitalList()
				this.getHotHospital()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.main-page {
		background: #fff;
		position: relative;
		display: block;
	}
	
	.menu{
		padding-top: 30rpx !important;
		padding-bottom: 10rpx !important;
	}
</style>
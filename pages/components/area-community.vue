<template>
	<view>
		<view class="area-community" :class="{'full':focus}">
			<view class="top-nav" :class="{'full':focus}">
				<view class="content" :class="{'full':focus}" :style="{borderColor:colors}">

					<view class="scan" v-if="!focus" @click="scan()">
						<u-icon name="scan" :color="colors" size="28"></u-icon>
					</view>
					<view v-else class="scan area" @click="areaClick()">
						<u-transition :show="true" mode="fade">
							<view class="area-content">
								<view class="city">{{city.name||'全国'}}</view>
								<view class="area-arrow" :class="{'open':showCity}">
									<u-icon name="arrow-down-fill" :color="colors" size="12"></u-icon>
								</view>
							</view>
						</u-transition>

					</view>
					<view class="community" @click="inputClick">
						<u--input border="none" confirm-type="search" @confirm="searchClick" placeholder="搜索" v-model="keyword"></u--input>
					</view>
					<u-transition :show="true" mode="fade">
						<view v-if="focus" class="search" :style="{background:colors}" @click="searchClick">
							<u-icon name="search" color="#fff" size="18"></u-icon>
							搜索
						</view>
						<view v-else><u-icon name="search" :color="colors" size="24" @click="inputClick"></u-icon></view>
					</u-transition>
				</view>

				<view class="title">{{title}}</view>
			</view>
			<view class="city-box" v-if="showCity && focus">
				<view class="cur-location">
					<view>
						当前位置：
						<text class="curr-city" v-if="locationAddress.info && locationAddress.info.city" 
							@click="cityClick({code:locationAddress.info.city.code,name:locationAddress.info.city.name})">{{locationAddress.info.city.name}}</text>
						<text v-else class="curr-city" @click="reGetLocation">重新获取</text>
					</view>
					<view @click="cityClick({})">
						全国
					</view>
				</view>
				<view class="province-city-box">
					<view class="province">
						<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
							<template v-for="(item,index) in province">
								<view class="province-item-box">
									<view :key="index" class="province-item" @click="provinceClick(item,index)"
										:class="{'active':index==provinceIndex,'active-prev':index==provinceIndex-1,'active-next':index==provinceIndex+1}">
										{{item.name}}
									</view>
								</view>
							</template>

						</scroll-view>
					</view>
					<view class="city">
						<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
							<template v-for="(item,index) in cityData">
								<view :key="index" class="city-item" :class="{'active':item.code==city.code}"
									@click="cityClick(item,index)">
									<view class="name">{{item.name}}</view>
									<view class="num">{{item.num || ''}}</view>
								</view>
							</template>

						</scroll-view>
					</view>
				</view>
			</view>
			<view class="community-box" v-if="!showCity && focus && !isSearch">
				<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
					<view class="my-community" v-if="my.length">
						<view class="label">
							<u-icon name="star" labelSize="10" label="我的社区" :color="colors" size="12"></u-icon>
						</view>
						<view class="community-wrap">
							<template v-for="(item,index) in my" v-if="index<4">
								<view class="community-name" :key="index" @click="communityClick(item)">
									<view class="name-content" v-if="item.id==communityId" :style="{borderColor:colors,backgroundColor:colors,color:'#fff'}">
										<view class="name">{{item.name}}</view>
										<view class="addr">{{item.city}}</view>
									</view>
									<view class="name-content" v-else :style=" {borderColor:colors,color:colors}">
										<view class="name">{{item.name}}</view>
										<view class="addr">{{item.city}}</view>
									</view>
								</view>

							</template>


						</view>

					</view>
					<view class="host-community" v-if="host.length">
						<view class="label">
							<u-icon name="attach" labelSize="10" label="最近社区" :color="colors" size="12"></u-icon>
						</view>
						<view class="hot-community-wrap">
							<template v-for="(item,index) in host" v-if="index<20">
								<community-item :key="index" :colors="colors" :item="item" @communitySelect="communityClick"></community-item>
							</template>
						</view>

					</view>
				</scroll-view>
			</view>
			<view class="search-box-wrap" v-if="isSearch">
				<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
					<template v-for="(item,index) in list">
						<community-item :key="index" :colors="colors" :item="item" @communitySelect="communityClick"></community-item>
					</template>
				</scroll-view>
			</view>
			<view class="loading" v-if="isLoading">
				<view class="sk-roller" :style="{color:colors}">
					<div></div>
					<div></div>
					<div></div>
					<div></div>
					<div></div>
					<div></div>
					<div></div>
					<div></div>
				</view>
			</view>
		</view>


		<u-overlay :show="focus" @click="overlayClick" :z-index="100"></u-overlay>
	</view>
</template>

<script>
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex'
	import communityItem from './community-item.vue'
	export default {
		options: { //小程序样式穿透
			styleIsolation: 'shared'
		},
		components:{
			communityItem,
		},
		props: {
			colors: {
				type: String
			},
			title: {
				type: String
			},
			// city: {
			// 	type: Object,
			// 	default: () => {}
			// },
			my: {
				type: Array,
				default: () => []
			},
			host: {
				type: Array,
				default: () => []
			},
			list: {
				type: Array,
				default: () => []
			},
			province: {
				type: Array,
				default: () => []
			},
		},
		data() {
			return {
				isLoading: false,
				keyword: '',
				isSearch: false,
				// cityTemp: {},
				focus: false,
				showCity: false,
				provinceIndex: 0,

			}
		},
		computed: {
			...mapState({
				city: state => state.init.city,
				locationAddress: state => state.init.locationAddress,
				community: state => state.init.community,
			}),
			cityData() {
				if (!this.province) return []
				if (!this.province.length) return []
				let obj = this.province[this.provinceIndex]
				if (!obj) return []
				if (!obj.citys) return []
				if (!obj.citys.length) return []
				return obj.citys
			},
			communityId(){
				if(!this.community) return
				return this.community.id
			}
		},
		watch: {
			// city: {
			// 	deep: true,
			// 	handler(v) {
			// 		this.cityTemp = v
			// 	}
			// },
			host: {
				deep: true,
				handler(v) {
					this.isLoading = false
				}
			},
			list: {
				deep: true,
				handler(v) {
					this.isLoading = false
				}
			},
		},
		methods: {
			...mapActions(['setCity','setCommunity','setLocationAddress']),
			scan() {
				this.$wxsdk.scanQRCode().then(res => {
					if (res.errMsg == 'scanCode:ok') {
						console.log(res)
						this.$emit('scan', {
							result: res.result,
							type: res.scanType,
							path: res.path,
						})
					}
				})
			},
			reGetLocation(){
				this.isLoading=true
				setTimeout(()=>{
					this.$wxsdk.getFuzzyLocationToAddress().then(res => {
						this.setLocationAddress(res)
						this.isLoading=false
						//console.log(res)
					}).catch(err=>{

						this.isLoading=false
					})
				},1000)

			},
			inputClick() {
				this.focus = true
				// this.showCity = false
			},
			overlayClick() {
				this.keyword = ''
				this.isSearch = false
				this.focus = false
				this.showCity = false
			},
			areaClick() {
				this.focus = true

				this.isSearch = false
				this.showCity = !this.showCity
			},
			provinceClick(item, index) {
				this.provinceIndex = index

			},
			cityClick(item, index) {
				if (this.city.code == item.code) return
				const obj={code:item.code,name:item.name}
				this.setCity(obj)
				this.showCity = false
				this.isSearch = false
				this.$emit('citySelect', obj)
				// this.isLoading = true
			},
			communityClick(item) {
				this.keyword = ''
				this.isSearch = false
				this.setCommunity(item)
				setTimeout(() => {

					this.focus = false
					this.$emit('communitySelect', item)
				}, 80)
			},
			searchClick() {
				this.showCity=false
				this.isSearch = true
				this.isLoading = true
				this.$emit('search', {
					key: this.keyword,
					city: this.city,
				})
			},


		}
	}
</script>
<style lang="scss" scoped>
	.area-community {

		position: relative;
		// padding: 10rpx 12rpx;
		z-index: 101;
		background-color: #fff;

		&.full {
			border-radius: 0 0 10rpx 10rpx;
			overflow: hidden;
		}

		.top-nav {
			padding: 10rpx 12rpx;

			&.full {
				/* #ifdef MP */
				padding-right: 180rpx;
				/* #endif */
			}
		}

		.community {
			// background-color: #c3c3c3;
			// border-radius: 40rpx;
			flex: 1;
			line-height: 50rpx;
			padding-left: 30rpx;
			padding-right: 30rpx;
		}

		.scan {
			height: 50rpx;
			line-height: 50rpx;
			margin-left: 10rpx;
			position: relative;
		}

		.scan::before {
			position: absolute;
			left: 110%;
			transform: translateY(15%);
			// /top: 0;
			width: 4rpx;
			height: 40rpx;
			background-color: rgba(0, 0, 0, 0.15);
			content: '';

		}

		.area {
			padding-left: 20rpx;
			padding-right: 20rpx;

			.area-arrow {
				display: flex;
				transition: 0.3s;
				transform-origin: center;
				transform: rotateZ(0deg);

				&.open {
					transition: 0.3s;
					transform-origin: center;
					transform: rotateZ(180deg);
				}
			}
		}

		.area-content {
			display: flex;
			width: auto;
			flex-direction: row;
			position: relative;
			min-width: 100rpx;
			text-align: center;
			justify-content: center;
		}

		.search {
			text-align: center;
			width: 120rpx;
			height: 50rpx;
			font-size: 22rpx;
			transition: 0.4s;
			border-radius: 40rpx;
			color: #fff;
			line-height: 50rpx;
			display: flex;
			justify-content: center;
		}
	}

	.content {
		display: flex;
		padding: 4rpx;
		border-width: 3rpx;
		border-radius: 80rpx;
		border-style: solid;
		width: 260rpx;
		z-index: 2;
		background: #fff;
		transition: 0.3s;

		&.full {
			width: 100%;


		}

	}

	.title {
		width: 750rpx;
		position: absolute;
		top: 0;
		right: 0;
		text-align: center;
		width: 100%;
		line-height: 80rpx;
		z-index: -1;
		padding-left: 80rpx;
		font-size: 30rpx;
		font-weight: bold;
		letter-spacing: 2rpx;
	}

	.city {
		padding-right: 6rpx;
		font-size: 24rpx;
	}

	.city-box {
		.curr-city {
			font-weight: bold;
			color: #000;
			font-size: 28rpx;
		}

		.cur-location {
			padding: 20rpx;
			color: #777;
			font-size: 20rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding-right: 30rpx;
		}

	}

	.province-city-box {
		display: flex;
		flex-direction: row;
		height: 660rpx;
		overflow: hidden;

		.province {
			height: 100%;
			max-width: 160rpx;
			background-color: #eaeaea;
		}

		.city {
			flex: 1;
			padding: 10rpx 20rpx;
		}


	}

	.province-item-box{
		background-color: #fff;
	}

	.province-item {
		height: 80rpx;
		display: flex;
		align-items: center;
		padding: 20rpx;
		background: #eaeaea;
		color: #777;

		&.active {
			background: #fff;
			color: #000;
			font-weight: bold;
		}

		&.active-prev {
			border-radius: 0 0 20rpx 0;
		}

		&.active-next {
			border-radius: 0 20rpx 0 0;
		}
	}

	.city-item {
		padding: 20rpx 30rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		color: #777;

		&.active {
			color: #000;
			font-weight: bold;
		}
	}

	.community-box {
		height: 700rpx;
		padding: 20rpx 30rpx;
	}

	.host-community {
		padding-top: 30rpx;
	}

	.my-community,
	.host-community {
		color: #777;

		.label {
			font-size: 20rpx;
			padding: 0 0 15rpx 0;
		}

		.community-wrap {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
		}
	}

	.community-name {
		width: 50%;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-items: flex-start;
		padding: 5rpx 10rpx;

		.name-content {
			// border: 1px #eaeaea solid;
			border-width: 1rpx;
			border-style: solid;
			border-radius: 8rpx;
			width: 100%;
			text-align: center;
			padding: 5rpx 10rpx;

			.name {
				font-size: 24rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}


		.addr {
			font-size: 18rpx;
			color: #aaa;
		}
	}

	.scroll-box {
		height: 100%;
	}

	.hot-community-wrap {
		.hot-community-item {
			width: 100%;
			padding: 8rpx 10rpx;
			display: flex;

			.item-image {
				padding: 8rpx;
				border-radius: 8rpx;
				overflow: hidden;
			}

			.item-op {
				width: 100rpx;
				padding: 20rpx 0;

				.btn {
					padding: 4rpx;
					border-width: 1rpx;
					border-radius: 80rpx;
					border-style: solid;
					text-align: center;
					font-size: 20rpx;
					opacity: 0.6;
				}
			}

			.item-content {

				padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;

				.addr {
					font-size: 18rpx;
					color: #aaa;
				}

				.city {
					font-size: 16rpx;
					color: #ccc;
				}
			}
		}
	}

	.loading {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		z-index: 102;
		background: rgba(0, 0, 0, 0.4);

		.sk-roller {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			z-index: 102;
		}
	}

	.search-box-wrap {
		height: 680rpx;
		padding: 0 30rpx 10rpx 30rpx;

		.community-item {
			width: 100%;
			padding: 8rpx 10rpx;
			display: flex;

			.name {
				font-size: 30rpx;
				font-weight: bold;
			}

			.item-image {
				padding: 8rpx;
				border-radius: 8rpx;
				overflow: hidden;
			}

			.item-op {
				width: 100rpx;
				padding: 20rpx 0;

				.btn {
					padding: 4rpx;
					border-width: 1rpx;
					border-radius: 80rpx;
					border-style: solid;
					text-align: center;
					font-size: 20rpx;
					opacity: 0.6;
				}
			}

			.item-content {

				padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;

				.addr {
					font-size: 18rpx;
					color: #aaa;
				}

				.city {
					font-size: 16rpx;
					color: #ccc;
				}
			}
		}
	}
</style>

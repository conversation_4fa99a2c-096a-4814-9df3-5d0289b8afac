<template>
	<view class="community-item">
		<view class="item-image">
			<u-lazy-load v-if="item.imgHead" height="100" width="100" border-radius="4" img-mode="aspectFill"
				@click="$tools.previewImage([item.imgHead],0,800)"
				:image="$tools.showImg(item.imgHead,200)" />
			<u-lazy-load v-else height="100" width="100" border-radius="4" img-mode="aspectFill"
				:image="require('../images/logo.png')" />
		</view>
		<view class="item-content">
			<view class="name">
				{{item.name}}
				<text class="city">({{item.city}})</text>
			</view>
			<view class="addr">{{item.address}}</view>
		</view>
		<view class="item-op text-center" @click="communityClick(item)">
			<view v-if="item.juli" class="text-xs juli text-gray">{{item.juli}}km</view>
			<view class="btn" :style="{color:colors,background:colors+'30'}">选择</view>
		</view>
	</view>
</template>

<script>
	export default {
		options: {  //小程序样式穿透
			styleIsolation: 'shared'
		},
		props: {
			colors: {
				type: String
			},
			item: {
				type: Object
			},

		},
		data() {
			return {
			}
		},
		computed: {

		},
		watch: {

		},
		methods: {
			communityClick(item){
				this.$emit('communitySelect',item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.community-item{
		width: 100%;
		padding: 12rpx 10rpx;
		display: flex;
		.name{
			font-size: 30rpx;
			font-weight: bold;
		}
		.item-image{
			width: 100rpx;
			padding: 8rpx;
			border-radius: 8rpx;
			overflow: hidden;
		}
		.item-op{
			width:100rpx;
			padding: 20rpx 0;

			.juli {
				padding-bottom: 20rpx;
			}
			.btn{
				padding: 4rpx;
				border-width: 1rpx;
				border-radius: 80rpx;
				border-style: solid;
				text-align: center;
				font-size: 26rpx;
			}
		}
		.item-content{

			padding-left: 20rpx;
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			.addr{
				font-size: 18rpx;
				color: #aaa;
			}

			.city{
				font-size: 16rpx;
				color: #ccc;
			}
		}
	}
</style>

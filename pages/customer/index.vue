<template>
	<ut-page>
		<view :style="{height:statusBarHeight+'px'}"></view>
		
		<view v-if="!token" @click="loginExit">
			<view  class="no-login"></view>
			
			<view class="need-login">
				<view class="padding-tb text-lg">你好，此服务仅限先施康养员工才能使用</view>
				<view>点击这里快速登录，并进行账户鉴权</view>
			</view>
		</view>
		<view v-else-if="!userInfo.phone">
			<view class="need-login">
				<view>需要验证您的手机号</view>
				<view class="phone-auth" :style="{color:colors}">
					<phone-wechat :colors="colors"></phone-wechat>
				</view>
			</view>
		</view>
		
		<view v-if="isManager && userInfo.phone"  class="community-box">
			<u-cell-group>
				<u-cell icon="calendar-fill" title="当前公司"></u-cell>
				<u-cell v-if="myCommunity.length>1 || !community || !community.id" :title="community.name" :value="myCommunity.length>1?'请选择':''" @click="show=true"></u-cell>
				<u-cell v-else :title="community.name"></u-cell>
			</u-cell-group>
		</view>
		
		<template v-for="(item,index) in newMenu">
			<view :key="index">
				<view v-if="item.menus.length" class="list bg-white ut-radius margin padding-xs">
					<list-cell>{{item.title}}</list-cell>
					<ut-grid :colors="colors" :list="item.menus" :count="item.menus.length>4?4:item.menus.length" :show-background-color="true"></ut-grid>
				</view>
			</view>
		</template>
		
		<u-popup :show="show" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" height="400" close-icon-pos="top-left" @close="show=false">
			<view class="pop-title">我的公司</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<template v-for="(item,index) in myCommunity">
					<community-item :key="index" :item="item" @communitySelect="communitySelect"></community-item>
				</template>
			</scroll-view>
		</u-popup>

		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import communityItem from '../components/community-item.vue'
import phoneWechat from '@/pages/user/components/phone-wechat.vue'
import {menu} from './menu.js'
const app = getApp()

export default {
	components: {
		communityItem,
		phoneWechat,
	},
	data() {
		return {
			colors: '',
			statusBarHeight:uni.getSystemInfoSync().statusBarHeight,
			isManager:false,
			myCommunity: [],
			show:false,
			menuList:[],
			
		}
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
		if (!this.token) {
			//this.loginExit()
		}
		
		// todo 检查权限
		
		this.getMyCommunity()
		this.getIsManager()
	},
	async onLoad(options) {
		await this.$onLaunched
		
		
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			community: state => state.init.community,
			token: state => state.user.token,
			locationAddress: state => state.init.locationAddress,
			userInfo: state => state.user.info,
		}),
		newMenu(){
			if(!this.menuList) return []
			let arr=[]
			menu.forEach(item=>{
				let sub={}
				sub.title=item.title
				sub.menus=[]
				arr.push(sub)
				item.menus.forEach(item2=>{
					let obj=this.menuList.find(u=>u.code==item2.code)
					if(obj) sub.menus.push({
						code:item2.code,
						name:obj.title,
						url:obj.path?obj.path:item2.url,
						image:obj.icon?obj.icon:item2.image,
						num:obj.num,
						
					})
				})
				
			})
			return arr
		}
	},
	watch: {
		token: {
			handler(val) {
				if (val) {
					this.getMyCommunity()
					this.getIsManager()
				}
			},
		},
		'userInfo.phone':{
			handler(){
				this.getMyCommunity()
				this.getIsManager()
			}
		}
	},
	methods: {
		...mapActions(['loginExit', 'setCommunity']),
		checkLogin() {
			if (!this.token) {
				this.loginExit()
				return true
			}
			return false
		},
		routeTo(path) {
			if (this.checkLogin()) return
			this.$tools.routerTo(`/pagesC/admin/${path}`)
		},
		async getMyCommunity() {
			if (!this.token) return
			if(!this.userInfo || !this.userInfo.phone) return
			uni.showLoading({ title: '请等待...' })
			const { data } = await this.$ut.api('mang/community/my', {
				commKey: this.commKey,
			}).finally(() => uni.hideLoading())
			if (data.info) {
				this.myCommunity = data.info.map(o => ({
					id: o.id,
					name: o.name,
					address: o.address,
					city: o.cityName,
					imgHead: o.imgHead,
				}))
				if(this.myCommunity && this.myCommunity.length==1){
					this.communitySelect(this.myCommunity[0])
				}
			}
		},
		async getIsManager(){
			if (!this.token) return
			const {data} = await this.$ut.api('mang/isManager',{
				commKey:this.commKey,
				baseId:this.community.id,
			})
			this.isManager=data
			if(data)
			{
				 await this.getMyMenu()
				 this.getNurseMenuCount()
			}
		},
		async getMyMenu(){
			if(!this.community.id) return
			const {data} = await this.$ut.api('mang/myMenu',{
				commKey:this.commKey,
				baseId:this.community.id
			})
			this.menuList=data
			
		},
		communitySelect(item) {
			this.show = false
			this.setCommunity(item)
			this.getIsManager()
		},
		async getNurseMenuCount(){
			if(!this.community.id) return
			const {data} = await this.$ut.api('mang/nurse/menuCount',{
				communityId:this.community.id,
				module:'long'
			})
			if(!data) return
			if(data.waitSetAttendant>0){
				let obj=this.menuList.find(u=>u.code=='distribution')
				if(obj) this.$set(obj,'num',data.waitSetAttendant)
			}
		},


	},
}
</script>

<style lang="scss" scoped>


.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;

	height: 800rpx;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}


.list {
	.list-cell:last-child {
		border-bottom: none;
	}
}

.community-box{
	margin: 30rpx;
	background-color: #fff;
	border-radius: 10rpx;
}

.need-login {
	display: flex;
	width: 100%;
	height: 90vh;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	z-index: 1;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	border: 0;
	font-size: 32rpx;
	font-weight: bold;
}

.phone-auth {
	padding: 10rpx;
	border-width: 3rpx;
	border-radius: 80rpx;
	border-style: solid;
	width: 400rpx;
	background: #fff;
	transition: 0.3s;
	margin-top: 50rpx;
	margin-bottom: 50rpx;
	text-align: center;
	line-height: 40rpx;
	font-size: 32rpx;
}

.no-login{
	width:100%;
	height: 100vh;
	background-image: url(https://oss.afjy.net/api/file/preview?file=f5ivL7F);
	opacity: 0.3;
}
</style>

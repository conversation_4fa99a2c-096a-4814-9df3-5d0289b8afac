<template>
	<ut-page class="userbox">
		<view class="userHeader" :style="{background:colors,height:(statusBarHeight+460)+'rpx',paddingTop:statusBarHeight+'px'}">
			<image src="https://oss.afjy.net/api/file/preview?file=eWtsswW.png&width=750" class="user_bg"></image>
			<view class="nav">
				<view class="user" @click="onLogin">
					<view>
						<image :src="userInfo.headimgurl?userInfo.headimgurl:headUrl" v-if="userInfo"
							@click="$tools.previewImage([userInfo.headimgurl?userInfo.headimgurl:headUrl+'&width=800'])">
						</image>
						<image :src="require('../images/logo.png')" v-else @click.stop="onLogin"></image>
					</view>
					<view class="user-info">
						<text>{{userInfo.nickname || '登录'}}</text>
						<view class="phone" v-if="userInfo.phone">
							<text class="iconfont icon-phone"></text>
							<text class="user-tel">{{userInfo.phone}}</text>
						</view>
					</view>
				</view>
				<!-- <view class="setting" @click="jumpUser()"> <text class="iconfont icon-settings"></text></view> -->
			</view>

		</view>
		<view class="userMenu ut-page-bg" @touchmove.stop.prevent="moveHandle" @touchstart="coverTouchStart"
			@touchmove="coverTouchMove" @touchend="coverTouchEnd"
			:style="'transform:' + coverTransform + ';transition:' + coverTransition">
			<image src="https://oss.afjy.net/api/file/preview?file=eWttjyZ.png&width=750" class="arc"></image>
			<!-- 签到 -->
			<view class="info">
				<list-cell>我的订单</list-cell>
				<ut-grid :colors="colors" :list="userMenu" :count="userMenu.length>4?4:userMenu.length"></ut-grid>
				<!-- <user-menu></user-menu> -->
			</view>
			<!-- <sign-in :dataList="dataList"></sign-in> -->
		</view>


		<view class="info">
			<!-- <my-fun :colors="colors" :isEmployee="employeeState.isEmployee" :auditState="employeeState.auditState" /> -->
		</view>


		<view class="info">
			<user-info :colors="colors" @themeChange="themeChange" />
		</view>

		<view class="info">
			<my-menu :colors="colors" :is-staff="isStaff" />
		</view>

		<!-- 		<u-tabbar
			:value="value6"
			@change="name => value6 = name"
			:fixed="true"
			:placeholder="true"
			:safeAreaInsetBottom="true"
		>
			<u-tabbar-item text="首页" icon="home" ></u-tabbar-item>
			<u-tabbar-item text="放映厅" icon="photo" ></u-tabbar-item>
			<u-tabbar-item text="直播" icon="play-right" ></u-tabbar-item>
			<u-tabbar-item text="我的" icon="account" ></u-tabbar-item>
		</u-tabbar> -->

		<phone :value.sync="showPhone" :colors="colors"></phone>
		<ut-login-modal :colors="colors"></ut-login-modal>
		<!-- <loading :show="isShow"></loading> -->
	</ut-page>
</template>

<script>
	let app = getApp();
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex';
	// import websocket from '@/common/websocket'
	import phone from './components/phone.vue'
	import userInfo from '@/pages/user/user-info.vue'
	import myMenu from '@/pages/xp/user/myMenu.vue'
	// import myFun from '@/pages/xp/user/myFun.vue'

	export default {
		components: {
			phone,
			userInfo,
			myMenu,
			// myFun,
		},
		data() {
			return {
				statusBarHeight:uni.getSystemInfoSync().statusBarHeight,
				value6: 0,
				showPhone: false,
				utCont: 0, // 判断是否跳转 测试 登录页面
				utNum: 0,
				scrollTop: 0,
				colors: '',
				// isShow: false,
				startY: 0,
				//原始坐标
				moving: false,
				//当前元素的状态（是否移动）
				coverTransform: 'translateY(0px)',
				//元素的移动
				coverTransition: '0s',
				headUrl: '/static/imgs/ut-logo.png',
				// value: '立即验证',
				headimgurl: '',
				userMenu: [{
						"name": "待支付",
						"icon": "icon-daifukuan",
						"url": "/pagesB/sign/sign",
						"image": "https://oss.afjy.net/api/file/preview?file=eWtp76X",
						"num": 0
					},
					{
						"name": "待服务",
						"icon": "icon-daifukuan",
						"url": "/pagesB/sign/sign",
						"image": "https://oss.afjy.net/api/file/preview?file=eWtqax9",
						"num": 0
					},
					{
						"name": "已完成",
						"icon": "icon-daifukuan",
						"url": "/pagesB/sign/sign",
						"image": "https://oss.afjy.net/api/file/preview?file=eWtrJFk",
						// "num": 3
					},
					{
						"name": "已取消",
						"icon": "icon-daifukuan",
						"url": "/pagesB/sign/sign",
						"image": "https://oss.afjy.net/api/file/preview?file=eWtqZJy",
						// "num": 3
					}
				],
				//检测我的员工状态（是否是员工，是否审核过）
				employeeState: {
					isEmployee: false,
					auditState: 0,
				},
				orderCount: {},
				
				isStaff:false,
			};
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: async function(options) {
			await this.$onLaunched;
		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			this.utCont = 0
			this.utNum = 0
			// this.isShow = true
			let colors = app.globalData.newColor;
			this.setData({
				colors: colors
			});
			this.scrollTop = 0

			if (this.token) {
				this.loadMyInfo()
				// this.checkEmployee()
				this.orderStateCount()
				
				this.getIsAdmin()
			}

			// this.setUser(this.userInfo)
		},
		async onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		computed: {
			...mapState({
				userInfo: state => state.user.info,
				loginTip: state => state.user.loginTip,
				template: state => state.init.template,
				commKey: state => state.init.template.commKey,
				websocket: state => state.websocket.socketTask,
				theme: state => state.init.theme,
				uploadInfo: state => state.init.oss,
				token: state => state.user.token,
			}),
			headImg: {
				set(val) {
					this.headimgurl = val
				},
				get() {
					if (this.headimgurl) return this.headimgurl
					return this.userInfo.headimgurl
				}
			},
		},
		methods: {
			...mapMutations(["setTempMenu"]),
			...mapActions(["socketInit", "socketOn", "setUser",'loginExit']),
			loadMyInfo() {
				this.$ut.api('comm/login/myinfo').then(res => {
					this.setUser(res.data)
				})
			},
			checkEmployee() {
				this.$ut.api('hospital/employee/checkMyIsEmployee', {
					commKey: this.commKey,
				}).then(res => {
					this.employeeState = res.data
				})
			},
			orderStateCount() {
				// this.$ut.api('hospital/order/myStateCount', {
				// 	commKey: this.commKey,
				// }).then(res => {
				// 	//this.orderCount=res.data
				// 	if (res.data) {
				// 		if (this.userMenu) {
				// 			if (this.userMenu.length >= 1) {
				// 				this.userMenu[0].num = res.data.waitPay
				// 			}
				// 			if (this.userMenu.length >= 2) {
				// 				this.userMenu[1].num = res.data.waitService
				// 			}
				// 			if (this.userMenu.length >= 3) {
				// 				this.userMenu[2].num = res.data.over
				// 			}
				// 			if (this.userMenu.length >= 4) {
				// 				this.userMenu[3].num = res.data.cancel
				// 			}
				// 		}
				// 	}
				// })
			},
			prewAvatar() {
				this.$tools.previewImage([this.headImg], 0)
			},
			phoneJump() {
				this.$tools.routerTo("/pages/user/phone")
			},
			themeJump() {
				this.$tools.routerTo("/pagesA/setting/index")
			},
			avatarJump() {
				if (this.userInfo) {
					this.$tools.routerTo("/pagesA/user/uploadAvatar")
				}
			},
			coverTouchStart(e) {
				//滑动开始 记录目标原始坐标
				this.setData({
					startY: e.touches[0].clientY,
					coverTransition: 'transform .2s linear'
				});
			},
			moveHandle() { //阻止下拉默认事件

			},
			coverTouchMove(e) {
				//滑动中
				let moveY = e.touches[0].clientY; //记录移动的坐标
				let startY = this.startY;
				let moveDistance = moveY - startY; //计算当前坐标与原始坐标的差
				if (moveDistance < 0) {
					//如果小于0 不做操作
					this.setData({
						moving: false
					});
					return;
				}
				this.setData({
					moving: true
				});

				if (moveDistance >= 60) {
					//如果差在该区间内
					moveDistance = 60;
				}

				if (moveDistance > 0 && moveDistance <= 60) {
					this.setData({
						coverTransform: `translateY(${moveDistance}px)`
					});
				}
			},
			coverTouchEnd(e) {
				//滑动结束 回到原始位置
				if (this.moving == false) {
					return;
				}
				this.setData({
					coverTransform: 'translateY(0px)',
					moving: false,
					coverTransition: 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)'
				});
			},
			onLogin() {
				if (!this.token) {
					this.loginExit()
					return true
				}
				return false
				// if (this.userInfo) return;
				// uni.navigateTo({
				// 	url: '/pages/login/login'
				// })
			},
			jumpUser() {
				if (this.userInfo) {
					this.$tools.routerTo('/pages/user/userInfo')
				} else {
					this.utNum++
					this.utCont = 0
				}
			},
			//内部测试跳转
			myLogin() {
				if (this.utCont >= 5 && this.utNum >= 5) {
					this.$tools.routerTo('/pages/login/login?test=1')
				}
			},

			themeChange(item) {
				this.setData({
					colors: item.color
				})
			},
			async getIsAdmin(){
				const {data} =await this.$ut.api('mang/isAdmin',{})
				this.isStaff=data
			}
		}
	};
</script>
<style scoped lang="scss">
	.userHeader {
		height: 460upx;
		width: 100vw;
		position: relative;
		padding: 50upx 4%;

		.user_bg {
			height: 100%;
			width: 100%;
			display: block;
			position: absolute;
			top: 0;
			left: 0;
		}
	}

	.info {
		margin: 20rpx 30rpx;
		background-color: #fff;
		border-radius: 8rpx;
		margin-top: 0;
	}




	.userHeader .nav {
		position: relative;
		z-index: 1;
		height: 180upx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.user {
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;
		overflow: hidden;
	}

	.user image {
		flex-shrink: 0;
		width: 130upx;
		height: 130upx;
		border: 5upx solid #fff;
		border-radius: 50%;
	}

	.user text {
		font-size: 38upx;
		color: #fff;
		margin-left: 20upx;
	}

	.user .user-info {
		width: 560rpx;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;


		text {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			width: 100%;
			font-size: 34rpx;
			line-height: 44rpx;
			letter-spacing: 2rpx;
		}

		.phone {
			.icon-phone {
				font-size: 20rpx;
			}

			.user-tel {
				color: #fff;
				font-size: 26rpx;
				margin-left: 10rpx;
			}
		}
	}

	.nav .setting {
		color: #fff;

		.icon-settings {
			font-size: 40rpx;
		}
	}


	.vip {
		height: 240upx;
		position: relative;
		display: flex;
		padding: 10upx 24upx;
		border-radius: 16upx 16upx 0 0;
		width: 100%;
		overflow: hidden;
	}

	.banquan {
		position: absolute;
		top: 120upx;
		left: 24upx;
		font-size: 24upx;
	}

	.vipTime {
		position: absolute;
		color: #f6e5a3;
		top: 60upx;
		left: 90upx;
		font-size: 22upx;
	}

	.vip .vip_bg {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}

	.vipname {
		z-index: 5;
		display: flex;
		width: 100%;
		height: 60upx;
		position: relative;
		align-items: center;
	}

	.vipname .money {
		color: #f6e5a3;
		font-size: 35upx;
		line-height: 60upx;
	}

	.vipname .names {
		color: #f6e5a3;
		line-height: 20upx;
		font-size: 24upx;
		margin-left: 40upx;
	}

	.vip_level {
		width: 115upx;
		height: 35upx;
		text-align: center;
		line-height: 40upx;
		font-size: 20upx;
		color: #333;
		padding: 5rpx;
		background: linear-gradient(270deg, #f9e6af, #ffd465);
		border-radius: 20upx;
		position: absolute;
		top: 12upx;
		right: 10upx;
	}

	.userMenu {
		margin-top: -160upx;
		position: relative;
		padding-bottom: 10rpx;
		z-index: 1;
	}

	.userMenu .arc {
		width: 100%;
		height: 36upx;
		position: absolute;
		top: -34upx;
		left: 0;
	}

	.place {
		padding: 0 4%;
		position: relative;
		background-color: #f8f8f8;
		padding-bottom: 40upx;
	}

	.management {
		padding: 20rpx;
		font-size: 30rpx;
		font-weight: bold;
		background: #fff;
		letter-spacing: 4rpx;
		margin-bottom: 20rpx;

		.iconfont {
			margin-right: 10rpx;
		}
	}

	:deep(.iconBg) {
		display: none;
	}



	:deep(.user-index .center) {

		@keyframes test {
			0% {
				opacity: 0;
				-webkit-transform: translateY(-60rpx);
			}

			60% {
				opacity: 1;
				-webkit-transform: translateY(10rpx);
			}

			80% {
				-webkit-transform: translateY(-5rpx);
			}

			100% {
				-webkit-transform: translateY(0);
			}

			// 0%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(-90deg);opacity:0;}
			// 100%{-webkit-transform-origin:left bottom;-webkit-transform:rotate(0);opacity:1;}
		}

		// transition: all 0.1s ease-in-out 0s;

		width:100% !important;
		height: 100%;
		animation: test linear .3s;

		.custom-title {
			overflow: hidden;
			width: 100%;
			height: 100%;
			background-image: url('https://oss.afjy.net/api/file/preview?file=eWtsswW.png&width=750');
		}

		.user {
			height: 100%;
		}

		.user image {
			margin-left: 40rpx;
			flex-shrink: 0;
			width: 60rpx;
			height: 60rpx;
			border: 5upx solid #fff;
			border-radius: 50%;
		}

		.user .user-info {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;

			text {
				padding-top: 10rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				width: 100%;
				font-size: 24rpx;
				line-height: 30rpx;
				letter-spacing: 2rpx;
			}

			.phone {
				.icon-phone {
					font-size: 14rpx;
					padding-top: 0;
				}

				.user-tel {
					padding-top: 0;
					color: #fff;
					font-size: 20rpx;
					margin-left: 10rpx;
				}
			}
		}
	}

	:deep(.top_nav::after) {
		left: 0;
		margin: 0 30rpx;
	}
</style>

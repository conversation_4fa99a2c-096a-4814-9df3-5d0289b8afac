<template>
	<ut-grid :colors="colors"></ut-grid>
</template>

<script>
    export default {
        data() {
            return {
                list: [
                    {
                        name: 'hourglass',
                        title: '签到',
                    }
                ],
            }
        },
        methods: {
            click(name) {
                this.$refs.uToast.success(`点击了第${name}个`)
            }
        }
    }
</script>

<style lang="scss">
    .grid-text {
        font-size: 14px;
        color: #909399;
        padding: 10rpx 0 20rpx 0rpx;
        /* #ifndef APP-PLUS */
        box-sizing: border-box;
        /* #endif */
    }
</style>
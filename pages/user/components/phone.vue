<template>
	<view class="cu-modal" v-if="value" :class="[{ show: value }]"  cathctouchmove @click="hideModal" @touchmove.stop.prevent="movehandle">
		<view class="cu-dialog" @tap.stop style="background: none;overflow: visible;">
			<view class="modal-box">
				<view class="title">手机号码验证</view>
				<view>
					<view class="flex-item">
						<view class="label">手机号:</view>
						<input class="input" type="number" maxlength="11" v-model="phone" placeholder="请输入手机号码" />
					</view>
					<view class="flex-item">
						<view class="label">验证码:</view>
						<input class="input" type="number" maxlength="4" v-model="code" placeholder="请输入验证码" />
						<view class="btn" v-show="codeShow" @click="$shaken(getCode)">获取验证码</view>
						<view class="btn" v-show="!codeShow">{{count}} 秒</view>
					</view>
				</view>
				<button class="save" style="background:#c3c3c3;" v-if="!showBtn">立即验证</button>
				<button class="save" :style="{'background':colors}" v-else @click="submit">立即验证</button>
			</view>
			
		</view>
	</view>
</template>

<script>
	let app = getApp();
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex'
	import store from '@/common/store/index.js'
	export default {
		options: {
		  // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现(不会出现shadow节点下再去创建元素)
		  virtualHost: true
		},
		props: {
		  value: {
		    type: Boolean,
		    default: false
		  },
		  colors:{
			  type:String,
			  default:'',
		  },
		},
		data() {
			return {
				noClick: true,
				phone: "",
				code: "",
				codeShow: true,
				count: '',
				timer: null,
			}
		},
		computed: {
			...mapState({
				userInfo: state => state.user.info,
				refresh: state => state.user.refresh,
				token: state => state.user.token,
			}),
			
			showBtn(){
				if(!this.phone) return false
				if(!this.code) return false
				return true
			}
		},
		watch:{
	
		},
		onLoad:function(){
			if(this.userInfo){
				this.phone = this.userInfo.phone
			}
		},
		onShow: function() {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		methods: {
			...mapActions(['setToken','setUser','loginExit']),
			movehandle(){
			},
			hideModal() {
				// console.log('ccccc')
				this.$emit('update:value', false)
			},
			validatePhoneNumber(str) {
			  const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
			  return reg.test(str)
			},
			getCode() {
				
				if(!this.phone) return
				if(!this.validatePhoneNumber(this.phone)) return
				if(!this.codeShow) return 
				
				//查看登录状态
				if(!this.token){
					store.commit('LOGIN_TIP', true);
					this.$emit('changes', false)
					return
				}
				
				this.codeShow = false
				this.count = 60;
				this.timer = setInterval(() => {
					this.count--
					if(this.count<=0){
						this.codeShow = true;
						clearInterval(this.timer);
						this.timer = null;
					}
				}, 1000)

				this.$ut.api('comm/sms/sendValidCode',{phone:this.phone}).then(res=>{})
			},
			submit(){
				let that = this
				
				uni.showLoading({
					title:"请稍后..."
				})
				this.$ut.api('comm/sms/validCode',{phone:this.phone,code:this.code}).then(res=>{
					this.$ut.api('comm/login/refresh',{ 
						refresh_token: this.refresh
					}).then(res => {
						that.setToken(res.data)
						that.$ut.api('comm/login/myinfo').then(res2 => {
							uni.hideLoading()
							that.setUser(res2.data)
							uni.hideLoading()
							this.hideModal()
						})
					}).catch(err => {
						uni.hideLoading()
					})
				}).catch(e=>{
					uni.hideLoading()
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	


	.modal-box {
		width: 610rpx;
		border-radius: 20rpx;
		background: #fff;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		padding: 30rpx;
		
		.title{
			font-weight: bold;
			font-size: 36rpx;
			padding: 10rpx;
			padding-bottom: 30rpx;
		}
	}

	.flex-item {
		display: flex;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 2rpx solid #eee;
		position: relative;
	}

	.flex-item:last-child {
		border-bottom: none;
	}

	.flex-item .label {
		width: 120rpx;
		font-size: 26rpx;
		font-weight: bold;
		display: flex;
		justify-content: end;
	}

	.flex-item .input {
		padding-left: 20rpx;
		font-size: 30rpx;
	}

	.flex-item .btn {
		position: absolute;
		right: 20rpx;
		font-size: 20rpx;
	}

	.save {
		background: #030303;
		border: none;
		color: #ffffff;
		margin-top: 40rpx;
		font-size: 28rpx;
		width:80%;
		letter-spacing: 6rpx;
	}
</style>

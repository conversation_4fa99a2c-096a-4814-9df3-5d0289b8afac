<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<button :style="{color:colors}" class="btn-get-phone" open-type="getPhoneNumber" :disabled="btnDisabled" @getphonenumber="getPhoneNumber">{{title}}</button>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<view @click="getPhoneNumber">{{title}}</view>
		<!-- #endif -->
		<phone :value.sync="showPhone" :colors="colors"></phone>
	</view>
</template>

<script>
	let app = getApp();
	import {mapMutations, mapActions, mapState} from 'vuex'
	import phone from './phone.vue'
	export default {
		components: {
			phone,
		},
		props: {
		  colors: {
			type: String
		  },
		  title:{
			  type:String,
			  default:'获取手机号'
		  }
		},
		computed: {
			...mapState({
				userInfo: state => state.user.info,
				commKey: state => state.init.template.commKey,
				refresh:state => state.user.refresh,
				token: state => state.user.token
			}),
		},
		data() {
			return {
				showPhone:false,
				btnDisabled:false,
			}
		},
		watch:{

		},
		methods: {
			 ...mapActions(['setToken', 'loginExit','setUser']),
			
			getPhoneNumber(e) {
				wx.reportEvent("getphonenumber", e)
				this.btnDisabled=true
				
				// #ifdef MP
				if(e.target.errMsg=='getPhoneNumber:fail user deny'){
					this.btnDisabled=false
					this.showPhone = true
					return
				}
				
				const code=e.detail.code
		
				uni.showLoading({
					title:'请稍等...'
				})
				
				this.$ut.api('comm/user/updateMyPhone',{commKey:this.commKey,code:code}).then(res=>{
					this.$ut.api('comm/login/refresh',{
						refresh_token: this.refresh
					}).then(res => {
						this.setToken(res.data)
						this.$ut.api('comm/login/myinfo').then(res2 => {
							this.setUser(res2.data)
							uni.hideLoading()
						})
						this.btnDisabled=false						
					}).catch(err => {
						this.btnDisabled=false
						uni.hideLoading()
					})
	
				}).catch(res=>{
					this.btnDisabled=false
					uni.hideLoading()
				})
				// #endif
				
			},	
		},
	}
</script>

<style lang="scss" scoped>
	.btn-get-phone{
		line-height: 1;
		font-size: 32rpx;
		background-color: transparent;
		border: 1px solid #fff;
		
	}
	.btn-get-phone::after{
		border: none;
		padding: 0;
		box-sizing:initial;
		border-radius: 0;
	}
</style>
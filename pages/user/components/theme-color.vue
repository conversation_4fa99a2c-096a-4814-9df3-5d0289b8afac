<template>
	<view :class="'cu-modal ' + (value == true?'show':'')" v-if="value" @click="hideModal" @touchmove.stop.prevent="movehandle">
		<view class="cu-dialog">
			<view class="cu-bar justify-end solid-bottom">
				<view class="content">选择颜色</view>
				<view class="action" @tap="closeColor">
					<text class="cuIcon-close text-red"></text>
				</view>
			</view>
			<view class="grid col-5">
				<view v-for="(item, index) in themeList" :key="index" class="padding-xs" @tap="SetColor(item)">
					<view :class="'padding-tb radius bg-' + item.name" style="color:#ffffff">
						{{ item.title }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	import {mapMutations,mapActions,mapState} from 'vuex'
	import themes from "./themes.js";
	export default {
		props: {
		  value: {
		    type: Boolean,
		    default: false
		  },
		  colors:{
			  type:String,
			  default:'',
		  },
		},
		data() {
			return {
				themeList: themes
			};
		},

		computed:{
			...mapState({
				theme:state=>state.init.theme,
				token: state => state.user.token,
			}),
		},


		methods: {
			...mapActions(['setConfig']),
			movehandle(){
			},
			hideModal() {
				this.$emit('update:value', false)
			},
			showColorModal() {
				this.setData({
					value: true
				});
			},

			closeColor() {
				this.$emit('update:value', false)
			},

			SetColor(item) {
				this.setConfig(item)
				app.globalData.newColor = item.color;
				uni.setTabBarStyle({
					selectedColor: item.color
				})
				let tabList = item.tabList;
				if (item.tabList) {
					for (var i = 0; i < tabList.length; i++) {
						let img = tabList[i];
						uni.setTabBarItem({
							//设置tabBar 首页图标
							index: i,
							selectedIconPath: img
						});
					}
				}
				this.$emit('update:value', false)
				this.$emit('change',item)
			},
			
		}
	};
</script>

<style scoped lang="scss">
	
	.cu-dialog{
		animation: ut-popIn  .3s;
	}
	.grid{
		padding-top: 20rpx;
		padding: 16rpx;
	}
</style>
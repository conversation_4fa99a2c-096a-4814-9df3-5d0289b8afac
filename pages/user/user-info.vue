<template>
	<view class="user-info">
		<list-cell :rightshow="true" @click.native="showHeaderImg()" :colors="colors">
			<template v-slot:icon><text class="iconfont icon-zhanghu"/></template>
			修改头像
			<template v-slot:right></template>
		</list-cell>
		<list-cell :rightshow="showPhoneMore" @click.native="getPhoneClick" :colors="colors">
			<template v-slot:icon><text class="iconfont icon-phone"/></template>
			<view class="phone-box">
				<text>手机号</text>
				<!-- #ifdef MP -->
				<button v-if="!userInfo.phone && userInfo" class="btn-get-phone" :disabled="btnDisabled" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">获取手机号</button>
				<view v-else class="phone-view">{{userInfo.phone}}</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view class="phone-view">{{userInfo.phone}}</view>
					<!-- <text style="margin-left: 80rpx;font-size: 13px;" @click.stop="showPhone=true" >{{ userInfo.phone || '立即验证'}}</text> -->
					
				<!-- #endif -->
			</view>
			<template v-slot:right>
				<!-- #ifdef MP -->
				<button v-if="userInfo.phone" class="btn-edit-phone" :disabled="btnDisabled" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">修改</button>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<text v-if="userInfo.phone" @click="showPhone=true">修改</text>
				<!-- #endif -->
			</template>
		</list-cell>
		<!-- {{tt}} -->
		<list-cell :rightshow="false" @click.native="showThemeColor=true" :colors="colors" :border-bottom="0">
			<template v-slot:icon><text class="cuIcon-colorlens"/></template>
			<view>
				主题色
			</view>
		</list-cell>
			
		<wx-user-info-modal v-model="showAuthorizationModal" :detail="userInfo"
			@updated="updatedUserInfoEvent"></wx-user-info-modal>
		<phone :value.sync="showPhone" :colors="colors"></phone>
		<theme-color :value.sync="showThemeColor" :colors="colors" @change="onChange"/>
	</view>
</template>

<script>
let app = getApp();
import {mapMutations, mapActions, mapState} from 'vuex'

import WxUserInfoModal from './components/wx-user-info.vue'
import phone from './components/phone.vue'
import themeColor from './components/theme-color.vue'
export default {
	components: {
		WxUserInfoModal,
		phone,
		themeColor,
	},
	props: {
	  colors: {
		type: String
	  },
	},
	computed: {
		...mapState({
			theme:state=>state.init.theme,
			userInfo: state => state.user.info,
			uploadInfo: state => state.init.oss,
			commKey: state => state.init.template.commKey,
			refresh:state => state.user.refresh,
			token: state => state.user.token,
		}),
		showPhoneMore(){
			// #ifdef H5
			return true
			// #endif
			// #ifdef MP
			if(this.userInfo.phone) return true
			// #endif
			return false
		}
	},
	data() {
		return {
			showAuthorizationModal: false,
			showPhone:false,
			showThemeColor:false,
			value: '立即验证',
			tt:'',
			btnDisabled:false,
		}
	},
	watch:{
		showThemeColor:{
			handler(newVal){
				// #ifdef MP				
				if(newVal) {uni.hideTabBar()}else{uni.showTabBar()}
				// #endif
			}
		},
		showAuthorizationModal:{
			handler(newVal){
				// #ifdef MP
				if(newVal) {uni.hideTabBar()}else{uni.showTabBar()}
				// #endif
			}
		},
		showPhone:{
			handler(newVal){
				// #ifdef MP
				if(newVal) {uni.hideTabBar()}else{uni.showTabBar()}
				// #endif
			}
		}
	},
	methods: {
		 ...mapActions(['setToken', 'loginExit','setUser']),
		showHeaderImg(){
			if(!this.token){
				this.loginExit()
				return
			}
			this.showAuthorizationModal = true
		},
		upload(filePath){
			this.$tools.uploadImage(
				this.uploadInfo.url, {
					Token: this.uploadInfo.token
				},
				filePath
			).then(res => {
				this.uploadSuccess(res)

			}).catch(err => {
				this.uploadError(err)
			})
		},
		headImgNicknameSave(userInfo){
			uni.showToast({
				title:'保存成功'
			})

			this.userInfo.headimgurl= userInfo.headimgurl
			this.userInfo.nickname=userInfo.nickname
			this.setUser(this.userInfo)
		},
		
		async saveHeadImg(path){
			const upData = await this.$tools.uploadImage(
				this.uploadInfo.server+this.uploadInfo.single, 
				{
					Token: this.uploadInfo.token
				},
				path
			)
			if(upData.length>0) return this.uploadInfo.preview +'?file='+ upData[0].name
		},
		async updatedUserInfoEvent(userInfo,state) {
			this.showAuthorizationModal = false
			let isEdit = false
			let imgUrl=this.userInfo.headimgurl
			let nickName=this.userInfo.nickname
			if(userInfo.headimgurl!=this.userInfo.headimgurl){
				imgUrl=await this.saveHeadImg(userInfo.headimgurl)
				isEdit=true
			}
			if(userInfo.nickname!=this.userInfo.nickname){
				nickName=userInfo.nickname
				isEdit=true
			}
			// this.tt=imgUrl
			if(!isEdit) return
			uni.showLoading({
				title:'请稍等...'
			})
			this.$ut.api('comm/user/updateMyHeadAndName',{imgHead:imgUrl,nickName:nickName}).then(res=>{
				uni.hideLoading()
				
				this.userInfo.headimgurl= imgUrl
				this.userInfo.nickname=nickName
				this.setUser(this.userInfo)
				
				//提交保存电话
			}).catch(res=>{
				uni.hideLoading()
			})
						
			
		},
		getPhoneNumber(e) {
			wx.reportEvent("getphonenumber", e)
			this.btnDisabled=true
			// #ifdef MP
			if(e.target.errMsg=='getPhoneNumber:fail user deny'){
				//如果用户拒绝授权，弹出短信窗体
				this.btnDisabled=false
				this.showPhone = true
				return
			}
			
			const code=e.detail.code
			uni.showLoading({
				title:'请稍等...'
			})
			this.$ut.api('comm/user/updateMyPhone',{commKey:this.commKey,code:code}).then(()=>{
				this.$ut.api('comm/login/refresh',{
					refresh_token: this.refresh
				}).then(res => {
					this.setToken(res.data)
					this.$ut.api('comm/login/myinfo').then(res2 => {
						// console.log(res2.data)
						this.setUser(res2.data)
						uni.hideLoading()
					})
					this.btnDisabled=false
				}).catch(err => {
					this.btnDisabled=false
					uni.hideLoading()
				})
				
				//提交保存电话
			}).catch(res=>{
				this.btnDisabled=false
				uni.hideLoading()
			})
			// #endif
			
		},	
		
		getPhoneClick(){
			if(!this.token){
				this.loginExit()
				return
			}
			// #ifdef H5
			if(!this.userInfo.phone) this.showPhone = true
			// #endif
		},
		onChange(item){
			this.$emit('themeChange',item)
		},
			
			exitSys(){
				//退出登录
				let that = this;
				uni.showModal({
					cancelColor: '#999',
					content: '确认要退出登录吗?',
					confirmColor: this.theme.color,
					success: function(res) {
						if (res.confirm) {
							that.loginExit()
							uni.showToast({
								title: '退出成功',
								icon: 'none'
							});
							setTimeout(() => {
								uni.redirectTo({
									url: '/pages/index/index'
								});
							 }, 1000);
						}
					}
				});
			},
		},
	}
</script>

<style lang="less">
	
	.phone-box{
		display: flex;
		align-items: center;
		position: relative;
		align-content: center;
		flex-direction: row;
		
		.phone-view{
			flex: 1;
			text-align: center;
		}
	}
	
	.btn-get-phone{
		height: 60rpx;
		line-height: 60rpx;
		font-size: 32rpx;
	}
	.btn-edit-phone,.btn-edit-phone::after{
		height: 60rpx;
		line-height: 60rpx;
		border: none;
		padding: 0;
		font-size: 24rpx;
		background: none;
	}
	
	.user-info .list-cell:last-child::after{
		border-bottom: none;
	}

	
</style>

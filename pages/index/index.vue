<template>
	<ut-page class="main-page">
		<ut-top class="top" bg-color="#fff" @topHeight="topHeight">
			<area-community ref="areaCommunity" :colors="colors" :my="myCommunity" :host="hotCommunity"
							:list="hotCommunity" :title="title" :searchData="hotCommunity" :city="city" :province="province"
							@scan="scan" @citySelect="citySelect" @communitySelect="communitySelect" @search="search">
			</area-community>
		</ut-top>
		<view class="page-item">
			<view class="swiper">
				<ut-banner-der image-name="img" bg-name="backgroundImg" :height="360"
							   :datail="swiperData"></ut-banner-der>
			</view>
		</view>
		<view class="page-item main-menu">
			<ut-menu :list="menu" :row-count="5" :image-size="88" @check="menuCheck"></ut-menu>
		</view>
		<view class="page-item margin-top-min">
			<image src="https://oss.afjy.net/api/file/preview?file=f5ivL7F" width="100%" style="width: 100%;" mode="widthFix"/>
		</view>
	</ut-page>
</template>
<script>
	let app = getApp()
	import  {mapMutations, mapActions, mapState}  from 'vuex'
	import { APP_TITLE } from '@/config'
	import areaCommunity from '../components/area-community.vue'
	export default {
		components: {
			areaCommunity
		},
		options: {  //小程序样式穿透
			styleIsolation: 'shared'
		},
		data() {
			return {
				firstShow: true,
				colors: '',
				showLoading: true,
				h: 0,
				s: 0,
				title: APP_TITLE,
				swiperData: [],
				city:{},
				cityData: {},
				province: [],
				hotCommunity: [],
				myCommunity: [],
				searchCommunity: [],
				menu: [],
				notice: [],
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				locationAddress: state => state.init.locationAddress,
			}),
		},
		onLoad: async function(options) {
			await this.$onLaunched
	
			this.pageLoad()
			this.getLocation()
		},
		onPullDownRefresh() {
			uni.hideLoading()
			this.pageLoad()
	
			setTimeout(function () {
				uni.stopPullDownRefresh()
			}, 1000)
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor,
			})
			if (!this.firstShow) {
				this.loadData()
			} else {
				this.firstShow = false
			}
		},
		async onPageScroll(e) {

		},
		methods: {
			...mapActions(['setLocationAddress', 'setCity']),
			pageLoad() {
				this.getMenu()
				this.getSwiper()
	// 			this.getArea()
	
	// 			this.getMyCommunity()
			},
			loadData() {
				//this.getNearCommunity('')
	
				//this.getBulletin()
			},
			loadMyData() {
				//this.getMyCommunity()
			},
			
			topHeight(h, s) {
				this.h = h
				this.s = s
			},
			scan(res) {
				if (res.type == 'WX_CODE') {
					if (res.path) {
						uni.navigateTo({
							url: decodeURIComponent(res.path),
						})
					}
					return
				}
			},
			citySelect(data) {
				this.loadData()
				console.log(data)
				// this.test()
	
			},
			communitySelect(community) {
				this.loadData()
			},
			async getSwiper() {
				const {data} =await this.$ut.api('setting/swipper', { commKey: this.commKey })
				this.swiperData = data
				this.showLoading = false	
			},
			async getMenu() {
				const {data} = await this.$ut.api('setting/indexMenu', { commKey: this.commKey })
				
				this.menu = data.map(o => ({
					image: o.imgHead,
					title: o.title,
					path: o.url,
					cmd: o.cmdCode,
					checkLogin: o.checkLogin,
					checkPhone: o.checkPhone,
					checkCode: o.checkCode,
				}))
			},
			getLocation() {
				if(this.locationAddress && this.locationAddress.location && this.locationAddress.location.longitude ) return
				this.$wxsdk.getLocationToAddress().then(res => {
					this.setLocationAddress(res)
					
					if (res.info) {
						this.setCity(res.info.city)
						this.loadData()
					}else{
						this.loadData()
					}
				}).catch(err => {
					this.loadData()
				})
			},
		},
	}
</script>
<style lang="scss" scoped>
	.top {
		background-color: #fff;
	}
	
	.page-item {
		padding: 0 12rpx;
		background-color: #fff;
	}
	
	.swiper {
		overflow: hidden;
		border-radius: 16rpx;
		background-color: #fff;
	}
	
	.main-menu {
		padding-top: 40rpx;
		padding-bottom: 40rpx;
	}
	
	.main-community {
		position: sticky;
		z-index: 1;
		//  margin-top: -40rpx;
		// padding: 0 12rpx;
	}
	
	
	.main-fixed {
		height: 40rpx;
	}
	
	.community-body {
		padding: 20rpx 16rpx;
	}
</style>

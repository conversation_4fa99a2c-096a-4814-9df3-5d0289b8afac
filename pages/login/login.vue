<template>
	<view class="login login-back" :style="'background: url(' + bgImg[imgIndex] + ');'">
		<view class="logo">
			<image src="../images/logo.png"></image>
		</view>
		<view v-if="showLogin || test">
			<ut-tabs :tabsData="tabsData" @change="choose" :bjColor="'transparent'" :fontColor="'#172172'"
				:lineColor="'#172172'"></ut-tabs>
			<view class="login_from" v-if="tabIndex ==1">
				<input placeholder="请输入账号" v-model="tel" type="number" maxlength="11"
					placeholder-style="color: #333"></input>

				<input placeholder="请输入密码" v-model="password" password placeholder-style="color: #333"></input>

				<view class="login_btn" @click="onlogin">登录</view>
			</view>
			<view class="login_from" v-if="tabIndex ==2">
				<input placeholder="请输入手机号" v-model="tel" type="number" maxlength="11"
					placeholder-style="color: #333"></input>
				<view class="codes">
					<input placeholder="请输入短信验证码" v-model="smscode" maxlength="6" type="number"
						placeholder-style="color: #333"></input>
					<view @click="getCode" :style="{opacity: isCode == true ? '1':'0.8'}">{{codeName}}</view>
				</view>
				<view class="login_btn">登录</view>
			</view>

			<view class="wxLogin" v-if="showButton_weixin">
				<view>—— 快速登录 ——</view>
				<image src="https://oss.afjy.net/api/file/preview?file=eWtureU.png&width=150"></image>
				<!-- #ifdef MP -->
				<button open-type="getUserInfo" @getuserinfo="getUserInfo"></button>
				<!-- #endif -->
				<!-- #ifndef MP -->
				<button open-type="getUserInfo" @click="onAuthorize"></button>
				<!-- #endif -->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		setUserInfo,
		setToken
	} from "@/common/utils/auth";
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex';
	import $platform from "@/common/platform";
	export default {
		components: {
		},
		data() {
			return {
				isCanUse: uni.getStorageSync('isCanUse'),
				nickName: '',
				avatarUrl: '',
				test: false,
				bgImg: [
					'https://oss.afjy.net/api/file/preview?file=eWtvyLa',
					'https://oss.afjy.net/api/file/preview?file=eWtwaOY',
				],
				imgTime: '',
				imgIndex: 0,
				codeName: '验证码',
				isCode: true,
				tel: '',
				smscode: '',
				password: '',
				tabsData: [{
					name: '账号密码',
					value: 1
				}, {
					name: '验证码',
					value: 2
				}],
				tabIndex: 1,
				OAuth:'',
			};
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: async function(options) {
			await this.$onLaunched;

			let platform=$platform.get()
			
			switch(platform){
				case "wxOfficialAccount":
					if (options.test != undefined && options.id != '') {
						this.test = true;
					} else {
						this.wxGZHLogin();
					}
					break;
				case "App":
					this.getAppOAuth();
					break;
				case "wxMiniProgram":
					this.onAuthorize();
					break;
			}
			
			// if ($platform.get() === "wxOfficialAccount") {
			// 	if (options.test != undefined && options.id != '') {
			// 		this.test = true
			// 	} else {
			// 		this.wxGZHLogin();
			// 	}
			// }

		},
		computed: {
			...mapState({
				loginTip: state => state.user.loginTip,
				commKey: state => state.init.template.commKey,
			}),
			showLogin() {
				if ($platform.get() === "wxOfficialAccount") return false;
				return true;
			},
			showButton_weixin(){
				if(!this.OAuth) return false
				if(~this.OAuth.indexOf('weixin')) return true
				return false
			}
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			this.setbImg(); //动态切换背景
		},

		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {
			clearInterval(this.imgTime);
		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {
			clearInterval(this.imgTime);
		},

		methods: {
			...mapActions(['setToken', 'setUser']),
			wxGZHLogin() {
				let local = window.location.href
				let code = this.getUrlCode().code
				let commKey=this.getUrlCode().commKey
				let lastPage = uni.getStorageSync('lastPage')
				uni.showLoading({
					title: '登录中...'
				})
				this.$ut.api('wechatlogin', {
					code: code,
					commKey:commKey
				}).then(res => {
					this.setToken(res.data);
					setTimeout(() => {}, 500)
					this.$ut.api('myInfo').then(res2 => {
						this.setUser(res2.data)
						setTimeout(() => {
							uni.hideLoading()
							uni.showToast({
								title: '登陆成功'
							})
						}, 500)
						setTimeout(() => {

							if (lastPage != local) {
								// uni.showToast({
								// 	title:lastPage
								// })
								// if (lastPage == '/pages/user/index' || lastPage =='/pages/order/order') {
								// 	uni.switchTab({
								// 		url: lastPage
								// 	})
								// } else {
								// 	uni.redirectTo({
								// 		url: lastPage
								// 	})
								// }
								 window.location = lastPage;
								//uni.navigateBack({})
								//window.location = lastPage;
							} else {
								uni.switchTab({
									url: '/pages/index/index'
								})
							}
						}, 1000)
					})
					//if(lastPage!=local)window.location = lastPage;
				})
			},
			getUrlCode() {
				// 截取url中的code方法
				var url = location.search;
				// this.winUrl = url;
				var theRequest = new Object();
				if (url.indexOf('?') != -1) {
					var str = url.substr(1);
					var strs = str.split('&');
					for (var i = 0; i < strs.length; i++) {
						theRequest[strs[i].split('=')[0]] = strs[i].split('=')[1];
					}
				}
				return theRequest;
			},
			getUserInfo() {
				console.log('点了');
				let _this = this;
				uni.getUserInfo({
					provider: 'weixin',
					success: function(infoRes) {
						console.log(infoRes);
						_this.setData({
							nickName: infoRes.userInfo.nickName,
							//昵称
							avatarUrl: infoRes.userInfo.avatarUrl //头像
						});
						let date = new Date().getTime()
						setToken(date) //模拟存储token
						setUserInfo(infoRes.userInfo); //模拟存储用户信息
						try {
							uni.setStorageSync('isCanUse', 1); //记录是否第一次授权  false:表示不是第一次授权
							// uni.switchTab({
							//   url: '/pages/views/tabBar/home'
							// });
						} catch (e) {
							console.log('缓存失败');
						}
					},

					fail(res) {}

				});
			},

			setbImg() {
				clearInterval(this.imgTime);
				let that = this;
				let imgTime = setInterval(() => {
					let imgIndex = that.imgIndex + 1;
					if (imgIndex >= that.bgImg.length) {
						imgIndex = 0;
					}
					that.setData({
						imgIndex: imgIndex
					});
				}, 10000);
				this.setData({
					imgTime: imgTime
				});
			},
			onlogin() { //登录 模拟存储token
				if (this.tel.length == 0) {
					uni.showToast({
						title: '账号不能为空',
						icon: 'none'
					})
					return
				}
				if (this.password.length == 0) {
					uni.showToast({
						title: '密码不能为空',
						icon: 'none'
					})
					return
				}
				uni.showLoading({
					title: '登录中...'
				})
				this.$ut.api('comm/login/account', {
					account: this.tel,
					password: this.password
				}).then(res => {
					console.log(res)
					if (res.data.expires_in == 0) {
						uni.showToast({
							title: '登陆失败',
							icon: "error"
						})
						return
					}
					this.setToken(res.data);
					setTimeout(() => {}, 500)

					this.$ut.api('comm/login/myinfo').then(res2 => {
						this.setUser(res2.data)
						setTimeout(() => {
							uni.hideLoading()
							uni.showToast({
								title: '登陆成功'
							})
						}, 500)
						// #ifdef APP-PLUS
						uni.switchTab({
							url: '/pages/index/index'
						})
						// #endif
						// #ifndef APP-PLUS
						setTimeout(() => {
							let lastPage = uni.getStorageSync('lastPage');
							console.log(lastPage.fullPath)
							uni.redirectTo({
								url: lastPage.fullPath
							})
							//uni.navigateBack(-1)
						}, 1000)
						// #endif
					})

				})

			},
			getCode() { //获取用户短信验证码
				if (this.isCode == false) {
					return
				}
				if (this.tel == '') {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}
				if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(this.tel)) {
					uni.showToast({
						title: '请填写正确手机号码',
						icon: 'none'
					});
					return false;
				}
				this.getPhoneCode()
			},
			getPhoneCode() {
				let timer = ''
				let date = 120
				let that = this
				if (that.isCode == true) {
					uni.showToast({
						title: '验证码发送成功~',
						icon: 'none'
					})
					clearInterval(timer)
					setInterval(() => {
						if (date >= 1) {
							date--
							that.codeName = date + '秒重试'
							that.isCode = false
						} else {
							that.isCode = true
							that.codeName = '验证码'
							clearInterval(timer)
						}
					}, 1000)
				}
			},
			choose(index, item) {
				this.tabIndex = item.value
			},
			// wxlogin() {
			// 	// 1.wx获取登录用户code
			// 	uni.login({
			// 		provider: 'weixin',
			// 		success: function(loginRes) {
			// 			console.log('这是用户的code', loginRes);
			// 		}
			// 	});
			// },
			getAppOAuth(){
				let _this=this
				uni.getProvider({
					service: 'oauth',
					success: function(res) {
						_this.OAuth=res.provider
					},
				});
			},
			onAuthorize() { //微信公众号授权登录
				let _this=this
				uni.showLoading({
					title:'正在登录...'
				})
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						// console.log('-------获取openid(unionid)-----');
						// console.log(JSON.stringify(loginRes));
						// 获取用户信息
						uni.getUserInfo({
							provider: 'weixin',
							success: function(infoRes) {
								// console.log('-------获取微信用户所有-----');
								// console.log(JSON.stringify(infoRes.userInfo));
								_this.$ut.api('comm/login/app', {
									commKey:_this.commKey,
									openId: infoRes.userInfo.openId,
									unionId:infoRes.userInfo.unionId,
									nickName:infoRes.userInfo.nickName,
									avatarUrl:infoRes.userInfo.avatarUrl
								}).then(res => {
									_this.setToken(res.data);
									setTimeout(() => {}, 500)
									_this.$ut.api('myInfo').then(res2 => {
										console.log(res2)
										_this.setUser(res2.data)
										setTimeout(() => {
											uni.hideLoading()
											uni.showToast({
												title: '登陆成功'
											})
										}, 500)
										setTimeout(() => {
											uni.switchTab({
												url: '/pages/index/index'
											})
						
										}, 1000)
									})
									//if(lastPage!=local)window.location = lastPage;
								})
							}
						});
					}
				});
	
				// uni.getUserProfile({
				// 	desc: "获取用户基本资料",
				// 	lang: 'zh_CN',
				// 	success: (user) => {
				// 		console.log(user)
						// uni.login({
						// 	provider: 'weixin',
						// 	success: function(loginRes) {
						// 		console.log('aaaa',loginRes)
						// 		// 获取用户信息
						// 		uni.getUserInfo({
						// 			provider: 'weixin',
						// 			success: function(infoRes) {
						// 				console.log(infoRes)
						// 				//_this.other_login(loginRes, infoRes, 'wx');
						// 			}
						// 		});
						// 	}
						// });
				// 	},
				// })

			},
			//授权登录
			other_login(loginRes, infoRes, type) {
				console.log(loginRes,infoRes,type)
				let _this = this;
				let authApi;
				let pram = {};
				// _this.loginRes=JSON.stringify(loginRes).toString();
				// _this.infoRes=JSON.stringify(infoRes).toString();
				switch (type) {
					case 'qq':
						authApi = 'qqLogin';
						pram = {
							'openid': loginRes.authResult.openid,
							'nickname': infoRes.userInfo.nickname,
							'gender': infoRes.userInfo.gender,
							'province': infoRes.userInfo.province,
							'city': infoRes.userInfo.city,
							'figureurl': infoRes.userInfo.figureurl_qq
						}
						break;
					case 'wx':
						this.infoRes=infoRes
						authApi = 'mpLogin';
						pram = {
							'openid': loginRes.authResult.openid,
							'nickname': infoRes.userInfo.nickName,
							'sex': infoRes.userInfo.gender,
							'province': infoRes.userInfo.province,
							'city': infoRes.userInfo.city,
							'country': infoRes.userInfo.country,
							'headimgurl': infoRes.userInfo.avatarUrl,
							'unionid': loginRes.authResult.unionid
						}
						break;
					default:
				}
				
				this.$ut.api(authApi, pram).then(res => {
					this.setToken(res.data);
					setTimeout(() => {}, 500)
					this.$ut.api('myInfo').then(res2 => {
						this.setUser(res2.data)
						setTimeout(() => {
							uni.hideLoading()
							uni.showToast({
								title: '登陆成功'
							})
						}, 500)
						setTimeout(() => {
				
							if (lastPage != local) {
								// uni.showToast({
								// 	title:lastPage
								// })
								// if (lastPage == '/pages/user/index' || lastPage =='/pages/order/order') {
								// 	uni.switchTab({
								// 		url: lastPage
								// 	})
								// } else {
								// 	uni.redirectTo({
								// 		url: lastPage
								// 	})
								// }
								 window.location = lastPage;
								//uni.navigateBack({})
								//window.location = lastPage;
							} else {
								uni.switchTab({
									url: '/pages/index/index'
								})
							}
						}, 1000)
					})
					//if(lastPage!=local)window.location = lastPage;
				})
				
				
			
			},
		}
	};
</script>
<style scoped lang="scss">
	.login {
		height: 100%;
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		transition: all 0.6s ease-in-out;
		background-color: #333;
	}

	.login-back {
		background-size: 100% 100% !important;
		background-repeat: no-repeat !important;
		background-position: center !important;
	}

	.logo image {
		height: 160upx;
		width: 160upx;
		display: block;
		border-radius: 50%;
		margin: 0 auto;
		margin-top: 150upx;
	}

	.login_from {
		width: 80vw;
		margin: 0 auto;
		margin-top: 3vh;
	}

	.login_from .codes {
		display: flex;
		align-items: center;
		align-content: center;
		justify-content: space-between;
	}

	.login_from input {
		height: 80upx;
		line-height: 80upx;
		margin-bottom: 60upx;
		background-color: rgba(255, 255, 255, 0.8);
		box-sizing: border-box;
		padding: 0 30upx;
		border-radius: 10upx;
		font-size: 26upx;
		color: #333;
	}

	.codes input {
		width: 75%;
	}

	.codes view {
		height: 80upx;
		line-height: 80upx;
		width: 130upx;
		margin-bottom: 60upx;
		color: #FFFFFF;
		background-color: rgba(70, 143, 152, 0.8);
		text-align: center;
		font-size: 24upx;
		border-radius: 10upx;
	}

	.login_btn {
		width: 500upx;
		height: 80upx;
		margin: 0 auto;
		background-color: rgba(70, 143, 152, 0.8);
		text-align: center;
		line-height: 80upx;
		border-radius: 40upx;
		color: #fff;
	}

	.login_btn:active {
		opacity: 0.9;
	}

	.wxLogin {
		height: 200upx;
		width: 300upx;
		display: block;
		margin: 0 auto;
		border-radius: 50%;
		position: fixed;
		bottom: 7vh;
		left: 50%;
		transform: translateX(-50%);
	}

	.wxLogin view {
		text-align: center;
		color: #FFFFFF;
		font-size: 24upx;
		margin-bottom: 20upx;
	}

	.wxLogin image {
		height: 100upx;
		width: 100upx;
		display: block;
		z-index: 10;
		margin: 0 auto;
	}

	.wxLogin button {
		width: 100upx !important;
		height: 100upx;
		position: absolute;
		border-radius: 50%;
		text-align: center;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		opacity: 0;
		z-index: 10;
		padding: 0 !important;
	}
</style>

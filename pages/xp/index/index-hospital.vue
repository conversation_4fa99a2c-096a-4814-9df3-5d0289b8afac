<template>
	<view class="hospital-body">
		<template v-for="(item,index) in list">
			<view :key="index" class="hospital-item" @click="goHospital(item)">
				<view class="item-left">
					<u-lazy-load :image="$tools.showImg(item.imgHead,200)" img-mode="aspectFill" border-radius="8" width="160" height="160"></u-lazy-load>
				</view>
				<view class="item-right">
					<view class="name">
						{{item.name}}
						<text class="juli" v-if="item.juli<=1000 && item.juli!=''">{{ item.juli }}km</text>
						<text class="juli" v-if="item.juli>1000 && item.juli!=''">距你较远</text>
					</view>
					<view class="type">
						<view class="typetag" v-for="(itm,idx) in item.tags">{{itm}}</view>
					</view>
					<view class="content">
						{{item.describe}}
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	export default {
		props:{
			list:{
				type:Array,
				default:()=>[]
			}
		},
		methods: {
			goHospital(item) {
				this.$tools.routerTo('/pagesC/hospital/hospital?id=' + item.id)
			},
		}
	}
</script>

<style scoped lang="scss">
	.hospital-item {
		width: 100%;
		border-radius: 10rpx;
		background-color: #fff;
		margin: 30rpx 0;
		padding: 30rpx 20rpx;
		display: flex;
		align-items: flex-start;
		box-shadow: 0 0 8rpx 2rpx rgba(100, 100, 100, .12);
		
		.item-left {
			width: 160rpx;
			height: 160rpx;
			margin-right: 30rpx;
		}
		
		.item-right {
			flex: 1;
		
			// height: 200rpx;
			.name {
				font-size: 28rpx;
				font-weight: bolder;
				height: 75rpx;
				position: relative;
				
				.juli{
					position: absolute;
					right: 20rpx;
					top:40rpx;
					font-size: 24rpx;
					font-weight: normal;
				}
			}
		
			.type {
				font-size: 24rpx;
				font-weight: bolder;
				margin: 10rpx 0;
				background-image: linear-gradient(to right, #0bcebf, #00868B); //最后三行为文字渐变CSS
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				display: flex;
			}
			
			.typetag{
				margin-right: 10rpx;
			}
		
			.content {
				font-size: 22rpx;
				color: rgb(133, 133, 133);
				// height: 100rpx;
				overflow:hidden;
				text-overflow:ellipsis;
				display:-webkit-box;
				-webkit-line-clamp:2;
				-webkit-box-orient:vertical;
			}
		}
	}
</style>
<template>
	<view class="menu-box">
		<view class="menu" v-for="(item,index) in menu">
			<view class="btn-icon">
				<u-icon :name="item.icon" size="45" color='#00d2c5' labelPos='2' top='7'></u-icon>
			</view>
			<text class="text">{{item.name}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			menu:{
				type:Array,
				default: ()=>[],
			}
		},
		data() {
			return {
			}
		}
	}
</script>

<style scoped lang="scss">
	.menu-box{
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: flex-start;
	}
	.menu {
		width: 25%;
		padding-top: 30rpx;
		padding-bottom: 20rpx;
		display: inline-block;
		font-weight: bolder;
		color: rgb(127, 127, 127);
		text-align: center;
		
		display: flex;
		flex-direction: column;
		flex-wrap: nowrap;
		align-content: center;
		justify-content: center;
		align-items: center;
	
		.btn-icon {
			width: 110rpx;
			height: 110rpx;
			border-radius: 50%;
			text-align: center;
			line-height: 110rpx;
			margin-bottom: 10rpx;
			background-color: rgb(216, 242, 239);
	
		}
	}
</style>
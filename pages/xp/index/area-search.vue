<template>
	<ut-top bg-color="#fff">
		<view class="top-nav">
			<image :src="$tools.showImg(img,900)" mode="aspectFill"></image>
			<view class="city-icon">
				<view class="city-name">{{ place }}</view>
				<view class="iconfont icon-right"></view>
			</view>
			<u-search height="30" :showAction="false" bgColor="#F4F4F4" placeholder="找医院" shape="round" />

		</view>

	</ut-top>
</template>

<script>
	export default {
		props: {
			img: {
				type: String,
				default: 'https://img2.baidu.com/it/u=2720726909,3499407996&fm=253&fmt=auto&app=138&f=PNG?w=357&h=395',
			},
			place: {
				type: String,
				default: '昆明市',
			},
		},
		data() {
			return {

			}
		},

		computed: {

		},
		mounted() {



		},
		methods: {
			getNavHeight() {

			},

		}
	}
</script>

<style scoped lang="scss">
	.top-box {
		padding: 20rpx;
	}

	.top-nav {
		display: flex;
		align-items: center;
		padding: 10rpx 200rpx 10rpx 20rpx;
		
		image {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
		}

		.city-icon {
			display: flex;
			align-items: center;
			margin-left: 20rpx;

			.city-name {
				font-size: 28rpx;
				color: #000;
				// font-weight: 600;
			}

			.iconfont {
				margin-left: 10rpx;
				color: #000;
				font-size: 20rpx;
				margin-right: 16rpx;
			}
		}
	}
</style>
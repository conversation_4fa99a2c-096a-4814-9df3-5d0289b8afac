<template>
	<view class="my-menu">
		<view @click="message">
			<list-cell :rightshow="true" :colors="colors">
				<template v-slot:icon>
					<text class="iconfont icon-zhanghu" />
				</template>
				订阅消息
				<template v-slot:right></template>
			</list-cell>
		</view>
		<list-cell :rightshow="true" :colors="colors">
			<template v-slot:icon>
				<text class="iconfont icon-kefu1" />
			</template>
			<button open-type="contact" class="btn-contact" bindcontact="test" session-from="sessionFrom">问题与客服
			</button>

			<template v-slot:right></template>
		</list-cell>
		<view @click="address()">
			<list-cell :rightshow="true" :colors="colors">
				<template v-slot:icon>
					<text class="iconfont icon-advice" />
				</template>
				我的地址
				<template v-slot:right></template>
			</list-cell>
		</view>
		<view v-if="isStaff" @click="toStaff()">
			<list-cell :rightshow="true" :colors="colors">
				<template v-slot:icon>
					<text class="iconfont icon-settings" />
				</template>
				管理功能
				<template v-slot:right></template>
			</list-cell>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex'

export default {
	props: {
		colors: {
			type: String,
		},
		isStaff:{
			type:Boolean,
			default:false,
		},
	},
	data() {
		return {}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
		}),
	},
	methods: {
		async message() {
			let that = this
			uni.showLoading({
				title: '请稍等...',
			})
			const {data} = await this.$ut.api('comm/wechat/noticeTemplate', {
				commKey: this.commKey,
				code: 'charge',
			}).finally(()=>{uni.hideLoading()})
			const tmplIds = data.map((item) => item.tmpl_id)
			if(!tmplIds || !tmplIds.length) return
			that.$wxsdk.subscribe(tmplIds)

		},
		address() {
			uni.chooseAddress({
				success(res) {

				},
			})
		},
		toStaff() {
			this.$tools.routerTo(`/pagesA/index/index`)
		},

	},
}
</script>

<style>
.my-menu .list-cell:last-child::after {
	border-bottom: none;
}

.btn-contact {
	background-color: rgba(0, 0, 0, 0);
	text-align: left;
	font-size: 30rpx;
	padding-left: 0;
	line-height: 1;
}

.btn-contact::after {
	border: none;
}
</style>

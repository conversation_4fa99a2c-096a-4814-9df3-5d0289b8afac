<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="用户鉴权" navbarType="1"></f-navbar>
		<view class="view-card" v-if="!token">
			<view class="content-box">
				<view class="other">
					<view class="btn"
						:style="{background:'linear-gradient(90deg, '+colors+'60'+', '+colors+'90'+')',boxShadow:'0px 7rpx 6rpx 0px '+colors+'90'}"
						@click="checkLoginAndCommunity">
						<text>立即登录使用</text>
					</view>
				</view>
			</view>
		</view>
		<view v-else>
			<view v-if="!userInfo.phone" class="content-box">
				<view class="other">
					<!-- #ifdef MP -->
					<view class="btn"
						:style="{background:'linear-gradient(90deg, '+colors+'60'+', '+colors+'90'+')',boxShadow:'0px 7rpx 6rpx 0px '+colors+'90'}">
						<button class="btn-edit-phone" open-type="getPhoneNumber" :disabled="btnDisabled"
							@getphonenumber="getPhoneNumber">立即验证电话号码</button>
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<text>验证电话号码尊享生活</text>
					<!-- #endif -->
				</view>
			</view>
			<view class="image-box" v-else>
				<view v-if="!timeExpire">
					当前用户正在进行重要操作，系统安全需要本人进行真实性验证！
				</view>
				<view v-else>
					已经超过扫描时间，请在页面上刷新二维码。
				</view>

			</view>
		</view>


		<ut-fixed safe-area-inset position="bottom" v-if="!timeExpire">
			<view class="page-footer">
				<view class="cuicon cuIcon-edit btn"
					:style="{background:'linear-gradient(90deg, '+colors+'ff'+', '+colors+'ff'+')',boxShadow:'0px 7rpx 6rpx 0px '+colors+'90'}"
					@tap="scanAuth">
					同意继续操作
				</view>
			</view>
		</ut-fixed>
		<!-- :disabled="!edit.editFace" -->
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
	var app = getApp();
	import {
		mapActions,
		mapState
	} from 'vuex'

	export default {
		components: {},
		options: {
			styleIsolation: 'shared'
		},
		// inject:['bind','unbind'],
		data() {
			return {
				colors: '',
				scene: '',
				communityId: '',
				curUploadInfo: {},
				myPhotos: [],
				btnDisabled: false,
				showPhone: false,
				timeExpire: false,
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				token: state => state.user.token,
				userInfo: state => state.user.info,
				community: state => state.init.community,
				websocket: state => state.websocket.socketTask,
				refresh: state => state.user.refresh,
			}),
			headers() {
				return {
					Token: this.curUploadInfo.token
				}
			},
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor
			});

		},
		onLoad: async function(options) {
			await this.$onLaunched
			if (options.communityId) {
				this.communityId = options.communityId
			}
			if (options.scene) {
				// options 中的 scene 需要使用 decodeURIComponent 才能获取到生成二维码时传入的 scene
				this.scene = decodeURIComponent(options.scene)
				this.scanStart()
			}

			// this.bind('uploadimage',(res)=>{
			// 	console.log(res)
			// })
		},
		onUnload() {
			// this.unbind('uploadimage')
		},
		watch: {
			token: {
				handler(v) {
					if (v) this.scanStart()
				}
			}
		},
		methods: {
			...mapActions(['setToken', 'loginExit', 'setUser']),
			uploadFaceSuccess(uploadFileRes) {
				uploadFileRes.forEach(r => {
					let item = r.data
					this.myPhotos.push(this.curUploadInfo.preview + '?file=' + item.name)
					// this.myPhotos=[this.curUploadInfo.preview  + '?file='+item.name]
					console.log('img', this.curUploadInfo.preview + '?file=' + item.name)
					//this.$set(this.form,'imgHead',this.uploadInfo.preview  + '?file='+item.name+item.ext)
				})

			},
			imgDelete(e) {
				console.log(e)
			},
			save() {
				if (!this.myPhotos.length) return
				if (!this.userInfo || !this.userInfo.phone) return
				const params = {
					phone: this.userInfo.phone,
					type: 'uploadimage',
					content: JSON.stringify(this.myPhotos),
				}
				this.$ut.api('comm/user/socketSend', params).then(res => {
					this.$refs.upload.clear()
					this.myPhotos = []
				})

			},
			loadUploadInfo() {
				this.$ut.api('property/uploadInfo', {
					communityId: this.communityId
				}).then(res => {
					this.curUploadInfo = res.data
				})
			},
			checkLoginAndCommunity() {
				if (!this.token) {
					this.loginExit()
					return
				}
			},
			getPhoneNumber(e) {
				wx.reportEvent("getphonenumber", e)
				this.btnDisabled = true

				// #ifdef MP
				if (e.target.errMsg == 'getPhoneNumber:fail user deny') {
					//如果用户拒绝授权，弹出短信窗体
					this.btnDisabled = false
					this.showPhone = true
					return
				}

				const code = e.detail.code
				uni.showLoading({
					title: '请稍等...'
				})

				this.$ut.api('comm/user/updateMyPhone', {
					commKey: this.commKey,
					code: code
				}).then(() => {
					this.$ut.api('comm/login/refresh', {
						refresh_token: this.refresh
					}).then(res => {
						this.setToken(res.data)
						this.$ut.api('comm/login/myinfo').then(res2 => {
							this.setUser(res2.data)
							uni.hideLoading()
						})
						this.btnDisabled = false
					}).catch(err => {
						this.btnDisabled = false
						uni.hideLoading()
					})

					//提交保存电话
				}).catch(res => {
					this.btnDisabled = false
					uni.hideLoading()
				})
				// #endif

			},
			scanStart() {
				if (!this.token) return
				this.$ut.api('comm/wechat/mp/scanStart', {
					commKey: this.commKey,
					code: this.scene
				}).catch(res => {
					this.timeExpire = true
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}, 3000)

				})
			},
			scanAuth() {
				
					if (!this.token) return
				uni.showLoading({
					title: '请稍等'
				})
				this.$ut.api('comm/wechat/mp/scanAuth', {
					commKey: this.commKey,
					code: this.scene
				}).finally(() => uni.hideLoading()).then(() => {
					uni.showToast({
						title: '验证成功'
					})
					uni.reLaunch({
						url: '/pages/index/index'
					})

				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.image-box {
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		margin-top: 30rpx;
		width: 100%;
	}

	.page-footer {
		padding: 20rpx;
		width: 100%;
		display: flex;
		justify-items: center;
		align-items: center;
		justify-content: center;

		.btn {
			width: 100%;
			padding: 18rpx 10rpx;
			letter-spacing: 4rpx;
			border-radius: 35rpx;
			color: rgba(#fff, 0.9);
			text-align: center;
			font-size: 32rpx;
		}
	}

	.content-box {
		flex: 1;
		position: relative;
		height: 100%;
		display: flex;
		align-items: center;
		justify-items: center;
	}

	.other {
		width: 100%;
		padding-top: 10rpx;
		padding-left: 40rpx;
		padding-right: 40rpx;
		padding-bottom: 80rpx;
		display: flex;
		align-items: center;
		justify-items: center;
		justify-content: center;

		.btn {
			width: 340rpx;
			padding: 8rpx 12rpx;
			// background: linear-gradient(90deg, var(--colors2), var(--colors3));
			// box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
			border-radius: 35rpx;
			color: rgba(#fff, 0.9);
			text-align: center;
			font-size: 30rpx;
		}
	}

	.view-card {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		transition: box-shadow 0.3s ease-in-out;
		// margin: 5rpx;
		padding: 0rpx 16rpx;
		min-height: 400rpx;
	}

	.btn-edit-phone {
		background: none;
		color: inherit;
		padding: inherit;
		margin: inherit;
		line-height: inherit;
		font-size: inherit;

		&::after {
			border: inherit;
		}
	}
</style>
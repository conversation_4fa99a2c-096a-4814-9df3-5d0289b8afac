<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" navbarType="1" title="物业管理" />
	
		<template v-for="(item,index) in newMenu">
			<view :key="index">
				<view v-if="item.menus.length" class="list bg-white ut-radius margin padding-xs">
					<list-cell>{{item.title}}</list-cell>
					<ut-grid :colors="colors" :list="item.menus" :count="item.menus.length>4?4:item.menus.length"></ut-grid>
				</view>
			</view>
		</template>

		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
import { mapActions, mapState } from 'vuex'

const app = getApp()

export default {
	data() {
		return {
			colors: '',
			menuList:[],
			menu:[
				{
					'title':'抄表及装修',
					'menus':[
						{
							'name': '抄表',
							'url': '/pagesA/admin/index/index',
							'image': 'http://oss.ynyzmx.com/api/file/preview?file=7P6bvaD',
							'num': 0,
						},
						{
							'name': '巡更',
							'url': '/pagesA/admin/index/index',
							'image': 'http://oss.ynyzmx.com/api/file/preview?file=7P6idqt',
							'num': 0,
						},
						{
							'name': '装修巡检',
							'url': '/pagesA/admin/process-inspection/process-inspection',
							'image': 'http://oss.ynyzmx.com/api/file/preview?file=7P6k7ER',
							// "num": 3
						},
						{
							'name': '装修验收',
							'url': '/pagesA/admin/process-accept/process-accept',
							'image': 'http://oss.ynyzmx.com/api/file/preview?file=7P6jjfl',
							// "num": 3
						},
					],
				},
				{
					'title':'信息查询',
					'menus':[
						{
							'code':'car-find',
							'name': '车辆查询',
							'url': '/pagesB/car/find/find',
							'image': 'http://oss.ynyzmx.com/api/file/preview?file=7P6bvaD',
							'num': 0,
						},
					],
				}
				
			],
			
		}
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
		if (!this.token) {
			this.loginExit()
		}
		
		this.getMyMenu()
		// todo 检查权限
	},
	async onLoad(options) {
		await this.$onLaunched
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			community: state => state.init.community,
			token: state => state.user.token,
		}),
		newMenu(){
			if(!this.menuList) return []
			let arr=[]
			this.menu.forEach(item=>{
				let sub={}
				sub.title=item.title
				sub.menus=[]
				arr.push(sub)
				item.menus.forEach(item2=>{
					let obj=this.menuList.find(u=>u.code==item2.code)
					if(obj) sub.menus.push({
						code:item2.code,
						name:obj.title,
						url:item2.url,
						image:obj.icon?obj.icon:item2.image,
						
					})
				})
				
			})
			return arr
		}
	},
	methods: {
		...mapActions(['loginExit']),
		checkLogin() {
			if (!this.token) {
				this.loginExit()
				return true
			}
			return false
		},
		routeTo(path) {
			if (this.checkLogin()) return
			this.$tools.routerTo(`/pagesA/admin/${path}`)
		},
		async getMyMenu(){
			const {data} = await this.$ut.api('staff/myMenu',{
				commKey:this.commKey,
				baseId:this.community.id
			})
			this.menuList=data
			
		}
	},
}
</script>

<style lang="scss" scoped>
.list {
	.list-cell:last-child {
		border-bottom: none;
	}
}
</style>

<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="拍照协助" navbarType="2"></f-navbar>
		<view class="view-card"  v-if="!token" >
			<view class="content-box">
				<view class="other">
					<view class="btn"
						:style="{background:'linear-gradient(90deg, '+colors+'60'+', '+colors+'90'+')',boxShadow:'0px 7rpx 6rpx 0px '+colors+'90'}"
						@click="checkLoginAndCommunity"
					>
						<text>立即登录使用</text>
					</view>
				</view>
			</view>
		</view>
		<view v-else>
			<view v-if="!userInfo.phone"  class="content-box">
				<view class="other">
					<!-- #ifdef MP -->
					<view class="btn"
						:style="{background:'linear-gradient(90deg, '+colors+'60'+', '+colors+'90'+')',boxShadow:'0px 7rpx 6rpx 0px '+colors+'90'}"
					>
						<button class="btn-edit-phone" open-type="getPhoneNumber" :disabled="btnDisabled" @getphonenumber="getPhoneNumber">立即验证电话号码</button>
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<text>验证电话号码尊享生活</text>
					<!-- #endif -->
				</view>
			</view>
			<view class="image-box" v-else>
				<ut-image-upload
					ref="upload"
					name="file"
					v-model="myPhotos"
					mediaType="image"
					:colors="colors"
					:max="20"
					:headers="headers"
					:action="curUploadInfo.server+curUploadInfo.single||''"
					:preview-image-width="1200"
					:width="220"
					:height="280"
					:border-radius="8"
					@uploadSuccess="uploadFaceSuccess"
					@imgDelete="imgDelete">
				</ut-image-upload>
			</view>
		</view>
		
		
		<ut-fixed safe-area-inset position="bottom">
			<view class="page-footer">
				<view
					v-if="myPhotos.length>0"
					@click="save"
					class="cuicon cuIcon-edit btn"
					:style="{background:'linear-gradient(90deg, '+colors+'ff'+', '+colors+'ff'+')',boxShadow:'0px 7rpx 6rpx 0px '+colors+'90'}">
					同步到电脑端</view>
				<view v-else class="cuicon cuIcon-edit btn"
					  :style="{background:'linear-gradient(90deg, #ccc, #ccc)',boxShadow:'0px 7rpx 6rpx 0px #ccc'}">
					请点击上方拍照或选择手机照片
				</view>
			</view>
		</ut-fixed>
			<!-- :disabled="!edit.editFace" -->
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp();
import {mapActions,mapState} from 'vuex'

export default {
	components: {
	},
	options: {
		styleIsolation: 'shared'
	},
	// inject:['bind','unbind'],
	data() {
		return {
			colors:'',
			scene:'',
			communityId:'',
			curUploadInfo:{},
			myPhotos: [],
			btnDisabled:false,
			showPhone:false,
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			// uploadInfo: state => state.init.oss,
			websocket: state => state.websocket.socketTask,
			refresh:state => state.user.refresh,
		}),
		headers(){
			return {
				Token:this.curUploadInfo.token
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor
		});

	},
	onLoad: async function(options) {
		await this.$onLaunched
		if(options.communityId){
			 this.communityId=options.communityId
		}
		if(options.scene){
		   // options 中的 scene 需要使用 decodeURIComponent 才能获取到生成二维码时传入的 scene
		   this.scene = decodeURIComponent(options.scene)
		   if(this.scene){
			   const arr=this.scene.split(',')
			   if(arr.length==2){
				   this.communityId=arr[1]
			   }
		   }
		}
		
		// this.bind('uploadimage',(res)=>{
		// 	console.log(res)
		// })
	},
	onUnload(){
		// this.unbind('uploadimage')
	},
	watch:{
		communityId:{
			handler(v){
				if(v) this.loadUploadInfo()
			}
		},
		token:{
			handler(v){
				if(v) this.loadUploadInfo()
			}
		}
	},
	methods:{
		...mapActions(['setToken', 'loginExit','setUser']),
		uploadFaceSuccess(uploadFileRes){
			uploadFileRes.forEach(r=>{
				let item=r.data
				this.myPhotos.push(this.curUploadInfo.preview  + '?file='+item.name)
				// this.myPhotos=[this.curUploadInfo.preview  + '?file='+item.name]
				console.log('img',this.curUploadInfo.preview  + '?file='+item.name)
				//this.$set(this.form,'imgHead',this.uploadInfo.preview  + '?file='+item.name+item.ext)
			})

		},
		imgDelete(e){
			console.log(e)
		},
		save(){
			if(!this.myPhotos.length) return
			if(!this.userInfo || !this.userInfo.phone) return
			const params={
				phone:this.userInfo.phone,
				type:'uploadimage',
				content:JSON.stringify(this.myPhotos),
			}
			this.$ut.api('comm/user/socketSend',params).then(res=>{
				this.$refs.upload.clear()
				this.myPhotos=[]
			})

		},
		loadUploadInfo(){
			this.$ut.api('property/uploadInfo',{communityId:this.communityId}).then(res=>{
				this.curUploadInfo=res.data
			})
		},
		checkLoginAndCommunity(){
			if(!this.token){
				this.loginExit()
				return
			}
		},
		getPhoneNumber(e) {
			wx.reportEvent("getphonenumber", e)
			this.btnDisabled=true
			
			// #ifdef MP
			if(e.target.errMsg=='getPhoneNumber:fail user deny'){
				//如果用户拒绝授权，弹出短信窗体
				this.btnDisabled=false
				this.showPhone = true
				return
			}
			
			const code=e.detail.code
			uni.showLoading({
				title:'请稍等...'
			})
			
			this.$ut.api('comm/user/updateMyPhone',{commKey:this.commKey,code:code}).then(()=>{
				this.$ut.api('comm/login/refresh',{
					refresh_token: this.refresh
				}).then(res => {
					this.setToken(res.data)
					this.$ut.api('comm/login/myinfo').then(res2 => {
						this.setUser(res2.data)
						uni.hideLoading()
					})
					this.btnDisabled=false
				}).catch(err => {
					this.btnDisabled=false
					uni.hideLoading()
				})
				
				//提交保存电话
			}).catch(res=>{
				this.btnDisabled=false
				uni.hideLoading()
			})
			// #endif
			
		},	
	}
}
</script>

<style lang="scss" scoped>
	.image-box{
		padding: 0 30rpx;
		display: flex;
		align-items: center;
		margin-top: 30rpx;
		width: 100%;
	}

	.page-footer {
		padding: 20rpx;
		width: 100%;
		display: flex;
		justify-items: center;
		align-items: center;
		justify-content: center;
		.btn {
			width: 100%;
			padding: 18rpx 10rpx;
			letter-spacing: 4rpx;
			border-radius: 35rpx;
			color: rgba(#fff, 0.9);
			text-align: center;
			font-size: 32rpx;
		}
	}
	
	.content-box{
		flex: 1;
		position: relative;
		height: 100%;
		display: flex;
		align-items: center;
		justify-items: center;
	}
	.other{
		width: 100%;
		padding-top: 10rpx;
		padding-left: 40rpx;
		padding-right: 40rpx;
		display: flex;
		align-items: center;
		justify-items: center;
		justify-content: center;
		
		.btn{
			width: 340rpx;
			padding: 8rpx 12rpx;
			// background: linear-gradient(90deg, var(--colors2), var(--colors3));
			// box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
			border-radius: 35rpx;
			color: rgba(#fff, 0.9);
			text-align: center;
			font-size: 30rpx;
		}
	}
	
	.view-card{
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		// border-radius: 8rpx;
		// border:1rpx solid rgba(0,0,0,0.1);
		// box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.15);
		transition: box-shadow 0.3s ease-in-out;
		// margin: 5rpx;
		padding: 0rpx 16rpx;
		height: calc(100vh - 80rpx);
	}
	
	.btn-edit-phone{
		background: none;
		color: inherit;
		padding: inherit;
		margin: inherit;
		line-height: inherit;
		font-size: inherit;
		&::after{
			border: inherit;
		}
	}
	
	
	
</style>

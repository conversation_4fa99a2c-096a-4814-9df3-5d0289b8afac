<template>
	<view v-if="show" class="svga-box">
	    <c-svga ref="cSvgaRef" canvasId='myCanvas' v-if="showSvg && svg!=''"
	            :src="svg" 
	            :loops='loop' :auto-play="true"></c-svga>
		<view v-else class="container">
			<text></text>
			<text></text>
			<text></text>
			<text></text>
			<text></text>
			<view class="loading">请等待...</view>
		</view>		
	</view>
</template>

<script>
	import CSvga from '../c-svga/c-svga.vue'
	export default {
		name: 'loading',
		data() {
			return {
			};
		},

		components: {
			CSvga
		},
		props: {
			show: {
				type: Boolean,
				default: false
			},
			showSvg: {
				type: Boolean,
				default: false
			},
			loop:{
				type:Number,
				default:0
			},
			svg:{
				type:String,
				default:'https://oss.afjy.net/api/file/preview/8gsISEi.svg',
				//https://mp-eeab6da6-80cd-4e80-844a-66b2a7203834.cdn.bspapp.com/cloudstorage/b3875bda-8de0-476d-8f55-d10b24f16d98.svg
			}
		},
		created() {
			
		},
		mounted() {
			// console.log(this.$refs.cSvgaRef)
			// this.$refs.cSvgaRef.call('startAnimation')
		},
		methods: {
			onFinished() {
				console.log('动画停止播放时回调');
			},
			onFrame(frame){//动画播放至某帧后回调
				// console.log(frame);
			},
			onPercentage(percentage){ //动画播放至某进度后回调
				// console.log(percentage);
			},
			onLoaded(){
				console.log('加载完成');
			}
		}
	};
</script>

<style scoped lang="scss">
	.svga-box{
		display: flex;
		justify-content: center;
		align-items: center;
	
		position: fixed;
		left: 0;
		top: 0;
		z-index: 1000;
		width:100vw;
		height:100vh;
		background-color: rgba(255,255,255,0.5);
	}
	
	.container {
	  position:relative;
	  
	  text{
		position:absolute;
		width:40rpx;
		height:40rpx;
		background:#3498db;
		opacity:0.5;
		border-radius:40rpx;
		left:-60rpx;
		-webkit-animation: preloader_4 1s infinite ease-in-out;
		-moz-animation: preloader_4 1s infinite ease-in-out;
		-ms-animation: preloader_4 1s infinite ease-in-out;
		-animation: preloader_4 1s infinite ease-in-out;
	}
	
	text:nth-child(2){
		left:-20rpx;
		-webkit-animation-delay: .2s;
		-moz-animation-delay: .2s;
		-ms-animation-delay: .2s;
		animation-delay: .2s;
	}
	
	text:nth-child(3){
		left:20rpx;
		-webkit-animation-delay: .4s;
		-moz-animation-delay: .4s;
		-ms-animation-delay: .4s;
		animation-delay: .4s;
	}
	text:nth-child(4){
		left:60rpx;
		-webkit-animation-delay: .6s;
		-moz-animation-delay: .6s;
		-ms-animation-delay: .6s;
		animation-delay: .6s;
	}
	text:nth-child(5){
		left:100rpx;
		-webkit-animation-delay: .8s;
		-moz-animation-delay: .8s;
		-ms-animation-delay: .8s;
		animation-delay: .8s;
	}
	
	@-webkit-keyframes preloader_4 {
	    0% {opacity: 0.3; -webkit-transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	    50% {opacity: 1; -webkit-transform: translateY(-20rpx); background:#f1c40f;	box-shadow: 0px 40rpx 6rpx rgba(0, 0, 0, 0.05);}
	  	100%  {opacity: 0.3; -webkit-transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	}
	@-moz-keyframes preloader_4 {
	    0% {opacity: 0.3; -moz-transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	    50% {opacity: 1; -moz-transform: translateY(-20rpx); background:#f1c40f;	box-shadow: 0px 40rpx 6rpx rgba(0, 0, 0, 0.05);}
	  	100%  {opacity: 0.3; -moz-transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	}
	@-ms-keyframes preloader_4 {
	    0% {opacity: 0.3; -ms-transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	    50% {opacity: 1; -ms-transform: translateY(-20rpx); background:#f1c40f;	box-shadow: 0px 40rpx 6rpx rgba(0, 0, 0, 0.05);}
	  	100%  {opacity: 0.3; -ms-transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	}
	@keyframes preloader_4 {
	    0% {opacity: 0.3; transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	    50% {opacity: 1; transform: translateY(-20rpx); background:#f1c40f;	box-shadow: 0px 40rpx 6rpx rgba(0, 0, 0, 0.05);}
	  	100%  {opacity: 0.3; transform:translateY(0px);	box-shadow: 0px 0px 6rpx rgba(0, 0, 0, 0.1);}
	}
}

.loading{
	padding-top: 70rpx;
}
	
</style>

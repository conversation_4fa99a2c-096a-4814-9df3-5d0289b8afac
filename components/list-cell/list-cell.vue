<template>
	<view class="list-cell" >
		<view class="icon" v-if="$slots.icon" :style="{color:colors}">
			<slot name="icon" />
		</view>
		<view class="name">
			<slot />
		</view>
		<view class="right-box">
			<slot name="right" />
			<text class="iconfont icon-right" v-if="rightshow"></text>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			icons: {
				type: String
			},
			linkUrl: {
				type: String //跳转的页面地址
			},
			colors: {
				type: String
			},
			rightshow: {
				type: Boolean,
				default: true
			},
		},
		methods: {

		}
	};
</script>
<style lang="scss">
	.list-cell {
		line-height: 60upx;
		padding: 20upx 30upx;
		display: flex;
		align-items: center;
		position: relative;
		align-content: center;
		flex-direction: row;

		.icon {
			align-self: center;
			font-size: 30rpx;
			max-height: 60upx;
			margin-right: 20rpx;
			width: 40rpx;
			text-align: center;
		}

		.icon-right {
			margin-left: 20rpx;
		}

		.right-box {
			display: flex;
		}
	}

	.list-cell::after {
		position: absolute;
		left: 30upx;
		right: 0;
		height: 0;
		content: '';
		bottom: 0;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
		border-bottom: 1px solid #E4E7ED;
	}

	.list-cell:active {
		background-color: #fafafa;
	}



	.list-cell .name {
		flex: 1;
		font-size: 30upx;
		color: #303133;
	}

	.list-cell .iconfont {
		font-size: 24rpx;
		color: #303133;
	}
</style>

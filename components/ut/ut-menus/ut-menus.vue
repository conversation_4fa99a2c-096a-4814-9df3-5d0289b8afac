<template>
	<view class="my_server" v-if="isShow">
		<view class="title"><text :class="'iconfont ' + icon" :style="'color:' + colors"></text> {{title}}</view>
		<view class="server_box">
			<block v-for="(item, index) in detail" :key="index">
				<view  v-if="!item.isShow" class="server_list" @tap="jumpLink(item)">
					<view>
						<text :class="'iconfont ' + item.icon" :style="'color:' + colors"></text>
					</view>
					<view class="server_text">
						{{item.name}}
					</view>
					<s-badge v-if="item.count>0" class="count" :value="item.count"/>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		name: "ut-menus",
		props: {
			detail: {
				type: Array,
				default:function(){
					return []
				}
			},
			colors: {
				type: String
			},
			title: {
				type: String,
				default: '系统配置'
			},
			icon: {
				type: String,
				default: 'icon-fuwu'
			},
			isShow:{
				type:Boolean,
				default:true,
			}
		},
		data() {
			return {

			};
		},
		mounted() {
			
		},
		methods: {
			jumpLink(row) {
				// console.log(row)
				//页面跳转
				if (row.url == '') {
					return;
				} else {
					uni.navigateTo({
						url: row.url
					});
				}
		 }
		}
	}
</script>

<style lang="scss">
	.title{
		display: flex;
		align-items: center;
		font-size: 26rpx;
		letter-spacing: 4rpx;
		line-height: 2;
		font-weight: bold;
		padding: 10rpx 20rpx 0 20rpx;
		.iconfont{
			margin-right: 10rpx;
		}
	}
	.my_server {
		background-color: #fff;
		margin: 15rpx;
		border-radius: 10rpx;
		box-shadow: 0 0 2rpx 0 rgba(0, 0, 0, 0.1);
	}

	.server_box {
		border-top:1rpx solid #ccc;
		display: flex;
		align-items: center;
		flex-direction: row;
		flex-wrap: wrap;
		margin: 0 20rpx;
	}

	.server_box::after {
		width: 25%;
		content: '';
		border: 1px solid transparent;
	}

	.server_list {
		max-width: 20%;
		min-width: 20%;
		text-align: center;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
	}

	.server_list:active {
		opacity: 0.8;
	}

	.server_list text {
		font-size: 56upx;
		line-height: 60upx;
		display: inline-block;
	}

	.server_text {
		font-size: 22upx;
		color: #555555;
		margin-top: 15upx;
		white-space: nowrap;
		overflow: hidden;
		// text-overflow: ellipsis;
	}
	
	.count{
		position: absolute;
	
		top:8rpx;
		right:26rpx
	}
</style>

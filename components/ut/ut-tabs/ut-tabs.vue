<template>
	<view class="ut-tabs-container" :style="{'background-color':bjColor}">
		<scroll-view scroll-x scroll-with-animation class="ut-tabs-scroll">
			<view class="ut-tabs-scroll-box" :style="{'height': tabHeight,'line-height': tabHeight}">
				<view class="ut-tabs-scroll-box-item" :class="tabIndex==index?'active':''"   v-for="(item,index) in tabsData" :key="index" @click="onTabIndex(index,item)">
					<view class="name"  :style="{'color':fontColor}">{{item.name}}</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		name:"ut-tabs",
		props:{
			bjColor:{
				type:String,
				default:"#fff"
			},
			tabHeight:{
				type: String,
				default: '90rpx'
			},
			tabsData:{
				type:Array,
				default:function(){
					return []
				}
			},
			lineColor:{
				type:String,
				default:"#fa436a"
			},
			fontColor:{
				type:String,
				default:"#fff"
			},
		},
		data() {
			return {
				tabIndex:0
			};
		},
		methods:{
			onTabIndex(index,item){
				if(this.tabIndex!=index){
					this.tabIndex = index;
					// this.$emit("update:tabIndex",index);
					this.$emit("change",index,item)
				}
			}
		}
	}
</script>

<style lang="scss">
	$color:var(--colors);
	.ut-tabs-container{
		width: 100%;
	}
	.ut-tabs-scroll-box{
		white-space: nowrap !important;
		padding: 4rpx;
		display: flex;
		justify-content: space-evenly;
		&-item{
			min-width: 100rpx;
			position: relative;
			padding: 0rpx 10rpx;
			font-size: 26rpx;
			letter-spacing: 4rpx;
			display: inline-block;
			text-align: center;
			.name{
				width: 100%;
			}
			
			flex-shrink: 0;
			position: relative;
			transition: all 0.2s linear;
			&::after{
				transition: all 0.2s linear;
				transform: translateX(-50%) scaleX(0);
				content: '';
				width: 50%;
				position: absolute;
				left: 50%;
				bottom: 20rpx;
				border-bottom: 6rpx solid red;
				border-radius: 4rpx;
			}
		}
	}
	.active{
		color: $color;
		
		&::after{
			content: '';
			width: 100%;
			position: absolute;
			left: 50%;
			transform: translateX(-50%) scaleX(1);
			bottom: 10rpx;
			border-bottom: 4rpx solid red;
		}
		// 
	}
</style>

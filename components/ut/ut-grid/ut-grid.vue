<template>
	<view class="menu-box">
		<view v-for="(item, index) in list" :key="index" class="menu-item" @tap="jump(item)" :style="{width:count>4?100/count:25+'%'}">
			<view class="menu-item-cell" :style="cellStyle">
				<u-badge :absolute="true" :offset="['-6rpx','-4rpx']" max="99" :bgColor="colors" :value="item.num"></u-badge>
				<text class="iconfont" :style="{color:colors}" :class="item.icon" v-if="!item.image"></text>
				<view v-else class="menu-item-image">
					<u--image :showLoading="true" :src="item.image" width="64rpx" height="64rpx" style="padding: 12rpx;" radius="4rpx"></u--image>
				</view>
				
			</view>
			<view :style="{color:textColor?textColor:colors}" >{{item.name}}</view>
			<view v-if="item.star" class="star">
				<u-icon name="star-fill" :color="colors" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:'ut-grid',
		components: {
		},
		props: {
			colors: {
				type: String
			},
			textColor:{
				type:String
			},
			count:{
				type:Number,
				default:4,
			},
			list:{
				type:Array,
				default:()=>[],
			},
			cellColor:{
				type:[Boolean,String],
				default:false,
			},
			opacity:{
				type:Number,
				default:20,
			},
			jumpParam:{
				type:Object,
				default:()=>{},
			}
			
		},
		computed: {
			cellStyle(){
				if(this.cellColor==false || !this.cellColor) return {}
				if(typeof this.cellColor ==='string'){
					return {background:this.cellColor,borderRadius:'8rpx',padding:'2rpx'}
				}else{
					return {background:this.colors+this.opacity,borderRadius:'8rpx',padding:'2rpx'}
				}
			}
		},

		data() {
			return {
				
			};
		},
		methods: {
			jump(item){
				if(!item)return
				if(item.isTab){
					uni.reLaunch({
						url:item.url
					})
					// uni.switchTab({
					// 	url:item.url,
					// })
				}else{
					if(this.jumpParam){
						this.$tools.routerTo(item.url,this.jumpParam)
					}else{
						this.$tools.routerTo(item.url)
					}
					
				}
			}
		}
	};
</script>

<style lang="scss">
	.menu-box {
		display: flex;
		align-items: flex-start;
		flex-wrap: wrap;
		justify-content: flex-start;
	}
	
	.menu-item-image{
	}
	
	.menu-item {
		text-align: center;
		display: flex;
		flex-direction: column;
		justify-content:flex-start;
		align-items: center;
		margin: 10upx 0 20upx;
		padding: 20upx 0 10upx;
		font-size: 24upx;
		color: #75787d;
		line-height: 40upx;
		position: relative;

		
		.menu-item-cell{
			padding: 10rpx;
			position: relative;
			.iconfont{
				font-size: 70rpx;
				padding: 12px 0;
				display: block;
			}
			.u-badge{
				font-size: 20rpx;
				opacity:0.85;
				z-index: 1;
			}
			
		}
	}
	
	.star{
		position: absolute;
		left: 30rpx;
		top: 5rpx;
	}
</style>

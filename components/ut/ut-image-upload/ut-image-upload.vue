<template>
	<view class="ut-image-upload-list">
		<view class="ut-image-upload-Item"
			:style="{width:width+'rpx',height:height+'rpx',borderRadius:borderRadius+'rpx',padding:spacing+'rpx'}"
			v-for="(item,index) in uploadLists" :key="index">
			<view class="ut-image-upload-Item-video" :style="{borderRadius:borderRadius+'rpx'}"
				v-if="(/.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url))" @touchstart="touchstart(item,index,1)"
				@touchend="touchend">
				<video :disabled="false" :controls="false" :src="item.url">
					<cover-view class="ut-image-upload-Item-video-fixed" :style="{borderRadius:borderRadius+'rpx'}"
						@click="previewVideoClick(item.url)">
						<cover-view class="ut-image-upload-Item-del-cover image-show"
							v-if="false && remove && previewVideoSrc==''" @click.stop="imgDel(item,index)">×</cover-view>
					</cover-view>
				</video>
			</view>
			<view v-else class="image-loading-box" @touchstart="touchstart(item,index,0)" @touchend="touchend">
				<image :lazy-load="true" class="image-real" :src="showImg(item.url)"
					@click="imgPreviewClick(item,index)" :style="{borderRadius:borderRadius+'rpx'}" :mode="mode"
					@load="imageLoad(index)"></image>
				<view class="image-loading" v-if="!checkLoad(index)">
					<view class="loader">
						<view class="loaders">
							<view class="loader2"
								:style="{width:imageLoadingWidth()+'rpx',height:imageLoadingWidth()+'rpx'}"></view>
						</view>
						加载中
					</view>
				</view>
				<view class="ut-image-upload-progress-text" v-if="item.progress!=100" :style="{lineHeight:height+'rpx'}">{{item.progress}}%</view>
				<view class="ut-image-upload-progress" v-if="item.progress!=100" @click="imgPreviewClick(item,index)" :style="{lineHeight:height+'rpx',opacity:1-item.progress/100}"></view>
				<view class="ut-image-upload-Item-del" :class="{'image-show':checkLoad(index)}"
					v-if="!disabled && remove && item.progress==100 && checkLoad(index)" @click="imgDel(item,index)">×</view>
			</view>
		</view>
		<view class="ut-image-upload-Item"
			:style="{width:width+'rpx',height:height+'rpx',padding:spacing+'rpx'}"
			v-if="uploadLists.length<max && add" @click="chooseFile">
			<view class=" ut-image-upload-Item-add" :style="{borderRadius:borderRadius+'rpx',lineHeight:(height-2*spacing)+'rpx'}">
				<text v-if="!$slots.default">+</text>
				<slot name="default" v-else></slot>
			</view>
		</view>

		<view class="preview-full" v-if="previewVideoSrc!=''">
			<video :autoplay="true" :src="previewVideoSrc" :show-fullscreen-btn="false">
				<cover-view class="preview-full-close" @click="previewVideoClose"> ×
				</cover-view>
			</video>
		</view>


		<!--  -->
	</view>
</template>

<!-- 使用方法 -->
<!-- <ut-image-upload
	name="file"
	v-model="myPhotos"   							['url']
	mediaType="video"								image video all
	:colors="colors"
	:max="1"
	:headers="headers" 								{Token}
	:action="uploadInfo.server+uploadInfo.single" 	上传地址uploadUrl
	:preview-image-width="1200"						预览图片宽度
	:width="150"									方格宽度
	:height="120"									方格高度
	:border-radius="8"								圆角
	@uploadSuccess="uploadSuccess" 					成功事件
	@imgDelete="imgDelete">							删除事件
	<template v-slot:default>
		<view class="camera">
			<u-icon name="camera-fill" color="#ccc" size="28"></u-icon>
			<text class="text">正面照片</text>
		</view>
	</template>
</ut-image-upload> -->
<!-- uploadSuccess(uploadFileRes){

		uploadFileRes.forEach(r=>{
					let item=r.data
					if(item) this.myPhotos.push(this.uploadInfo.preview  + '?file='+item.name+item.ext)
				})
				
},
imgDelete(e){
}, -->

<script>
	export default {
		name: 'ut-image-upload',
		props: {
			width: {
				type: [Number,String],
				default: 120,
			},
			colors: {
				type: String,
				default: '',
			},
			height: {
				type: [Number,String],
				default: 120,
			},
			borderRadius: {
				type: Number,
				default: 8,
			},
			dataType: { //v-model的数据结构类型
				type: Number,
				default: 0, // 0: ['http://xxxx.jpg','http://xxxx.jpg'] 1:[{type:0,url:'http://xxxx.jpg'}]  type 0 图片 1 视频 url 文件地址 此类型是为了给没有文件后缀的状况使用的
			},
			max: { //展示图片最大值
				type: Number,
				default: 1,
			},
			chooseNum: { //选择图片数
				type: Number,
				default: 9,
			},
			name: { //发到后台的文件参数名
				type: String,
				default: 'file',
			},
			remove: { //是否展示删除按钮
				type: Boolean,
				default: true,
			},
			add: { //是否展示添加按钮
				type: Boolean,
				default: true,
			},
			disabled: { //是否禁用
				type: Boolean,
				default: false,
			},
			sourceType: { //选择照片来源 【ps：H5就别费劲了，设置了也没用。不是我说的，官方文档就这样！！！】
				type: Array,
				default: () => ['album', 'camera'],
			},
			action: { //上传服务器
				type: String,
				default: '',
			},
			headers: { //上传的请求头部
				type: Object,
				default: () => {},
			},
			formData: { //HTTP 请求中其他额外的 form data
				type: Object,
				default: () => {},
			},
			compress: { //是否需要压缩
				type: Boolean,
				default: true,
			},
			quality: { //压缩质量，范围0～100
				type: Number,
				default: 100,
			},
			value: { //受控图片列表
				type: Array,
				default: () => [],
			},
			uploadSuccess: {
				default: (res) => {
					return {
						success: false,
						url: ''
					}
				},
			},
			mediaType: { //文件类型 image/video/all
				type: String,
				default: 'image',
			},
			maxDuration: { //拍摄视频最长拍摄时间，单位秒。最长支持 60 秒。 (只针对拍摄视频有用)
				type: Number,
				default: 60,
			},
			camera: { //'front'、'back'，默认'back'(只针对拍摄视频有用)
				type: String,
				default: 'back',
			},
			mode: {
				type: String,
				default: 'aspectFill',
			},
			longPress: {
				type: Boolean,
				default: false,
			},
			// listIndex: {
			// 	type: Number,
			// 	default: -1,
			// },
			previewImageWidth: {
				type: Number,
				default: 750,
			},
			spacing:{
				type:Number,
				default:10,
			}
		},
		data() {
			return {
				timer: null,
				islongPress: false,
				imageLoaded: {},
				uploadLists: [],
				mediaTypeData: ['image', 'video', 'all'],
				previewVideoSrc: '',
				// 储存上传文件的请求 用于整理思路
				uploadTasks: [],
				// 保存父组件/页面的监听事件
				uploadTask: {
					// 默认不监听 调用setUpMonitor函数则监听
					load: false
				},
				uploadData:[],
				// 保存当前所有任务的进度 用于返回父组件/页面的监听事件
				uploadTaskProgress: [],
				isLoaded:false,
			}
		},
		mounted: function() {
			this.$nextTick(function() {
				this.uploadLists = []
				this.value.forEach(item => {
					if(typeof item==='object'){
						this.uploadLists.push({
							id: item.id,
							url: item.url,
							state:6,
							progress: 100,
						})
					}else{
						this.uploadLists.push({
							id: this.guid(),
							url: item,
							state:6,
							progress: 100,
						})
					}
				})
				//this.uploadLists = this.value;

				if (this.mediaTypeData.indexOf(this.mediaType) == -1) {
					uni.showModal({
						title: '提示',
						content: 'mediaType参数不正确',
						showCancel: false,
						success: function(res) {
							if (res.confirm) {
							} else if (res.cancel) {
							}
						}
					});
				}
			});
		},
		watch: {
			value(val, oldVal) {
				if(this.isLoaded) return
				if(!oldVal.length && !val.length){
					this.isLoaded=true
					return
				}
				this.uploadLists = []
				val.forEach(item => {
					this.uploadLists.push({
						id: this.guid(),
						url: item,
						state:6,
						progress: 100,
					})
				})
				this.isLoaded=true
			},

		},
		methods: {
			imageLoadingWidth() {
				if (this.width > this.height) {
					return this.height > 40 ? this.height / 3 : 40
				} else {
					return this.width > 40 ? this.width / 3 : 40
				}
			},
			imageLoad(index) {
				this.$set(this.imageLoaded, 'l_' + index, true)
			},
			checkLoad(index) {
				if (this.imageLoaded['l_' + index]) return true
				return false
			},
			isVideo(item) {
				let isPass = false
				if ((!/.(gif|jpg|jpeg|png|gif|jpg|png)$/i.test(item) && this.dataType == 0) || (this.dataType == 1 && item
						.type == 1)) {
					isPass = true
				}
				return isPass
			},
			getFileUrl(item) {
				var url = item;
				if (this.dataType == 1) {
					url = item.url
				}
				return url
			},
			previewVideoClick(src) {
				if (!this.islongPress) {
					this.previewVideo(src)
				}
			},
			previewVideo(src) {
				this.previewVideoSrc = src;
			},
			previewVideoClose() {
				this.previewVideoSrc = ''
			},
			imgDel(item,index) {
				uni.showModal({
					title: '提示',
					content: '您确定要删除么?',
					success: (res) => {
						if (res.confirm) {
							
							let delObj = this.uploadLists[index]
							// console.log(index)
							this.uploadLists.splice(index, 1)
							let arr=[]
							this.uploadLists.forEach(item=>{
								if(item.state==6)arr.push(item.url)
							})
							this.$emit("input", arr);
							this.$emit("imgDelete", {
								url: delObj.url,
								id: delObj.id
							});
						} else if (res.cancel) {}
					}
				});
			},
			delIndex(index){
				this.uploadLists.splice(index, 1)
			},
			clear(){
				this.uploadLists=[]
			},
			imgPreviewClick(item, index) {
				if (!this.islongPress) {
					this.imgPreview(item.url, index)
				}
			},
			imgPreview(url, index) {
				var imgData = this.uploadLists.filter(item => !/.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url)) //只预览图片的
				let arr = []
				imgData.forEach(item => {
					if (!item) return true
					if(typeof item.url==='object' && item.url!==null){
						if (item.url.url.indexOf('oss.') > 0) {
							arr.push(item.url.url + "&width=" + this.previewImageWidth + "&quality=70")
						} else {
							arr.push(item.url.url)
						}
					}else{
						if (item.url.indexOf('oss.') > 0) {
							arr.push(item.url + "&width=" + this.previewImageWidth + "&quality=70")
						} else {
							arr.push(item.url)
						}
					}
				})
				uni.previewImage({
					urls: arr,
					current: index,
					loop: true,
				});
			},
			chooseFile() {
				if (this.disabled) {
					return false;
				}
				this.uploadData=[]
				switch (this.mediaTypeData.indexOf(this.mediaType)) {
					case 1: //视频
						this.videoAdd();
						break;
					case 2: //全部
						uni.showActionSheet({
							itemList: ['相册', '视频'],
							success: (res) => {
								if (res.tapIndex == 1) {
									this.videoAdd();
								} else if (res.tapIndex == 0) {
									this.imgAdd();
								}
							},
							fail: (res) => {
							}
						});
						break;
					default: //图片
						this.imgAdd();
						break;
				}
			},
			videoAdd() {
				let nowNum = Math.abs(this.uploadLists.length - this.max);
				let thisNum = (this.chooseNum > nowNum ? nowNum : this.chooseNum) //可选数量
				uni.chooseVideo({
					compressed: this.compress,
					sourceType: this.sourceType,
					camera: this.camera,
					maxDuration: this.maxDuration,
					success: (res) => {
						this.chooseSuccessMethod([res.tempFilePath], 1)
					}
				});
			},
			imgAdd() {
				let nowNum = Math.abs(this.uploadLists.length - this.max);
				let thisNum = (this.chooseNum > nowNum ? nowNum : this.chooseNum) //可选数量
				// #ifdef APP-PLUS
				if (this.sourceType.length > 1) {
					uni.showActionSheet({
						itemList: ['拍摄', '从手机相册选择'],
						success: (res) => {
							if (res.tapIndex == 1) {
								this.appGallery(thisNum);
							} else if (res.tapIndex == 0) {
								this.appCamera();
							}
						},
						fail: (res) => {
						}
					});
				}
				if (this.sourceType.length == 1 && this.sourceType.indexOf('album') > -1) {
					this.appGallery(thisNum);
				}

				if (this.sourceType.length == 1 && this.sourceType.indexOf('camera') > -1) {
					this.appCamera();
				}
				// #endif
				//#ifndef APP-PLUS
				uni.chooseImage({
					count: thisNum,
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: this.sourceType,
					success: (res) => {
						this.chooseSuccessMethod(res.tempFilePaths, 0)
					}
				});
				// #endif
			},
			appCamera() {
				var cmr = plus.camera.getCamera();
				var res = cmr.supportedImageResolutions[0];
				var fmt = cmr.supportedImageFormats[0];
				cmr.captureImage((path) => {
						this.chooseSuccessMethod([path], 0)
					},
					(error) => {
					}, {
						resolution: res,
						format: fmt
					}
				);
			},
			appGallery(maxNum) {
				plus.gallery.pick((res) => {
					this.chooseSuccessMethod(res.files, 0)
				}, function(e) {
				}, {
					filter: "image",
					multiple: true,
					maximum: maxNum
				});
			},
			chooseSuccessMethod(filePaths, type) {
				if (this.action == '') { //未配置上传路径
					this.$emit("chooseSuccess", filePaths, type); //filePaths 路径 type 0 为图片 1为视频
					return
				}

				if (type == 1) {
					this.imgUpload(filePaths, type);
				} else {
					if (this.compress) { //设置了需要压缩
						this.imgCompress(filePaths, type);
					} else {
						this.imgUpload(filePaths, type);
					}
				}


			},
			imgCompress(tempFilePaths, type) {
				uni.showLoading({
					title: '压缩中...'
				});

				let compressImgs = [];
				let results = [];
				tempFilePaths.forEach((item, index) => {
					compressImgs.push(new Promise((resolve, reject) => {
						// #ifndef H5
						uni.compressImage({
							src: item,
							quality: this.quality,
							success: res => {
								results.push(res.tempFilePath);
								resolve(res.tempFilePath);
							},
							fail: (err) => {
								reject(err);
							},
							complete: () => {
								//uni.hideLoading();
							}
						})
						// #endif
						// #ifdef H5
						this.canvasDataURL(item, {
							quality: this.quality / 100
						}, (base64Codes) => {
							let tempPath = this.parseBlob(base64Codes)
							results.push(tempPath);
							resolve(tempPath);
						})
						// #endif
					}))
				})
				Promise.all(compressImgs) //执行所有需请求的接口
					.then((results) => {
						uni.hideLoading();
						this.imgUpload(results, type);
					})
					.catch((res, object) => {
						uni.hideLoading();
					});
			},
			imgUpload(tempFilePaths, type) {
				if (this.action == 'uniCloud') {
					this.uniCloudUpload(tempFilePaths, type)
					return
				}
				uni.showLoading({
					title: '上传中'
				});

				let that = this
				
				tempFilePaths.forEach(item=>{
					let obj = {
						id: that.guid(),
						state:0,
						progress: 0,
						url: item,
						data: []
					}
					// console.log(obj)
					that.uploadLists.push(obj)
					//that.$emit("uploadSuccess", obj)
				})
				

				let uploadImgs = []
				const arr=that.uploadLists.filter(u=>u.state==0)
				arr.forEach((item, index) => {
					const promise = new Promise((resolve, reject) => {
						
						let task = uni.uploadFile({
							url: that.action, //仅为示例，非真实的接口地址
							filePath: item.url,
							name: that.name,
							fileType: 'image',
							formData: that.formData,
							header: that.headers,
							success: (uploadFileRes) => {
								let obj=that.uploadLists.find(u=>u.id==task.id)
								if(obj)
								{
									const _res = JSON.parse(uploadFileRes.data);
									that.$set(obj, 'progress', 100)
									that.$set(obj, 'data', _res[0])
									that.$set(obj, 'state', 2)
									this.uploadData.push(obj)
									// that.$emit("uploadSuccess", obj)
								}
								
								resolve(obj)
							},
							fail: (err) => {
								reject(err);
								that.$emit("uploadFail", err);
							},
							complete: () => {}
						});
						task.id=item.id
						task.onProgressUpdate((res) => {
							if(res.progress!=100)
							{
								let obj=this.uploadLists.find(u=>u.id==task.id)
								if(obj)
								 {
									 that.$set(obj, 'progress', res.progress)
									 that.$set(obj, 'state', 1)
								 }
							}
							
						});
					})
					uploadImgs.push(promise)
				})
				Promise.all(uploadImgs) //执行所有需请求的接口
					.then((results) => {
						uni.hideLoading();
						//  console.log('uploadList',this.uploadLists)
						//  console.log('uploadData',this.uploadData)
						// // return
						// // uni.hideLoading();
						// // console.log(this.uploadLists,this.uploadData)
						// let arr=[]
						// this.uploadData.forEach(item=>{
						// 	let obj = this.uploadData.find(u=>u.id==item.id)
						// 	if(!obj) arr.push(item)
						// })
						// console.log(arr)
						that.$emit("uploadSuccess", this.uploadData)
					})
					.catch((res, object) => {
						uni.hideLoading();
						this.$emit("uploadFail", res);
					});

			},
			uniCloudUpload(tempFilePaths, type) {
				uni.showLoading({
					title: '上传中'
				});
				let uploadImgs = [];
				tempFilePaths.forEach((item, index) => {
					uploadImgs.push(new Promise((resolve, reject) => {

						uniCloud.uploadFile({
							filePath: item,
							cloudPath: this.guid() + '.' + this.getFileType(item, type),
							success(uploadFileRes) {
								if (uploadFileRes.success) {
									resolve(uploadFileRes.fileID);
								}
							},
							fail(err) {
								console.log(err)
								reject(err);
							},
							complete() {}
						});

					}))
				})
				Promise.all(uploadImgs) //执行所有需请求的接口
					.then((results) => {
						uni.hideLoading();

						uniCloud.getTempFileURL({
							fileList: results,
							success: (res) => {
								res.fileList.forEach(item => {
									//this.value.push(item.tempFileURL)
									// #ifndef VUE3
									this.value.push(item.tempFileURL)
									this.$emit("input", this.value);
									// #endif
									// #ifdef VUE3
									this.modelValue.push(item.tempFileURL)
									this.$emit("update:modelValue", this.modelValue);
									// #endif
								})
							},
							fail() {},
							complete() {}
						});
					})
					.catch((res, object) => {
						uni.hideLoading();
					});
			},
			getFileType(path, type) { //手机端默认图片为jpg 视频为mp4
				// #ifdef H5 
				var result = type == 0 ? 'jpg' : 'mp4';
				// #endif


				// #ifndef H5
				var result = path.split('.').pop().toLowerCase();
				// #ifdef MP 
				if (this.compress) { //微信小程序压缩完没有后缀
					result = type == 0 ? 'jpg' : 'mp4';
				}
				// #endif
				// #endif
				return result;
			},
			guid() {
				return 'xxxxxxxx-date-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
					var r = Math.random() * 16 | 0,
						v = c == 'x' ? r : (r & 0x3 | 0x8);
					return v.toString(16);
				}).replace(/date/g, function(c) {
					return Date.parse(new Date());
				});
			},
			/**
			 * 触发监听
			 * @param {Number} key 当前任务的下标
			 * @param {Object} uploadTask uni.uploadFile 的返回值
			 */
			triggerMonitor(key, uploadTask) {
				uploadTask.onProgressUpdate(res => {
					// 触发父组件/页面的监听事件
					this.uploadTaskProgress[key] = res;
					// 合并所有任务的列表用于父组件/页面的监听
					// {
					//	// 当前的进度
					// 	progress: 0,
					//	// 当前的所有进度 保存每条任务的进度等等
					// 	tasks: []
					// }
					this.uploadTask.onProgressUpdate(this.mergerProgress(this.uploadTaskProgress));
				})
			},
			/**
			 * 合并进度
			 * @param {Array} tasks 所有的任务
			 */
			mergerProgress(tasks) {
				var progress = 0;
				tasks.forEach((value, key) => {
					if (value) {
						progress += value.progress;
					} else {
						progress += 0;
					}
				})
				return {
					progress: progress / tasks.length,
					tasks
				}
			},
			/**
			 * 设置父组件/页面的监听事件
			 * onProgressBegin 开始监听触发事件
			 * onProgressUpdate 进度变化事件
			 * onProgressEnd 结束监听事件
			 * [req 1,req 2...]
			 * @param {Object} obj {onProgressUpdate,onProgressBegin,onProgressEnd}
			 */
			setUpMonitor(obj) {
				this.uploadTask = {
					load: true,
					...obj
				}
			},
			canvasDataURL(path, obj, callback) {
				var img = new Image();
				img.src = path;
				img.onload = function() {
					var that = this;
					// 默认按比例压缩
					var w = that.width,
						h = that.height,
						scale = w / h;
					w = obj.width || w;
					h = obj.height || (w / scale);
					var quality = 0.8; // 默认图片质量为0.8
					//生成canvas
					var canvas = document.createElement('canvas');
					var ctx = canvas.getContext('2d');
					// 创建属性节点
					var anw = document.createAttribute("width");
					anw.nodeValue = w;
					var anh = document.createAttribute("height");
					anh.nodeValue = h;
					canvas.setAttributeNode(anw);
					canvas.setAttributeNode(anh);
					ctx.drawImage(that, 0, 0, w, h);
					// 图像质量
					if (obj.quality && obj.quality <= 1 && obj.quality > 0) {
						quality = obj.quality;
					}
					// quality值越小，所绘制出的图像越模糊
					var base64 = canvas.toDataURL('image/jpeg', quality);
					// 回调函数返回base64的值
					callback(base64);
				}
			},
			showImg(url, size = 200, quality = 70) {
				let urlTmp;
				if(typeof url==='object'){
					urlTmp=url.url
				}else{
					urlTmp=url
				}
				if (urlTmp.indexOf('oss.') > 0) {
					let str = urlTmp
					str += "&width=" + this.width
					str += "&height=" + this.height
					if (quality) str += "&quality=" + quality
					return str
				} else {
					return urlTmp
				}
			},

			parseBlob(base64) {
				var arr = base64.split(',');
				var mime = arr[0].match(/:(.*?);/)[1];
				var bstr = atob(arr[1]);
				var n = bstr.length;
				var u8arr = new Uint8Array(n);
				for (var i = 0; i < n; i++) {
					u8arr[i] = bstr.charCodeAt(i);
				}
				var url = URL || webkitURL;
				return url.createObjectURL(new Blob([u8arr], {
					type: mime
				}));

			},
			longpress(item, index, type) {
				let itemList = ['预览']
				if (this.remove) itemList.push('删除')
				let that = this
				that.islongPress = true
				uni.showActionSheet({
					itemList: itemList,
					success: function(res) {
						if (res.tapIndex == 0) {
							switch (type) {
								case 1: //视频
									that.previewVideo(item.url)
									break
								case 0:
									that.imgPreview(item.url, index)
									break
							}
						} else if (res.tapIndex == 1) {
							that.imgDel(item,index)
						}
					},
					fail: function(res) {
					}
				});
				// uni.showModal({
				// 	title:'长按'+index
				// })
			},
			touchstart(item, index, type) {
				if (!this.longPress && type != 1) return
				this.timer = setTimeout(() => {
					this.longpress(item, index, type)
				}, 1000)
			},
			touchend() {
				clearTimeout(this.timer)
				setTimeout(() => {
					this.islongPress = false
				}, 200)
			}
		}
	}
</script>

<style>
	.image-real {
		animation: fadenum .3s 1;
	}

	@keyframes fadenum {
		0% {
			opacity: 0;
		}

		100% {
			opacity: 0.7;
		}
	}

	.ut-image-upload-Item-del.image-show {
		opacity: 0.7;
		animation: fadenum .8s 1;
	}

	.image-loading-box {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.image-loading {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.image-loading .loader {
		color: #999;
		font-size: 20rpx;
	}

	.loaders {
		display: flex;
		justify-content: center;
		font-size: 16rpx;
		margin-bottom: 20rpx;
		opacity: 0.9;
	}

	.loader2 {
		border: 4rpx solid #f3f3f3;
		border-radius: 50%;
		border-top: 4rpx solid var(--colors);

		animation: spin2 1s linear infinite;
	}

	@keyframes spin2 {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.preview-full {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 100%;
		z-index: 10001;
	}

	.preview-full video {
		width: 100%;
		height: 100%;

	}

	.preview-full-close {
		position: fixed;
		left: 32rpx;
		top: 30rpx;
		width: 80rpx;
		height: 80rpx;
		line-height: 60rpx;
		text-align: center;
		z-index: 3004;
		/* 	background-color: #808080; */
		color: #fff;
		font-size: 65rpx;
		font-weight: bold;
		text-shadow: 1px 2px 5px rgb(0 0 0);
	}



	/* .preview-full-close-before,
	.preview-full-close-after {
		position: absolute;
		top: 50%;
		left: 50%;
		content: '';
		height: 60rpx;
		margin-top: -30rpx;
		width: 6rpx;
		margin-left: -3rpx;
		background-color: #FFFFFF;
		z-index: 20000;
	}

	.preview-full-close-before {
		transform: rotate(45deg);

	}

	.preview-full-close-after {
		transform: rotate(-45deg);

	} */

	.ut-image-upload-list {
		display: flex;
		flex-wrap: wrap;
	}

	.ut-image-upload-Item {
		/* 		width: 160rpx;
		height: 160rpx; */
		/* border-radius: 8rpx; */
		/* margin-right: 10rpx; */
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		/* margin-bottom: 10rpx */
	}
	
	.ut-image-upload-Item:last-child{
		margin-right: 0;
	}

	.ut-image-upload-Item image {
		width: 100%;
		height: 100%;
	}

	.ut-image-upload-Item-video {
		width: 100%;
		height: 100%;
		position: relative;
		overflow: hidden;
	}


	.ut-image-upload-Item-video-fixed {
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		z-index: 1;

	}

	.ut-image-upload-Item video {
		width: 100%;
		height: 100%;
		border-radius: 10rpx;

	}

	.ut-image-upload-Item-add {
		font-size: 105rpx;
		/* line-height: 160rpx; */
		text-align: center;
		border: 1px dashed #d9d9d9;
		color: #d9d9d9;
		width:100%;
		height: 100%;
		/* line-height: 160rpx; */
	}

	.ut-image-upload-Item-del {
		background-color: var(--colors);
		font-size: 24rpx;
		position: absolute;
		width: 35rpx;
		height: 35rpx;
		line-height: 35rpx;
		text-align: center;
		top: 0;
		right: 0;
		z-index: 9;
		color: #fff;
		opacity: 0.7;

	}



	.ut-image-upload-Item-del-cover {
		background-color: var(--colors);
		font-size: 24rpx;
		position: absolute;
		width: 35rpx;
		height: 35rpx;
		text-align: center;
		top: 0;
		right: 0;
		color: #fff;
		/* #ifdef APP-PLUS */
		line-height: 25rpx;
		/* #endif */
		/* #ifndef APP-PLUS */
		line-height: 35rpx;
		/* #endif */
		z-index: 2;
		opacity: 0.7;
	}
	
	.ut-image-upload-progress, .ut-image-upload-progress-text{
		position: absolute;
		left:0;
		top: 0;
		color: var(--colors);
		width: 100%;
		text-align: center;
		
	}
	
	.ut-image-upload-progress{
		background-color: #fff;
	}
</style>
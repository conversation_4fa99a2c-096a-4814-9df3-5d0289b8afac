<template>
	<view class="ut-top-wrap" :style="{paddingTop:statusHeight+'px',height:wrapHeight+'px','--status':-statusHeight+'px'}">
		<view class="ut-top-nav" :style="{paddingTop: top+'px',backgroundColor:bgColor}">
			<slot name="default"></slot>
		</view>
	</view>
</template>

<script>
	import {mapState} from 'vuex'
	export default {
		name:'ut-top',
		props:{
			bgColor:{
				type:String,
				default:'none',
			},
			full:{
				type:Boolean,
				default:false,
			}
		},
		options: {
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "isolated",  //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true,  //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
		},
		data() {
			return {
				h:0,
			}
		},
		options: {
			styleIsolation: 'shared'
		},
		computed:{
			...mapState({
				statusHeight: state => state.init.statusHeight,
			}),
			top(){
				if(this.full) return 0
				return  this.statusHeight
			},
			wrapHeight(){
				if(this.full) return 0
				return this.h
			}
			
		},
		mounted() {
			const query=uni.createSelectorQuery().in(this)
			query.select('.ut-top-nav').boundingClientRect(data=>{
				this.h=data.height
				this.$emit('topHeight',this.h,this.statusHeight)
			}).exec()
		},
	
	}
</script>
<style>
	.ut-top-nav .f-navbar{
		margin-top: var(--status);
	}
	
</style>
<style lang="scss" scoped>
	.ut-top-wrap{
		// z-index: 1;
		// position: fixed;
		// top: 0; /* css变量 */
		// left: 0;
		width: 100%;
	}
	.ut-top-nav{
		position: fixed;
		top: 0;
		left:0;
		z-index: 100;
		width: 100%;	
	}
</style>
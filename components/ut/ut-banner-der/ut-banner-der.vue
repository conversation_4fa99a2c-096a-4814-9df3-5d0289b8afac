<template>
	<view class="back" :style="[backstyle()]">
		<ut-banner :detail="datail" :show-dots="showDots" :width="width" :height="height" :image-name="imageName" @getIndex="getIndex" :showDots="showDots"></ut-banner>
	</view>
</template>

<script>
	export default {
		name: "utBannerDer",
		components:{
		},
		data() {
			return {
				imgCurrIndex: 0,
				showDots:false // 是否显示指示点
			};
		},
		props: {
			datail: {
				type: Array,
				default: function() {
					return []
				}
			},
			imageName:{
				type:String,
				default:'image'
			},
			bgName:{
				type:String,
				default:'bg'
			},
			width:{
				type:Number,
				default:750,
			},
			height:{
				type:Number,
				default: 300,
			},
			
		},
		methods: {
			getIndex(index) {
				this.imgCurrIndex = index
			},
			backstyle() {
				return {
					backgroundImage: this.getImageUrl()
				}
			},
			getImageUrl() {
				if(this.datail.length == 0){
					return "";
				}
				const url = this.datail[this.imgCurrIndex][this.bgName] || ''
				return `url(${url})`
			}
		}
	}
</script>

<style lang="less">
	.back {
		// padding: 20rpx 0;
		box-sizing: border-box;
		transition: all 0.6s ease-in-out 0s;
		background-size: 100% 100%;
		overflow-x: hidden;
	}
</style>

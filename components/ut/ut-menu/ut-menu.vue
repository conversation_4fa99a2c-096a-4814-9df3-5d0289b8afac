<template>
	<!-- 产品分类导航 -->
	<view class="menu-category-box" v-if="carousel.length">
		<swiper
			class="menu-swiper-box"
			:style="{height:boxHeight+'rpx'}"
			@change="onSwiper"
			circular
			:autoplay="false"
			:interval="3000"
			:duration="1000"
		>
			<swiper-item class="menu-swiper-item" v-for="(itemList, indexList) in carousel" :key="indexList">

				<view class="menu-tab-box" :style="{justifyContent: list.length<=rowCount?'space-around':''}">
					<view class="item-box" 
						:style="{ width: viewWidht + '%',paddingBottom: itemList.length>rowCount && index<rowCount?'20rpx':''}" 
						v-for="(item,index) in itemList" 
						:key="index" 
						@tap="routerTo(item)">
						
						<view v-if="item.cmd=='contact'" class="contact-box">
							<button open-type="contact" class="btn-contact" bindcontact="test" session-from="sessionFrom">
								<image class="item-image" :style="{ width: imageSize + 'rpx', height: imageSize + 'rpx',borderRadius:shape=='circle'?'500rpx':imageRadius+'rpx' }" :src="item.image"></image>
								<text class="item-title">{{ item.title }}</text>
							</button>
						</view>
						<template v-else>
							<image class="item-image" :style="{ width: imageSize + 'rpx', height: imageSize + 'rpx',borderRadius:shape=='circle'?'500rpx':imageRadius+'rpx' }" :src="item.image"></image>
							<text class="item-title">{{ item.title }}</text>
						</template>
						
					</view>
				</view>
			</swiper-item>
		</swiper>
		<view class="menu-category-dots" v-if="carousel.length > 1">
			<text class="category-dot" :class="{'category-dot-active':categoryCurrent === index}" v-for="(dot, index) in carousel.length" :key="index"></text>
		</view>
	</view>
</template>

<script>
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex'
export default {
	components: {},
	data() {
		return {
			categoryCurrent: 0 //分类轮播下标
		};
	},
	props: {
		list: {
			type: Array,
			default: ()=>[]
		},
		rowCount: {
			type:Number,
			default: 5
		},
		imageSize: {
			type: Number,
			default: 72
		},
		imageRadius: {
			type: Number,
			default: 6
		},
		shape:{
			type:String,
			default:''
		},
	},
	computed: {
		carousel() {
			if (this.list) {
				let data = this.sortData(this.list, this.rowCount * 2);
				return data;
			}
		},
		viewWidht(){
			switch(this.rowCount){
				case 1: return 100 
				case 2: return 50
				case 3: return 33.33
				case 4: return 25
				case 5: return 20
				case 6: return 16.66
				case 7: return 14.28
				case 8: return 12.5
				case 9: return 11.11
				case 10: return 10
				default: return 25
			}
		},
		boxHeight(){
			if(this.list.length <= this.rowCount){ 
				return this.imageSize+10+30
			}else{
				return (this.imageSize+10+30)*2+20
			}
		},
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			city: state => state.init.city,
			community: state => state.init.community,
			locationAddress: state => state.init.locationAddress,
			userInfo: state => state.user.info,
		}),
	},
	created() {},
	methods: {
		// 数据分层
		sortData(oArr, length) {
			let arr = [];
			let minArr = [];
			oArr.forEach(c => {
				if (minArr.length === length) {
					minArr = [];
				}
				if (minArr.length === 0) {
					arr.push(minArr);
				}
				minArr.push(c);
			});

			return arr;
		},
		// 轮播
		onSwiper(e) {
			this.categoryCurrent = e.detail.current;
		},
		// 路由跳转
		routerTo(item) {
			if(item.checkLogin && !this.token){
				this.$store.commit('LOGIN_TIP', true)
				this.$emit('check',['login'])
				return
			}
			if(item.checkPhone && !this.userInfo.phone){
				this.$store.commit('PHONE_TIP', true)
				this.$emit('check',['phone'])
				return
			}
			if(item.checkCode){
				let arr=item.checkCode.split(',')
				var isGo=true
				arr.forEach(o=>{
					let obj=this.$store.state.init[o]
					if(!obj) isGo=false
				})
				if(!isGo){	
					this.$emit('check',arr)
					return
				}
			}
			if(item.cmd) return
			if(!item.path) return
			this.$tools.routerTo(item.path);
		}
	}
};
</script>

<style lang="scss">
.menu-category-box,
.menu-swiper-box {
	position: relative;
	.menu-tab-box {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		.item-box {
			display: flex;
			flex-direction: column;
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: rgba(51, 51, 51, 1);
			align-items: center;

			.item-image {
				margin-bottom: 10rpx;
			}
			
			.item-title{
			
				overflow: hidden;//溢出隐藏
				white-space: nowrap;//禁止换行
				text-overflow: ellipsis;//...
				padding: 0 10rpx;
			}
		}
	}

	.menu-category-dots {
		display: flex;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		bottom: 20rpx;

		.category-dot {
			width: 40rpx;
			height: 3rpx;
			background: #eeeeee;
			margin-right: 10rpx;
		}

		.category-dot-active {
			width: 40rpx;
			height: 3rpx;
			background: #a8700d;
			margin-right: 10rpx;
		}
	}
	
	.contact-box{
		width: 100%;
	}
	
	.btn-contact{
		border: none;
		padding: 0;
		background: none;
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 12px;
		font-weight: 500;
		line-height: 32rpx !important;
		border-radius: 0;
		color: #333;
		font-family: PingFang SC;
		
		&::after{
			content: none;
		}
	}
}
</style>

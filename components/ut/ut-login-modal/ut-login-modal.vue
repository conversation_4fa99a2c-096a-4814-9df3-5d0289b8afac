<template>

	<view class="cu-modal" v-if="showLogin && !screenShot" :class="[{ show: showLogin }, modalType]" cathctouchmove
		@tap="hideModal">
		<view class="cu-dialog" @tap.stop style="background: none;overflow: visible;">
			<view class="modal-box">
				<image class="head-bg" src="https://oss.afjy.net/api/file/preview?file=eWtn09p.png&width=750" mode="">
				</image>
				<view class="detail">
					<view class="title1">您还没有登录</view>
					<view class="title2">登录即刻开启品质生活</view>
				</view>
				<view class="btn-box y-f">
					<!-- #ifndef MP-WEIXIN  -->
					<button class="cu-btn login-btn" @tap="$shaken(onLogin)">立即登录</button>
					<!-- #endif  -->
					<!-- #ifdef MP-WEIXIN  -->
					<button class="cu-btn login-btn" v-if="canIUseGetUserProfile" @click="$shaken(wxLogin)"> 授权并登录
					</button>
					<button class="cu-btn login-btn" v-else open-type="getUserInfo" @getuserinfo="getUserInfo"> 授权并登录
					</button>
					<!-- <button class="cu-btn login-btn" open-type="getUserInfo" @getuserinfo="xcxWxLogin">授权并登录</button> -->
					<!-- #endif  -->
					<button class="cu-btn close-btn" @tap="hideModal">取消</button>
				</view>
			</view>
		</view>
	</view>




</template>

<script>
	// import Wechat from '@/common/wechat/wechat';
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex';
	import wechat from '@/common/wechat/wechat';
	import $platform from "@/common/platform";
	export default {
		name: 'ut-login-modal',
		components: {},
		data() {
			return {
				userInfo: {},
				hasUserInfo: false,
				canIUseGetUserProfile: false,
				noClick: true,
				screenShot: uni.getStorageSync('screenShot')
			};
		},
		props: {
			value: {
				type: Boolean,
				default: false
			},
			modalType: {
				type: String,
				default: ''
			},
			colors: {
				type: String
			}
		},
		computed: {
			...mapState({
				loginTip: state => state.user.loginTip,
				commKey: state => state.init.template.commKey,
				websocket: state => state.websocket.socketTask,
			}),
			showLogin: {
				get() {
					return this.loginTip;
				},
				set(val) {
					this.$store.commit('LOGIN_TIP', val);
				}
			},
		},
		onLoad() {


		},
		mounted() {
			// #ifdef MP
			if (wx.getUserProfile) {
				this.setData({
					canIUseGetUserProfile: true
				})
			}
			// #endif
		},
		methods: {
			...mapActions(['setToken', 'setUser']),

			// 隐藏登录弹窗
			hideModal() {
				this.showLogin = false;
			},
			// 去登录
			async onLogin() {
				this.hideModal()
				let token = ''
				if ($platform.get() === "wxOfficialAccount") {
					await wechat.login();
				} else {
					this.showLogin = false;
					uni.setStorageSync('lastPage', this.$Route);
					uni.redirectTo({
						url: '/pages/login/login'
					})

				}
			},
			mpCodeToApiToken(code, userInfo) { //根据code换取自家系统token
				let params = {
					commKey: this.commKey,
					code: code,
					nickname: userInfo.nickName,
					headimgurl: userInfo.avatarUrl
				}
				
				uni.showLoading({
					title: '登录中...'
				})
				this.$ut.api('comm/login/mp', params).then(res => {
					this.setToken(res.data);
					setTimeout(() => {}, 500)
					this.$ut.api('comm/login/myinfo').then(res2 => {
						let userInfo=res2.data
						this.setUser(userInfo)
						this.websocket.on('connected', function(res) {
							let param = {
								id: userInfo.id,
								nickname: userInfo.nickname,
								phone: userInfo.phone,
								headimg: userInfo.headimgurl,
								gender: userInfo.gender,
							}
							this.websocket.invoke('setMyInfo', param)
						})
						setTimeout(() => {
							uni.hideLoading()
							uni.showTabBar()
							uni.showToast({
								title: '登陆成功'
							})
						}, 500)

					})
				})
			},
			//小程序登录方式1（优先）
			wxLogin() {
				wechat.login().then((res) => {
					this.mpCodeToApiToken(res.code, res.userInfo)
				})
			},
			// 小程序登录方式2
			async getuserinfo(e) {
				await wechat.login().then((res) => {
					this.mpCodeToApiToken(res.code, res.userInfo)
				})
			},


		}
	};
</script>

<style lang="scss">
	// 登录提示
	.modal-box {
		width: 610rpx;
		border-radius: 20rpx;
		background: #fff;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 30rpx;

		.head-bg {
			width: 100%;
			height: 210rpx;
		}

		.detail {
			.title1 {
				color: #46351b;
				font-size: 35rpx;
				font-weight: bold;
			}

			.title2 {
				font-size: 28rpx;
				color: #999;
				padding-top: 20rpx;
			}
		}

		.btn-box {
			margin-top: 80rpx;

			.login-btn {
				width: 492rpx;
				height: 70rpx;
				background: linear-gradient(90deg, var(--colors), var(--colors2));
				box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
				border-radius: 35rpx;
				font-size: 28rpx;
				color: rgba(#fff, 0.9);
			}

			.close-btn {
				width: 492rpx;
				height: 70rpx;
				color: var(--colors);
				font-size: 26rpx;
				margin-top: 20rpx;
				background: none;
			}
		}
	}

	// 小程序登录提醒
	/* #ifdef MP-WEIXIN */
	.force-login-wrap {
		position: fixed;
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		z-index: 11111;
		top: 0;
		// background: linear-gradient(180deg, var(--colors), var(--colors2));
		background: #efefef;

		.logo-bg {
			width: 640rpx;
			height: 300rpx;
		}

		.force-login__content {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);

			.user-avatar {
				display: block;
				width: 160rpx;
				height: 160rpx;
				border-radius: 50%;
				overflow: hidden;
				margin-bottom: 40rpx;
			}

			.user-name {
				font-size: 35rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: var(--colors);
				margin-bottom: 30rpx;
			}

			.login-notice {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: var(--colors);
				line-height: 44rpx;
				width: 400rpx;
				text-align: center;
				margin-bottom: 80rpx;
			}

			.author-btn {
				width: 630rpx;
				height: 80rpx;
				background: linear-gradient(90deg, var(--colors), var(--colors2));
				box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
				border-radius: 40rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: rgba(255, 255, 255, 1);
			}

			.close-btn {
				width: 630rpx;
				height: 80rpx;
				margin-top: 30rpx;
				border-radius: 40rpx;
				border: 2rpx solid var(--colors);
				background: none;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: var(--colors);
			}
		}
	}

	/* #endif */
</style>
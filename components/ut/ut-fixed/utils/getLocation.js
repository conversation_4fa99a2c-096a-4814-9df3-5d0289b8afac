import getTypeValue from './getTypeValue';
import noop from './noop';

const cacheTime = 35 * 1000;
let beforeTime = 0;
let cacheLocation = null;

/**
 * @description: 获取地理位置信息，由于微信小程序从基础库2.17.0版本起 30秒内重复调用wx.getLocation会返回错误,因此使用此方法获取，在35秒内重复调用会使用缓存的成功信息
 * @param {object} params
 * @example 同 uni.getLocation
 */
export default function getLocation(params) {
  const nowTime = Date.now();
  const diffTime = nowTime - beforeTime;
  beforeTime = nowTime;
  params = getTypeValue(params, Object, {});
  const success = getTypeValue(params.success, Function, noop);
  const complete = getTypeValue(params.complete, Function, noop);
  params.success = function (location) {
    cacheLocation = location;
    success(location);
    complete(location);
  };
  if (diffTime < cacheTime && cacheLocation) {
    params.success(cacheLocation);
  } else {
    uni.getLocation(params);
  }
}

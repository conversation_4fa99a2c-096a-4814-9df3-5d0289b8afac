/*
 * @Description: 返回组件options项设置
 * @Author: 无痕
 * @Email: <EMAIL>
 * @Date: 2021-08-24 11:56:53
 * @LastEditTime: 2021-10-27 13:39:27
 * @LastEditors: 无痕
 */
import getTypeValue from '../utils/getTypeValue';

export default function getComponentOptions(options = {}) {
  return {
    // #ifdef MP-WEIXIN || MP-QQ
    multipleSlots: getTypeValue(options.multipleSlots, Boolean, true), //  在组件定义时的选项中启动多slot支持，默认启用
    addGlobalClass: getTypeValue(options.addGlobalClass, Boolean, true), //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
    virtualHost: getTypeValue(options.virtualHost, Boolean, true), //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
    styleIsolation: getTypeValue(options.styleIsolation, String, 'shared'), //  表示页面 wxss 样式将影响到自定义组件，自定义组件 wxss 中指定的样式也会影响页面和其他设置了 apply-shared 或 shared 的自定义组件
    // #endif
  };
}

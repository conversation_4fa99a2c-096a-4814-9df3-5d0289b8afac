<template>
	<view>
		<view class="flex justify-start">
			<view>
				<!-- <u--image src="" v-if="!detail.imgHead" width="160" height="200" border-radius="4"></u--image> -->
				<text v-if="!detail.imgHead" class="cuIcon-people" />
				<u-lazy-load v-else :image="$tools.showImg(detail.imgHead,400)" width="280" height="360" border-radius="4" @click="$tools.previewImage([detail.imgHead],0,800)" />
			</view>
			<view class="padding-lr-sm text-content flex-sub text-black">
				<view class="text-lg text-bold padding-bottom">{{detail.name}}</view>
				<view><text class="detail-label">电话：</text>{{detail.phone}}</view>
				<view class="flex justify-between">
					<text v-if="detail.sex==1"><text class="detail-label">性别：</text>男</text>
					<text v-else-if="detail.sex==2"><text class="detail-label">性别：</text>女</text>
					<text v-else><text class="detail-label">性别：</text>(不详)</text>
					<text><text class="detail-label">民族：</text>{{detail.nation || ''}}</text>
				</view>
				<view><text class="detail-label">档案号：</text>{{detail.archives || ''}}</view>
				<view><text class="detail-label">出生日期：</text>{{detail.birthday || ''}}</view>
				<view><text class="detail-label">学历：</text>{{detail.education || ''}}</view>
				<view><text class="detail-label">婚姻：</text>{{detail.marriage || ''}}</view>
				
			</view>
		</view>
		<view v-if="!showAll && !more" class="flex justify-center padding-tb-sm" :style="{color:colors}">
			<text @click="more=true">展开显示更多</text>
			<u-icon name="arrow-down" :color="colors"  @click="more=true"/>
		</view>
		<view v-if="more || showAll">
			<u-transition :show="true">
			<view class="grid col-2 text-content text-black">
				<view><text class="detail-label">居住状态：</text>{{detail.liveState || ''}}</view>
				<view><text class="detail-label">失能等级：</text>{{detail.disabilityLevelName || ''}}</view>
				<view><text class="detail-label">政治面貌：</text>{{detail.politics || ''}}</view>
				<view><text class="detail-label">方言：</text>{{detail.dialect || ''}}</view>
				<view><text class="detail-label">宗教：</text>{{detail.religion || ''}}</view>
				<view><text class="detail-label">类型：</text>{{detail.typeName || ''}}</view>
				<view><text class="detail-label">护理级别：</text>{{detail.levelName || ''}}</view>
			</view>
			<view class="text-content text-black">
				<view><text class="detail-label">证件号：</text>{{detail.idcard || ''}}</view>
				<view><text class="detail-label">地址：</text>{{detail.address || ''}}</view>
				<view><text class="detail-label">备注：</text>{{detail.remark || ''}}</view>
			</view>
			<view class="text-content flex justify-between text-black">
				<view class="btn-wrapper"><u-button type="primary" :disabled="!fileData || !fileData.length" shape="circle" :color="colors" text="查看资料" :plain="true" size="small" @click="showFileData=true"></u-button></view>
				<view class="btn-wrapper"><u-button type="primary" :disabled="!detail.longitude || !detail.latitude" shape="circle" :color="colors" text="地图显示" :plain="true" size="small" @click="openMap"></u-button></view>
			</view>
			</u-transition>
		</view>
		<view v-if="!showAll && more" class="flex justify-center padding-tb-sm" :style="{color:colors}">
			<text @click="more=false">收起</text>
			<u-icon name="arrow-up" :color="colors"  @click="more=false"/>
		</view>
		
		<u-popup :show="showFileData" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="998" :overlay-style="{zIndex:998}" @close="showFileData=false">
			<view class="pop-title">资料信息</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<view style="max-height: 80vh;">
					<template v-for="(item,index) in fileData">
						<file-data-item :key="index" :detail="item" :only-view="true"></file-data-item>
					</template>
				</view>
			</scroll-view>
		</u-popup>
		
	</view>
</template>

<script>
	
import FileDataItem from './file-data-item.vue'
export default {
	components: {
		FileDataItem,
	},
	props:{
		colors:{
			type:String,
			default:'',
		},
		detail:{
			type:Object,
			default:()=>{},
		},
		showAll:{
			type:Boolean,
			default:false,
		},
		fileData:{
			type:Array,
			default:()=>[],
		},
	},
	data() {
		return {
			more: false,
			showFileData:false,
		}
	},
	computed:{
		position() {
			return {
				latitude: this.detail.latitude,
				longitude: this.detail.longitude,
				name: this.detail.name,
				address: this.detail.address,
			}
		},
	},
	methods:{
		openMap(){
			this.$wxsdk.openLocation(this.position);
		}
	}
}
</script>

<style scoped lang="scss">
.cuIcon-people{
	font-size: 290rpx;
	color: gray;
	border: 1px solid #ccc;
	width: 290rpx;
	height: 364rpx;
	line-height: 364rpx;
	border-radius: 6rpx;
	display: block;
}

.btn-wrapper{
	padding: 20rpx;
	width:35%;
}

.detail-label{
	font-size: 22rpx;
	color: gray;
}

.pop-title {
	text-align: center;
}

.text-content{
	line-height: 1.8 !important;
}

</style>
<template>
	<view class="item-box flex justify-between margin-tb padding">
		<view class="head-image" @click="$emit('detail',detail)">
			<u-lazy-load v-if="detail.imgHead" :image="$tools.showImg(detail.imgHead)" width="120" height="120" border-radius="4" />
		</view>
		<view class="content text-content flex justify-between align-center">
			<view class="flex-sub">
				<view class="flex justify-start align-center">
					<view class="text-df text-bold margin-right-xl" >{{detail.name}}</view>
					<view class="text-sm text-blue" v-if="detail.sex==1">男<text class="cuIcon-male" /></view>
					<view class="text-sm text-pink" v-if="detail.sex==2">女<text class="cuIcon-female" /></view>
				</view>
				<view v-if="detail.phone" style="text-sm text-gray"><text class="cuIcon-phone" />{{detail.phone}}</view>
				<view class="text-sm text-gray">{{detail.address}}</view>
			</view>
			<view>
				<u-button type="primary" shape="circle" :color="colors" text="选择" size="small" @click="$emit('select',detail)"></u-button>
			</view>
		</view>
		
	</view>
</template>

<script>
export default {
	components: {
	},
	props:{
		colors:{
			type:String,
			default:'',
		},
		detail:{
			type:Object,
			default:()=>{},
		}
	},
	data() {
		return {
			more: false,
		}
	},
}
</script>

<style scoped lang="scss">
.item-box{
	.content{
		flex: 1;
		padding: 0 20rpx;
	}
	
}
</style>
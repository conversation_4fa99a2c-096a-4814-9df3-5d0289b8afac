export default {
    props: {
	   authCamera: {
			type: Boolean,
			default: true
	   },
	   quality: {
			type: String,
			default: 'high'
	   },
	   compress: {
		   type: Number,
		   default: 85
	   },
	   devicePosition: {
			type: String,
			default: 'front'
	   },
	   tipsText: {
	     type: String,
	     default: '请靠近取景框'
	   },
	   frameWidth:{
		   type:Number,
		   default:650,
	   },
	   frameHeight:{
		   type:Number,
		   default:800,
	   },
	   showAvater:{
		   type:Boolean,
		   default:true,
	   },
	   savePhotosAlbum:{
		   type:Boolean,
		   default:true,
	   }
    }
}

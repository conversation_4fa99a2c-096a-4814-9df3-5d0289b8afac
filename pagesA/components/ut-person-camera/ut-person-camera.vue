<template>
	<view class="content" :class="{ 'camera-page': true, 'safety-zone-bottom': phoneSafeArea }"
		style="position: fixed;top: 0;left: 0;z-index: 999;width: 100%;height: 100vh;background-color: #000;"
		:style="{'--aperture-w':frameWidth+'rpx','--aperture-h':frameHeight+'rpx',paddingTop:systemInfo.statusBarHeight+'px'}">
		<!-- #ifndef H5 -->
		<camera @error="handleCameraError" :device-position="position" flash="off" resolution="high"
			output-dimension="max" v-if="!cameraImage && !filePath" class="camera-box" style="width: 100%; height: calc(100vh - 180rpx);">
			<cover-image v-show="pageWidth && showAvater && !filePath" :src="require('./image/avater.png')"
				style="width: 580rpx; height: 500rpx; margin:15vh 12vw 0 12vw;"></cover-image>
			<cover-view v-show="pageWidth && !filePath && !cameraImage" class="camera-aperture" id="frameDom"></cover-view>
			<cover-view v-show="pageWidth && !filePath && !cameraImage" class="camera-aperture-line camera-aperture-top"></cover-view>
			<cover-view v-show="pageWidth && !filePath && !cameraImage" class="camera-aperture-line camera-aperture-right"></cover-view>
			<cover-view v-show="pageWidth && !filePath && !cameraImage" class="camera-aperture-line camera-aperture-bottom"></cover-view>
			<cover-view v-show="pageWidth && !filePath && !cameraImage" class="camera-aperture-line camera-aperture-left"></cover-view>
			<cover-view v-show="pageWidth && tipsText && !filePath && !cameraImage" class="camera-aperture-tips">{{ tipsText }}</cover-view>
		</camera>

		<!-- #endif -->
		<!-- #ifdef H5 -->
		<view  class="camera-box h5Text" style="width: 100%; height: calc(100vh - 180rpx);">
			h5页面请更换拍照模式
		</view>
		<!-- #endif -->

		<!-- 确认拍照照片 -->
		<view v-if="cameraImage" class="image-box" style="width: 100%; height: calc(100vh - 180rpx);">
			<image :src="cameraImage" style="width: 100%; " :style="{height:750*frameHeight / frameWidth+'rpx'}"></image>
		</view>
		<!-- 准备拍照 -->
		<view v-if="!cameraImage && pageWidth " class="btns"
			style="width: 100%;height: 180rpx;background: #3B4144; ">
			<image class="item" @tap="chooseImage" :src="require('./image/xiangce.png')"></image>
		    <!-- #ifndef H5 -->
			<image class="item_center" @tap="takePhotoByHead" :src="require('./image/paizhaoanniu.png')"></image>
			<!-- #endif -->
			
			<image class="item" @tap="reverseCamera" :src="require('./image/fanzhuan.png')"></image>
		</view>
		<!-- 拍照完成 -->
		<view v-if="cameraImage" class="btns"
			style="width: 100%;height: 180rpx;background: #3B4144; ">
			<image class="img_btn" @tap="cameraImage = ''" :src="require('./image/restart.png')"></image>
			<image class="img_btn"></image>
			<image class="img_btn" @tap="confirmPhoto" :src="require('./image/confirm.png')"></image>
		</view>
		<view class="error-handler" v-if="!authCamera">
			<button class="nobtn" openType="openSetting">获取相机权限失败</button>
		</view>

		 <cropper-watermark v-if='filePath' watermark="" :watermarkType="1" mode="ratio" :width="362" :height="445" :maxWidth="1024" :maxHeight="1024" :url="filePath" @cancel="oncancel" @ok="onok"></cropper-watermark>

		<canvas v-if="use2d" type="2d" id="myCanvas" class="camera-canvas" :style="{
				width: `${canvasW}px`,
				height: `${canvasH}px`
			}"></canvas>
		<canvas v-else id="myCanvas" canvas-id="myCanvas" class="camera-canvas" :style="{
				width: `${canvasW}px`,
				height: `${canvasH}px`
			}"></canvas>

	</view>
</template>

<script>
import props from './props.js'
import CropperWatermark from './cropper-watermark'
export default {
	mixins: [props],
	components:{
		CropperWatermark,
	},
	data() {
		return {
			systemInfo:{
			    statusBarHeight:uni.getSystemInfoSync().statusBarHeight,
			    // #ifdef MP-ALIPAY
			    navBarH: uni.getSystemInfoSync().statusBarHeight + uni.getSystemInfoSync().titleBarHeight, //菜单栏总高度--单位px
			    titleBarHeight: uni.getSystemInfoSync().titleBarHeight, //标题栏高度--单位px
			    // #endif
			    // #ifndef MP-ALIPAY
			    navBarH: uni.getSystemInfoSync().statusBarHeight + 44, //菜单栏总高度--单位px
			    titleBarHeight: 44, //标题栏高度--单位px
			    // #endif
			},
			
			ctxHeader: null,
			cameraImage: '',
			position: this.devicePosition,
			phoneSafeArea:false,

			frameOptions: {},
			use2d: true,
			canvasW: 0,
			canvasH: 0,
			pageWidth: 0,
			pageHeight: 0,
			
			filePath:'',
		}
	},

	beforeCreate() {
		// #ifdef MP-WEIXIN
		//获取相机权限
		uni.getSetting({
			success: (res) => {
				console.log('相机权限:', res)
				if (res.authSetting["scope.camera"]) {
					this.authCamera = true
				} else {
					this.authCamera = false
					console.log('没有获取到相机权限:', res)
					uni.showToast({
						title: '请确认是否允许获取您的相机权限！',
						icon: 'none'
					})
				}
			},
			fail: (err) => {
				console.log('相机权限获取失败:', err)
			}
		})
		// #endif
	},
	beforeMount() {
		uni.getSystemInfo({
			success: res => {
				// 安全区判断
				if (res.safeAreaInsets.bottom) this.phoneSafeArea = true
			}
		})
		const winInfo = uni.getSystemInfoSync()
		this.pageWidth = winInfo.windowWidth
	},
	mounted() {

		const query = uni.createSelectorQuery().in(this)
		query.select('#frameDom').boundingClientRect(data => {
			this.frameOptions = data
		}).exec()
		query.select('.camera-box').boundingClientRect(data => {
			this.pageHeight = data.height
		}).exec()
	},
	methods: {
		chooseImage() {
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album'], //从相册选择
				success: (res) => {
					this.filePath = res.tempFilePaths[0]
					this.$emit('chooseImage', this.filePath)
				}
			});
		},
		//拍摄头像
		takePhotoByHead() {
			this.ctxHeader = uni.createCameraContext();
			this.ctxHeader.takePhoto({
				quality: this.quality,
				selfieMirror: true, // 是否开启镜像 默认true
				success: async (res) => {
					this.cameraImage = await this.cropImage(res.tempImagePath)

				},
				fail: (err) => {
					console.log('fail 拍照失败: ' + err);
					uni.showToast({
						title: '拍照失败',
						icon: 'none'
					})
				}
			});
		},
		handleCameraError() {
			uni.showToast({
				title: '用户拒绝使用摄像头',
				icon: 'none'
			})
		},
		reverseCamera() {
			this.position = (("back" === this.position) ? "front" : "back")
		},
		cropImage(imgUrl) {
			const _this = this
			
			return new Promise((resolve, reject) => {
				uni.getImageInfo({
					src: imgUrl,
					success: async file => {
						const proportionX = file.width / this.pageWidth
						const proportionY = file.height / this.pageHeight

						// 取景框信息
						const options = {
							width: this.frameOptions.width * proportionX,
							height: this.frameOptions.height * proportionY,
							x: this.frameOptions.left * proportionX,
							y: this.frameOptions.top * proportionY,
						}

						this.canvasW = options.width
						this.canvasH = options.height

						var canvas = await new Promise((resolve) => {
							uni.createSelectorQuery()
								.in(this)
								.select(".camera-canvas")
								.fields({
									node: true
								})
								.exec((rst) => {
									var node = rst[0].node;
									resolve(node);
								});
						});
						canvas.width = this.canvasW;
						canvas.height = this.canvasH;
						uni.showLoading({
							title: "处理中"
						});
						await new Promise((resolve) => {
							setTimeout(resolve, 200)
						});
						var context = canvas.getContext("2d")
						var image = canvas.createImage()
						await new Promise((resolve, reject) => {
							image.onload = resolve;
							image.onerror = reject;
							image.src = imgUrl;
						})
						context.save()
						context.drawImage(image, options.x, options.y, options.width, options
							.height, 0, 0, this.canvasW, this.canvasH)
						context.restore();
						uni.canvasToTempFilePath({
							canvas: canvas,
							success: (rst) => {
								uni.compressImage({
									src: rst.tempFilePath,
									quality: this.compress, //压缩比例
									success: ress => {
										this.cameraImage = ress.tempFilePath; //图片
										
										resolve(ress.tempFilePath)
										// if(this.savePhotosAlbum){
										// 	uni.saveImageToPhotosAlbum({
										// 		filePath: this.cameraImage,
										// 		success: () => {}
										// 	})
										// }
									}
								})
							},
							complete: () => {
								uni.hideLoading();
							}
						});
					},
					fail(err) {
						reject(err)
					}
				})
			})
		},
		confirmPhoto() {
			this.$emit('confirmPhoto', this.cameraImage)
		},
		onok(ev) {
		    this.filePath = "";
			this.$emit('confirmPhoto', ev.path)
		    // this.cameraImage = ev.path;
		},
		oncancel() {
		    // url设置为空，隐藏控件
		    this.filePath = "";
		}

	}
}
</script>

<style lang="scss" scoped>
	.content {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		background: #fff;
		box-sizing: border-box;
		height: 100%;
		width: 100vw;

		.btns {
			display: flex;
			justify-content: space-around;
			align-items: center;
			padding: 10rpx 0 10rpx 0;

			.img_btn {
				width: 70rpx;
				height: 70rpx;
				border-radius: 60rpx;
			}

			.item_center {
				width: 110rpx;
				height: 110rpx;
			}

			.item {
				width: 70rpx;
				height: 70rpx;
			}

		}
	}


	.camera-box {
		--aperture-t: 5vh;
		// --aperture-w: 650rpx;
		// --aperture-h: 800rpx;
		--aperture-line: 4rpx;
		--aperture-line-style: 4rpx dashed #fff;
		--edit-padding: 64rpx;

		position: relative;
		width: 100vw;
		height: calc(100vh - var(--control-h));

		.camera-aperture {
			position: absolute;
			top: 5vh;
			left: 0;
			right: 0;
			width: var(--aperture-w);
			height: var(--aperture-h);
			border-style: dashed;
			border-width: 4rpx;
			border-color: #f9f9f9;
			margin: 0 auto;
			pointer-events: none;
			opacity: 0;
		}

		.camera-aperture-line {
			position: absolute;
			background-color: #fff;
			z-index: 2;
		}

		.camera-aperture-top {
			top: var(--aperture-t);
			left: calc((100vw - var(--aperture-w)) / 2);
			width: var(--aperture-w);
			height: var(--aperture-line);
		}

		.camera-aperture-right {
			top: var(--aperture-t);
			left: calc(var(--aperture-w) + (100vw - var(--aperture-w)) / 2);
			width: var(--aperture-line);
			height: var(--aperture-h);
		}

		.camera-aperture-bottom {
			top: calc(var(--aperture-t) + var(--aperture-h));
			left: calc((100vw - var(--aperture-w)) / 2);
			width: calc(var(--aperture-w) + var(--aperture-line));
			height: var(--aperture-line);
		}

		.camera-aperture-left {
			top: var(--aperture-t);
			left: calc((100vw - var(--aperture-w)) / 2);
			width: var(--aperture-line);
			height: var(--aperture-h);
		}

		.camera-aperture-tips {
			position: absolute;
			top: calc(var(--aperture-t) + var(--aperture-h) + 30rpx);
			left: 0;
			right: 0;
			font-size: 32rpx;
			color: #fff;
			max-width: 95%;
			color: #fff;
			text-align: center;
			margin: 0 auto;
		}
	}

	.camera-canvas {
		position: fixed;
		top: 200vh;
		left: 0;
		z-index: -999;
		opacity: 0;
	}

	// 安全底部
	.safety-zone-bottom {
		--safety-zone-bottom: 34px;

		.camera-img>.preview-box,
		.camera-box {
			height: calc(100vh - var(--control-h) - var(--safety-zone-bottom));
		}
		
		.btns{
			padding-bottom: var(--safety-zone-bottom);
		}

		.safety-zone {
			height: var(--safety-zone-bottom);
			width: 100%;
			background-color: inherit;
			z-index: 9999;
		}
	}
	
	.image-box{
		display: flex;
		align-items: center;
		justify-items: center;
		background-color: #000;
	}
	
	.h5Text{
		color: rgb(204, 204, 204);
		background-color: black;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
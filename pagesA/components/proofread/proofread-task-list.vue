<template>
    <ut-page>
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" :title="title" navbarType="1"></f-navbar>

            <view :style="{backgroundColor:'#fff'}" class="padding-top-sm padding-lr-sm flex align-center">
                <view class="u-border margin-right-sm round padding-tb-sm padding-lr-lg"
                    @click="showMonthPicker = true">
                    <view class="text-grey text-bold">{{ month }}</view>
                </view>
                <u-input
                    prefixIcon="search"
                    placeholder="输入客户或护理员姓名"
                    v-model="search.key"
                    shape='circle'
                    border="surround"
                    clearable
                >
                    <template slot="suffix">
                        <u-button
                            text="搜索"
                            type="success"
                            size="mini"
                            @click="onSearch"
                        ></u-button>
                    </template>
                </u-input>
            </view>

            <view class="filter-bar">
                <u-tabs
                    :list="filterTabs"
                    v-model="currentFilter"
                    :activeColor="colors"
                    @change="onFilterChange"
                ></u-tabs>
            </view>
        </ut-top>

        <mescroll-body
            ref="mescrollRef"
            :top="topWrapHeight+'px'"
            :top-margin="-topWrapHeight+'px'"
            bottom="20"
            :up="upOption"
            :safearea="true"
            @init="mescrollInit"
            @down="downCallback"
            @up="upCallback"
            @emptyclick="emptyClick"
        >
            <view v-for="(item, index) in dataList" :key="index" @click="goToCheck(item)">
                <view class="item-box">
                    <view class="head-image">
                        <text class="cuIcon-people"></text>
                    </view>
                    <view class="content text-content">
                        <view class="flex justify-between align-center">
                            <view>
                                <view class="flex justify-start align-center flex-wrap">
                                    <view class="text-df text-bold margin-right-xs">{{ item.name }}</view>
                                    <view class="cu-tag sm radius" :class="getStatusClass(item)">
                                        {{ getStatusText(item) }}
                                    </view>
                                </view>
                                <view class="text-sm text-gray margin-top-xs">
                                    护理日期：{{ item.workDate }}
                                </view>
                            </view>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            分组：{{ item.groupName }}
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            护理员：{{ item.attendantName }}
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            手机号：{{ item.phone }}
                        </view>

                        <template v-if="item.isManual">
                            <view class="text-sm text-gray margin-top-xs">
                                校对人：{{ item.lastManualUserName }}
                            </view>
                            <view class="text-sm text-gray margin-top-xs">
                            校对时间：{{ item.lastManualTime }}
                        </view>
                        </template>
                    </view>
                </view>
            </view>
        </mescroll-body>

        <u-datetime-picker
            :show="showMonthPicker"
            v-model="monthValue"
            mode="year-month"
            title="选择月份"
            :closeOnClickOverlay="true"
            @confirm="onMonthConfirm"
            @close="showMonthPicker = false"
            @cancel="showMonthPicker = false"
        ></u-datetime-picker>
    </ut-page>
</template>

<script>
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'

const app = getApp()

export default {
    mixins: [MescrollMixin],
    components: {
        MescrollBody,
    },
    props: {
        colors: {
            type: String,
            default: '',
        },
        // 'mang/care' 或 'mang/nurse'
        apiPath: {
            type: String,
            required: true,
        },
        checkPagePath: {
            type: String,
            required: true,
        },
        title: {
            type: String,
            default: '校对任务列表',
        },
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            mescroll: null,
            currentFilter: 0,
            showMonthPicker: false,
            monthValue: Date.now(),
            search: {
                key: '',
                state: 0,
            },
            topWrapHeight: 0,
            filterTabs: [
                { name: '可校对' },
                { name: '未校对' },
                { name: '已校对' },
                { name: '无法校对' },
            ],
            upOption: {
                page: {
                    num: 0,
                    size: 10,
                },
                noMoreSize: 3,
                textNoMore: '没有更多数据了',
                empty: {
                    tip: '暂无校对任务',
                },
            },
            pageReq: {
                pageindex: 1,
                pagesize: 10,
            },
            dataList: [],
            firstLoad: true,
        }
    },
    computed: {
        ...mapState({
            commKey: state => state.init.template.commKey,
            token: state => state.user.token,
            userInfo: state => state.user.info,
            community: state => state.init.community,
        }),
        month() {
            if (!this.monthValue) return '选择月份'
            const date = new Date(this.monthValue)
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0')
            return `${year}-${month}`
        },
    },
    mounted() {
        this.downCallback()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        mescrollInit(mescroll) {
            this.mescroll = mescroll
        },
        downCallback() {
            this.pageReq.pageindex = 1
            this.mescroll.resetUpScroll()
        },
        async upCallback(page) {
            const params = {
                communityId: this.community.id,
                pageindex: page.num,
                pagesize: page.size,
                ...this.search,
                month: this.month,
            }
            const { data } = await this.$ut.api(`${this.apiPath}/proofread/listpg`, params)
            setTimeout(() => {
                this.mescroll.endBySize(data.info.length, data.record)
            }, this.firstLoad ? 0 : 500)
            this.firstLoad = false
            if (page.num === 1) {
                this.dataList = []
            }
            this.dataList = this.dataList.concat(data.info)
        },
        emptyClick() {
            this.mescroll.resetUpScroll()
        },
        onSearch() {
            this.downCallback()
        },
        onClear() {
            this.search.key = ''
            this.downCallback()
        },
        onFilterChange(v) {
            this.search.state = v.index
            this.downCallback()
        },
        onMonthConfirm({ value }) {
            this.monthValue = value
            this.showMonthPicker = false
            this.downCallback()
        },
        getStatusClass(item) {
            if (item.proofreadError) {
                return 'bg-orange light'
            }
            return item.isManual ? 'bg-green light' : 'bg-blue light'
        },
        getStatusText(item) {
            if (item.proofreadError) {
                return item.proofreadErrorRemark ?? '异常'
            }
            return item.isManual ? '已校对' : '待校对'
        },
        goToCheck(item) {
            this.$tools.routerTo(this.checkPagePath, {
                workId: item.id,
                month: this.month,
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.filter-bar {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-box {
  padding: 20rpx 30rpx;
  background-color: white;
  margin-bottom: 10rpx;
  display: flex;
  position: relative;
  overflow: hidden;

  .content {
    flex: 1;
    padding: 0 20rpx;
  }

  &:active {
    transform: scale(0.98);
    transition: all 0.3s ease;
  }
}

.cuIcon-people {
  font-size: 116rpx;
  color: gray;
  border: 1rpx solid #ccc;
  width: 116rpx;
  height: 156rpx;
  line-height: 156rpx;
  border-radius: 6rpx;
  display: block;
}
</style>

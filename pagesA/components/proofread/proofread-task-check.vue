<template>
    <ut-page class="page">
        <f-navbar fontColor="#fff" :bgColor="colors" :title="title" navbarType="2"></f-navbar>

        <view class="cu-card margin-sm radius shadow bg-white flex padding-sm">
            <view class="flex-sub-0 margin-right-sm">
                <u-lazy-load v-if="form.imgHead"
                    :image="$tools.showImg(form.imgHead)"
                    width="120"
                    height="160"
                    border-radius="4" />
            </view>
            <view class="flex-sub text-content">
                <view class="flex justify-between align-center">
                    <view>
                        <view class="flex justify-start align-center">
                            <view class="text-df text-bold text-black">{{ form.name }}</view>
                            <view class="text-sm text-blue margin-left-lg" v-if="form.sex == 1">男
                                <text class="cuIcon-male" />
                            </view>
                            <view class="text-sm text-pink margin-left-lg" v-if="form.sex == 2">女
                                <text class="cuIcon-female" />
                            </view>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            <text v-if="form.phone" class="cuIcon-phone margin-right-xs" />
                            {{ form.phone }}
                        </view>
                    </view>
                    <view class="text-right">
                        <view class="text-sm text-gray">护理日期</view>
                        <view class="text-df text-bold" :style="{ color: colors }">{{ form.workDate }}</view>
                    </view>
                </view>

                <view class="text-xs text-gray text-cut margin-top-xs">{{ form.address }}</view>
                <view v-if="form.attendantName" class="margin-top-xs">
                    <text class="text-sm" :style="{ color: colors }">({{ form.groupName }})</text>
                    <text class="text-sm text-gray">护理员：{{ form.attendantName }}</text>
                </view>

                <view v-if="form.idcard" class="flex align-center margin-top-xs">
                    <text class="text-xs text-gray margin-right-xs">证件号码：</text>
                    <text class="text-xs text-black">{{ form.idcard }}</text>
                </view>

                <view v-if="form.isManual && form.proofreadErrorRemark" class="margin-top-xs">
                    <text class="text-sm text-gray">校对结果：</text>
                    <text class="cu-tag sm bg-orange light radius">
                        {{ form.proofreadErrorRemark }}
                    </text>
                </view>
            </view>
        </view>

        <view v-if="joinedDatas" class="cu-card margin-sm radius shadow s-gray bg-white">
            <file-data-item :detail="joinedDatas" :colors="colors" :only-view="true"></file-data-item>
        </view>
        <view v-else class="cu-card margin-sm radius shadow s-gray bg-white padding-sm text-center">暂无资料</view>

        <view class="cu-card margin-sm padding-lr-sm radius shadow bg-white">
            <view v-for="option in errorTypeOptions" :key="option" class="error-type-item">
                <view class="flex justify-between align-center">
                    <view class="text-df text-black" style="width: 90px;">{{ option }}：</view>
                    <u-radio-group
                        :value="errorTypeStates[option]"
                        placement="row"
                        @change="updateErrorTypeState(option, $event)"
                        style="display: flex">
                        <u-radio
                            :name="false"
                            :activeColor="colors"
                            label="正确"
                            :customStyle="{ marginRight: '40rpx', padding: '10rpx' }"
                        ></u-radio>
                        <u-radio
                            :name="true"
                            activeColor="#f56c6c"
                            label="错误"
                            :customStyle="{ padding: '10rpx' }"
                        ></u-radio>
                    </u-radio-group>
                </view>
            </view>
        </view>

        <view class="cu-card margin-sm radius padding-sm shadow bg-white">
            <view class="flex justify-between align-center margin-bottom-sm">
                <u-button
                    type="error"
                    text="重置"
                    :plain="true"
                    size="small"
                    @click="resetProjects"
                    :custom-style="{ marginRight: '20rpx' }"
                />
                <u-button
                    type="primary"
                    :color="colors"
                    text="添加项目"
                    :plain="true"
                    size="small"
                    @click="addProject"
                />
            </view>
            <u-swipe-action ref="swipeProjectList">
                <u-swipe-action-item v-for="item in orderProjects" :key="item.id"
                    :name="item.projectId"
                    :options="getProjectActionOptions(item)"
                    @click="projectActionClick">
                    <view class="padding-tb-sm solid-bottom">
                        <project-item :detail="item" :colors="colors"></project-item>
                    </view>
                </u-swipe-action-item>
            </u-swipe-action>
            <view v-if="!form.projects || !form.projects.length" class="text-center padding-xl">
                <text class="text-gray">无服务项目</text>
            </view>
        </view>

        <view class="cu-bar bg-white tabbar foot" style="padding: 0 10rpx 0 20rpx;">
            <view class="flex-sub">
                <view v-if="form.suplus > 0" class="text-sm text-gray">
                    剩余
                    <text class="text-red text-bold padding-lr-xs">{{ form.suplus }}</text>
                    份未校对
                </view>
                <view v-else class="text-sm text-gray">暂无剩余校对任务</view>
            </view>
            <view class="action">
                <u-button
                    type="primary"
                    :color="colors"
                    :loading="loading"
                    @click="save"
                    text="保存"
                    size="large"
                    shape="circle"
                    :custom-style="{ minWidth: '200rpx', height: '80rpx' }"
                ></u-button>
            </view>
        </view>

        <u-popup
            :show="showProjectPopup"
            mode="bottom"
            round="10"
            :closeable="true"
            :safe-area-inset-bottom="false"
            :mask-close-able="true"
            close-icon-pos="top-left"
            :z-index="1025"
            :overlay-style="{zIndex:998}"
            @close="showProjectPopup=false"
        >
            <view class="text-center padding-tb text-df text-bold text-black">项目选择</view>
            <project-selector
                :project-data="projectData"
                :selected-projects="form.projects"
                :colors="colors"
                @selectProject="selectProject"
            />
        </u-popup>
    </ut-page>
</template>

<script>
import { mapState } from 'vuex'
import ProjectItem from '@/pagesA/components/project-item.vue'
import FileDataItem from '@/pagesA/components/file-data-item.vue'
import ProjectSelector from './project-selector.vue'

const app = getApp()
export default {
    components: {
        ProjectItem,
        FileDataItem,
        ProjectSelector,
    },
    props: {
        colors: {
            type: String,
            default: '',
        },
        // 'mang/care' 或 'mang/nurse'
        apiPath: {
            type: String,
            required: true,
        },
        title: {
            type: String,
            default: '服务项目校对',
        },
        workId: {
            type: String,
            required: true,
        },
        month: {
            type: String,
            default: null,
        },
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            loading: false,
            form: {
                id: '',
                workDate: '',
                groupName: '',
                attendantId: '',
                attendantName: '',
                customerId: '',
                name: '',
                sex: 0,
                phone: '',
                idcard: '',
                address: '',
                isManual: false,
                manualTime: '',
                manualUserName: '',
                projects: [],
                datas: [],
                suplus: 0,
                proofreadError: false,
                proofreadErrorRemark: '',
            },
            showProjectPopup: false,
            projectData: [],
            initialProjects: [],
            errorTypeOptions: ['截图项目时长', '签字表时间', '签字表项目'],
            errorTypeStates: {},
        }
    },
    computed: {
        ...mapState({
            commKey: state => state.init.template.commKey,
            token: state => state.user.token,
            userInfo: state => state.user.info,
            community: state => state.init.community,
        }),
        orderProjects() {
            return this.form.projects?.toSorted((a, b) => a.govCode - b.govCode) ?? []
        },
        joinedDatas() {
            if (!this.form.datas?.length) return null
            const datas = this.form.datas
            const titles = []
            const outputDetail = {
                title: '',
                files: [],
            }
            for (const d of datas) {
                titles.push(d.title)
                for (const details of d.details) {
                    outputDetail.files.push(...details.files)
                }
            }
            if (!outputDetail.files.length) return null
            return {
                createTime: '',
                dataTypeId: '',
                title: titles.join('、'),
                details: [outputDetail],
            }
        },
    },
    watch: {
        workId: {
            handler(newVal) {
                if (newVal) {
                    this.loadTask()
                }
            },
            immediate: true,
        },
    },

    methods: {
        async loadTask() {
            if (!this.workId) return
            this.loading = true
            const { data } = await this.$ut.api(`${this.apiPath}/proofread/task`, {
                communityId: this.community.id,
                workId: this.workId,
                month: this.month,
            }).finally(() => this.loading = false)
            this.updateFormData(data)
        },
        updateFormData(data) {
            this.form = {
                ...this.form,
                ...data, datas: data.datas || [],
                suplus: data.suplus || 0,
                proofreadErrorRemark: data.proofreadError ? data.proofreadErrorRemark : '',
            }
            if (data.projects && data.projects.length > 0) {
                this.initialProjects = JSON.parse(JSON.stringify(data.projects))
            }
            this.initErrorTypeStates()
        },
        initErrorTypeStates() {
            const errorStates = {}
            this.errorTypeOptions.forEach((option) => {
                errorStates[option] = false
            })
            if (this.form.proofreadErrorRemark) {
                const selectedErrors = this.form.proofreadErrorRemark
                    .trim()
                    .split('、')
                    .map((e) => e.replaceAll('有误', ''))
                selectedErrors.forEach((error) => {
                    if (this.errorTypeOptions.includes(error)) {
                        errorStates[error] = true
                    }
                })
                this.errorTypeOptions.forEach((option) => {
                    if (errorStates[option] === null && this.form.proofreadErrorRemark) {
                        errorStates[option] = false
                    }
                })
            }
            this.errorTypeStates = errorStates
        },
        updateErrorTypeState(option, value) {
            this.$set(this.errorTypeStates, option, value)
            this.syncErrorRemark()
        },
        syncErrorRemark() {
            const errorTypes = this.errorTypeOptions.filter((option) => this.errorTypeStates[option] === true)
            this.form.proofreadErrorRemark = errorTypes.map((e) => e + '有误').join('、')
            this.form.proofreadError = errorTypes.length > 0
        },
        async save() {
            this.loading = true
            const params = {
                communityId: this.community.id,
                workId: this.form.id,
                month: this.month,
                projectIds: this.form.projects ? this.form.projects.map(p => p.projectId) : [],
                proofreadError: this.form.proofreadError,
            }
            if (this.form.proofreadError) {
                params.proofreadErrorRemark = this.form.proofreadErrorRemark
            }
            const { data } = await this.$ut.api(`${this.apiPath}/proofread/save`, params)
                .finally(() => this.loading = false)
            uni.showToast({
                title: '保存成功',
                icon: 'success',
            })
            if (data && data.id) {
                this.updateFormData(data)
            } else {
                setTimeout(() => {
                    this.goBack()
                }, 1500)
            }
        },
        resetProjects() {
            uni.showModal({
                title: '确认重置',
                content: '确定要将服务项目重置为初始状态吗？',
                success: (res) => {
                    if (res.confirm) {
                        if (this.initialProjects?.length > 0) {
                            this.form.projects = JSON.parse(JSON.stringify(this.initialProjects))
                        } else {
                            this.form.projects = []
                        }
                    }
                },
            })
        },
        addProject() {
            this.loadProjectData()
            this.showProjectPopup = true
        },
        async loadProjectData() {
            const { data } = await this.$ut.api('mang/care/work/project/allRuleListpg', {
                communityId: this.community.id,
                workId: this.form.id,
            })
            this.projectData = data?.info || []
        },
        getProjectActionOptions() {
            const btnDelete = {
                text: '删除',
                code: 'delete',
                style: {
                    backgroundColor: '#f56c6c',
                },
            }
            return [btnDelete]
        },
        projectActionClick(data) {
            if (data.code === 'delete') {
                this.removeProject(data.name)
            }
            this.$refs['swipeProjectList'].closeAll()
        },
        removeProject(projectId) {
            if (this.form.projects && this.form.projects.length) {
                const index = this.form.projects.findIndex(p => p.projectId === projectId)
                if (index >= 0) {
                    this.form.projects.splice(index, 1)
                }
            }
        },
        selectProject(projects) {
            this.showProjectPopup = false
            if (!this.form.projects) {
                this.$set(this.form, 'projects', [])
            }
            if (!projects || !projects.length) return
            projects.forEach(project => {
                const existingProject = this.form.projects.find(p => p.projectId === project.id)
                if (!existingProject) {
                    this.form.projects.push({
                        id: project.id,
                        projectId: project.id,
                        projectName: project.name,
                        projectCode: project.code,
                        govCode: project.govCode,
                        requireMinDuration: project.minDuration,
                        requireMaxDuration: project.maxDuration,
                        remark: project.remark || '',
                    })
                }
            })
        },
        goBack() {
            uni.navigateBack()
        },
    },
}
</script>

<style lang="scss" scoped>
.page {
  padding-bottom: 128rpx;
}

.cuIcon-people {
  font-size: 116rpx;
  color: gray;
  border: 1rpx solid #ccc;
  width: 116rpx;
  height: 156rpx;
  line-height: 156rpx;
  border-radius: 6rpx;
  display: block;
}

.error-type-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

:deep(.u-radio) {
  min-height: 88rpx;
  display: flex;
  align-items: center;
}
</style>

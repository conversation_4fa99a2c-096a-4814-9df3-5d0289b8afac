<template>
	<view class="flex margin-lr-sm margin-tb-xs">
		<view class="head-image padding-right-sm">
			<text class="cuIcon-addressbook item-icon" />
		</view>
		<view class="flex-sub padding-bottom-sm">
			<view >
				<view class="text-df text-bold" >{{detail.projectName}}</view>
			</view>
			<view class="flex text-sm">
				<view>
					<text class="text-gray">社保编号：</text>
					<text>{{detail.govCode}}</text>
				</view>
				<view class="margin-left-xl">
					<text class="text-gray">时长：</text>
					<text>{{detail.minDuration}}</text>
					<text >-</text>
					<text>{{detail.maxDuration}}</text>
					<text class="text-gray text-sm"></text>分钟</text>
				</view>
			</view>

		</view>
		<view class="tip">
			<u-number-box v-model="detail.monthTimes" :min="1" :disabled="isEdit!=true" @change="valChange(detail)"></u-number-box>
			 
		</view>
	</view>
</template>

<script>
	export default {
		components: {
		},
		props:{
			colors:{
				type:String,
				default:'',
			},
			isEdit:{
				type:Boolean,
				default:false,
			},
			detail:{
				type:Object,
				default:()=>{},
			},
			tip:{
				type:String,
				default:'',
			},
		},
		data() {
			return {
			}
		},
		methods:{
			valChange(item){
				this.$emit('valChange',item)
			}
		}
	}
</script>

<style scoped lang="scss">
.item-icon{
	margin-top: 5rpx;
	font-size: 72rpx;
	color: var(--colors);
	// border: 1rpx solid #ccc;
	width: 72rpx;
	height: 72rpx;
	line-height: 72rpx;
	border-radius: 6rpx;
	display: block;
	font-weight: 100;
}

.tip{
	margin-top: 10rpx;
}
	
</style>
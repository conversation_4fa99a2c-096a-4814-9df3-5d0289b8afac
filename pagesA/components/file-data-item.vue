<template>
    <view class="padding-sm bg-white text-content item-box">
        <view class="text-bold text-lg">{{ detail.title }}</view>
        <view v-for="(item,index) in detail.details" :key="index" class="padding-left-lg">
            <view>
                <text>{{ item.title }}</text>
                <text v-if="item.require" class="text-xs margin-left-xs" :style="{color:colors}">(必填)</text>
            </view>
            <view class="image-box">
                <ut-image-upload
                    ref="upload"
                    name="file"
                    v-model="itemUrl[index]"
                    mediaType="image"
                    :colors="colors"
                    :max="20"
                    :headers="headers"
                    :action="uploadInfo.server+uploadInfo.single||''"
                    :preview-image-width="1200"
                    :width="220"
                    :height="180"
                    :border-radius="8"
                    :disabled="onlyView"
                    :add="!onlyView"
                    @uploadSuccess="uploadFaceSuccess"
                    @imgDelete="imgDelete">
                </ut-image-upload>
            </view>
            <view class="sub-box"></view>
        </view>
    </view>
</template>

<script>

import { mapState } from 'vuex'

export default {
    components: {},
    props: {
        colors: {
            type: String,
            default: '',
        },
        detail: {
            type: Object,
            default: () => {},
        },
        onlyView: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            itemUrl: [],
            myPhotos: [],
        }
    },
    computed: {
        ...mapState({
            commKey: state => state.init.template.commKey,
            token: state => state.user.token,
            userInfo: state => state.user.info,
            community: state => state.init.community,
            uploadInfo: state => state.init.oss,
        }),
        headers() {
            return {
                Token: this.uploadInfo.token,
            }
        },
    },
    mounted() { },
    watch: {
        detail: {
            handler() {
                this.init()
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        init() {
            this.itemUrl = []
            for (let i = 0; i < this.detail.details.length; i++) {
                let arr = []
                this.detail.details[i].files.forEach(item => {
                    arr.push(item.url)
                })
                this.itemUrl.push(arr)
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.sub-box {
  padding-top: 10rpx;
  border-bottom: 1rpx dashed #a0a0a0;
  margin-bottom: 10rpx;
}

.item-box {
  border-bottom: 1rpx solid #a0a0a0;

  &:last-child {
    border-bottom: none;
  }
}

</style>

<template>
	<!--
		@devicePosition: 摄像头位置	前置或后置摄像头，值为front, bac
		@quality: 成像质量，值为high（高质量）、normal（普通质量）、low（低质量）
	-->
	<view class="ut-image-upload-list">
		<view v-if="imageUrl"  class="ut-image-upload-Item"
		:style="{width:width+'rpx',height:height+'rpx',borderRadius:borderRadius+'rpx',paddingTop:showCamera?systemInfo.statusBarHeight+'px':0}"
		>
			<view class="image-loading-box" >
				<image :lazy-load="true" class="image-real" :src="showImg(imageUrl)"
					@click="imgPreviewClick" :style="{borderRadius:borderRadius+'rpx'}" :mode="mode"
					@load="imageLoad(0)"></image>
				<view class="image-loading" v-if="!checkLoad(0)">
					<view class="loader">
						<view class="loaders">
							<view class="loader2"
								:style="{width:imageLoadingWidth()+'rpx',height:imageLoadingWidth()+'rpx'}"></view>
						</view>
						加载中
					</view>
				</view>
				<view class="ut-image-upload-progress-text" v-if="item.progress!=100" :style="{lineHeight:height+'rpx'}">{{item.progress}}%</view>
				<view class="ut-image-upload-progress" v-if="item.progress!=100" @click="imgPreviewClick" :style="{lineHeight:height+'rpx',opacity:1-item.progress/100}"></view>
				<view class="ut-image-upload-Item-del" :class="{'image-show':checkLoad(0)}"
					v-if="!disabled && remove && item.progress==100 && checkLoad(0)" @click="imgDel">×</view>
			</view>
		</view>
		<view v-else class="ut-image-upload-Item ut-image-upload-Item-add"
			:style="{width:width+'rpx',height:height+'rpx',borderRadius:borderRadius+'rpx',lineHeight:height+'rpx'}"
			@click="chooseFile">
			<text v-if="!$slots.default">+</text>
			<slot name="default" v-else></slot>
		</view>
		<view v-if="showCamera">
			
			<view style="position: fixed;z-index: 1000;left: 40rpx;top: 20rpx;" :style="{paddingTop:systemInfo.statusBarHeight+'px'}">
				
				<view class="u-flex left" hover-class="left--hover" hover-start-time="150">
				    <view class="u-flex u-home-arrow-left" :style="{borderColor:'rgba(255,255,255,.5)'}">
				       <view @click="leftClick">
				            <u-icon name="arrow-left" size="20" color="#fff"></u-icon>
				        </view>
				    </view>
				</view>
			</view>
			<diy-person-camera
				:devicePosition="front"
				:quality="quality"
				@confirmPhoto="confirmPhoto" />
		</view>
	</view>
</template>

<script>
	import DiyPersonCamera from '../ut-person-camera/ut-person-camera.vue';
	export default {
		
		components: {
		    DiyPersonCamera
		},
		props:{
			value: { //受控图片列表
				type: String,
				default: '',
			},
			disabled: { //是否禁用
				type: Boolean,
				default: false,
			},
			remove: { //是否展示删除按钮
				type: Boolean,
				default: true,
			},
			name: { //发到后台的文件参数名
				type: String,
				default: 'file',
			},
			front:{
				type: String,
				default: '',
			},
			quality: {
				type: String,
				default: 'high'
			},
			width: {
				type: [Number,String],
				default: 120,
			},
			colors: {
				type: String,
				default: '',
			},
			height: {
				type: [Number,String],
				default: 120,
			},
			mode: {
				type: String,
				default: 'aspectFill',
			},
			borderRadius: {
				type: Number,
				default: 8,
			},
			action: { //上传服务器
				type: String,
				default: '',
			},
			headers: { //上传的请求头部
				type: Object,
				default: () => {},
			},
			formData: { //HTTP 请求中其他额外的 form data
				type: Object,
				default: () => {},
			},
			previewImageWidth: {
				type: Number,
				default: 750,
			},
			uploadSuccess: {
				default: (res) => {
					return {
						success: false,
						url: ''
					}
				},
			},
		},
		data() {
			return {
				systemInfo:{
				    statusBarHeight:uni.getSystemInfoSync().statusBarHeight,
				    // #ifdef MP-ALIPAY
				    navBarH: uni.getSystemInfoSync().statusBarHeight + uni.getSystemInfoSync().titleBarHeight, //菜单栏总高度--单位px
				    titleBarHeight: uni.getSystemInfoSync().titleBarHeight, //标题栏高度--单位px
				    // #endif
				    // #ifndef MP-ALIPAY
				    navBarH: uni.getSystemInfoSync().statusBarHeight + 44, //菜单栏总高度--单位px
				    titleBarHeight: 44, //标题栏高度--单位px
				    // #endif
				},
				showCamera:false,
				imageUrl:'',
				uploadLists: [],
				imageLoaded: {},
				item:{},
			}
		}, 
		computed:{

		},
		watch: {
			value(val, oldVal) {
				this.imageUrl=val
				this.item={
					id: this.guid(),
					url: val,
					state:6,
					progress: 100,
				}
				
			},
		
		},
		methods: {
			imageLoadingWidth() {
				if (this.width > this.height) {
					return this.height > 40 ? this.height / 3 : 40
				} else {
					return this.width > 40 ? this.width / 3 : 40
				}
			},
			imgPreviewClick() {
				if (this.imageUrl) {
					uni.previewImage({
						urls: [this.imageUrl],
						current: 0,
						loop: true,
					});
				}
			},
			imageLoad(index) {
				this.$set(this.imageLoaded, 'l_' + index, true)
			},
			chooseFile(){
				this.showCamera=true
			},
			confirmPhoto(fileUrl){
				console.log('fff',fileUrl)
				this.showCamera=false
				this.imageUrl=fileUrl
				this.imgUpload([fileUrl])
				this.$emit('confirmPhoto')
			},
			leftClick(){
				this.showCamera=false
			},
			imgUpload(tempFilePaths, type) {
				if (this.action == 'uniCloud') {
					this.uniCloudUpload(tempFilePaths, type)
					return
				}
				uni.showLoading({
					title: '上传中'
				});
			
				let that = this				
				tempFilePaths.forEach(item=>{
					let obj = {
						id: that.guid(),
						state:0,
						progress: 0,
						url: item,
						data: []
					}
					this.item=obj
				})
				
			
				let uploadImgs = []

				const promise = new Promise((resolve, reject) => {
					let task = uni.uploadFile({
						url: this.action, //仅为示例，非真实的接口地址
						filePath: this.item.url,
						name: this.name,
						fileType: 'image',
						formData: this.formData,
						header: this.headers,
						success: (uploadFileRes) => {
							const _res = JSON.parse(uploadFileRes.data);
							this.$set(this.item, 'progress', 100)
							this.$set(this.item, 'data', _res[0])
							this.$set(this.item, 'state', 2)
							this.$emit("uploadSuccess", this.item)
						},
						fail: (err) => {
							reject(err);
							this.$emit("uploadFail", err);
						},
						complete: () => {}
					})
					task.id=this.item.id
					task.onProgressUpdate((res) => {
						if(res.progress!=100)
						{
							this.$set(this.item, 'progress', res.progress)
							this.$set(this.item, 'state', 1)
						}
					});
					uploadImgs.push(promise)
				})
				Promise.all(uploadImgs) //执行所有需请求的接口
					.then((results) => {
						uni.hideLoading();
					})
					.catch((res, object) => {
						uni.hideLoading();
						this.$emit("uploadFail", res);
					});
			
			},
			uniCloudUpload(tempFilePaths, type) {
				uni.showLoading({
					title: '上传中'
				});
				let uploadImgs = [];
				tempFilePaths.forEach((item, index) => {
					uploadImgs.push(new Promise((resolve, reject) => {
			
						uniCloud.uploadFile({
							filePath: item,
							cloudPath: this.guid() + '.' + this.getFileType(item, type),
							success(uploadFileRes) {
								if (uploadFileRes.success) {
									resolve(uploadFileRes.fileID);
								}
							},
							fail(err) {
								reject(err);
							},
							complete() {}
						});
			
					}))
				})
				Promise.all(uploadImgs) //执行所有需请求的接口
					.then((results) => {
						uni.hideLoading();
			
						uniCloud.getTempFileURL({
							fileList: results,
							success: (res) => {
								res.fileList.forEach(item => {
									//this.value.push(item.tempFileURL)
									// #ifndef VUE3
									this.value.push(item.tempFileURL)
									this.$emit("input", this.value);
									// #endif
									// #ifdef VUE3
									this.modelValue.push(item.tempFileURL)
									this.$emit("update:modelValue", this.modelValue);
									// #endif
								})
							},
							fail() {},
							complete() {}
						});
					})
					.catch((res, object) => {
						uni.hideLoading();
					});
			},
			getFileType(path, type) { //手机端默认图片为jpg 视频为mp4
				// #ifdef H5 
				var result = type == 0 ? 'jpg' : 'mp4';
				// #endif
			
			
				// #ifndef H5
				var result = path.split('.').pop().toLowerCase();
				// #ifdef MP 
				if (this.compress) { //微信小程序压缩完没有后缀
					result = type == 0 ? 'jpg' : 'mp4';
				}
				// #endif
				// #endif
				return result;
			},
			guid() {
				return 'xxxxxxxx-date-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
					var r = Math.random() * 16 | 0,
						v = c == 'x' ? r : (r & 0x3 | 0x8);
					return v.toString(16);
				}).replace(/date/g, function(c) {
					return Date.parse(new Date());
				});
			},
			/**
			 * 触发监听
			 * @param {Number} key 当前任务的下标
			 * @param {Object} uploadTask uni.uploadFile 的返回值
			 */
			triggerMonitor(key, uploadTask) {
				uploadTask.onProgressUpdate(res => {
					// 触发父组件/页面的监听事件
					this.uploadTaskProgress[key] = res;
					// 合并所有任务的列表用于父组件/页面的监听
					// {
					//	// 当前的进度
					// 	progress: 0,
					//	// 当前的所有进度 保存每条任务的进度等等
					// 	tasks: []
					// }
					this.uploadTask.onProgressUpdate(this.mergerProgress(this.uploadTaskProgress));
				})
			},
			/**
			 * 合并进度
			 * @param {Array} tasks 所有的任务
			 */
			mergerProgress(tasks) {
				var progress = 0;
				tasks.forEach((value, key) => {
					if (value) {
						progress += value.progress;
					} else {
						progress += 0;
					}
				})
				return {
					progress: progress / tasks.length,
					tasks
				}
			},
			/**
			 * 设置父组件/页面的监听事件
			 * onProgressBegin 开始监听触发事件
			 * onProgressUpdate 进度变化事件
			 * onProgressEnd 结束监听事件
			 * [req 1,req 2...]
			 * @param {Object} obj {onProgressUpdate,onProgressBegin,onProgressEnd}
			 */
			setUpMonitor(obj) {
				this.uploadTask = {
					load: true,
					...obj
				}
			},
			
			showImg(url, size = 200, quality = 70) {
				// let bWidthSize=this.width>this.height?this.width*2:this.height*2
				if (url.indexOf('oss.') > 0) {
					let str = url
					str += "&width=" + this.width
					str += "&height=" + this.height
					if (quality) str += "&quality=" + quality
					return str
				} else {
					return url
				}
			},
			checkLoad(index) {
				if (this.imageLoaded['l_' + index]) return true
				return false
			},
			imgDel() {
				uni.showModal({
					title: '提示',
					content: '您确定要删除么?',
					success: (res) => {
						if (res.confirm) {
							this.item={}
							this.imageUrl=''
							this.$emit("imgDelete");
						} else if (res.cancel) {}
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.u-home-arrow-left{
	    justify-content: space-between;
	    border-radius: 100rpx;
	    padding: 8rpx;
	    opacity: .8;
	    border: 1rpx solid #dadbde;
	}
	.ut-image-upload-list {
		display: flex;
		flex-wrap: wrap;
	}
.ut-image-upload-Item {
	/* 		width: 160rpx;
	height: 160rpx; */
	/* border-radius: 8rpx; */
	margin-right: 10rpx;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

	.image-loading-box {
		width: 100%;
		height: 100%;
		position: relative;
	}
	
	.ut-image-upload-Item-add {
		font-size: 105rpx;
		/* line-height: 160rpx; */
		text-align: center;
		border: 1px dashed #d9d9d9;
		color: #d9d9d9;
		/* line-height: 160rpx; */
	}
	
	.image-loading {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.image-loading .loader {
		color: #999;
		font-size: 20rpx;
	}
	
	.loaders {
		display: flex;
		justify-content: center;
		font-size: 16rpx;
		margin-bottom: 20rpx;
		opacity: 0.9;
	}
	
	.loader2 {
		border: 4rpx solid #f3f3f3;
		border-radius: 50%;
		border-top: 4rpx solid var(--colors);
	
		animation: spin2 1s linear infinite;
	}
	
	@keyframes spin2 {
		0% {
			transform: rotate(0deg);
		}
	
		100% {
			transform: rotate(360deg);
		}
	}
	
	.ut-image-upload-Item-del {
		background-color: var(--colors);
		font-size: 24rpx;
		position: absolute;
		width: 35rpx;
		height: 35rpx;
		line-height: 35rpx;
		text-align: center;
		top: 0;
		right: 0;
		z-index: 9;
		color: #fff;
		opacity: 0.7;
	
	}
	
	
	
	.ut-image-upload-Item-del-cover {
		background-color: var(--colors);
		font-size: 24rpx;
		position: absolute;
		width: 35rpx;
		height: 35rpx;
		text-align: center;
		top: 0;
		right: 0;
		color: #fff;
		/* #ifdef APP-PLUS */
		line-height: 25rpx;
		/* #endif */
		/* #ifndef APP-PLUS */
		line-height: 35rpx;
		/* #endif */
		z-index: 2;
		opacity: 0.7;
	}
	
	.ut-image-upload-progress, .ut-image-upload-progress-text{
		position: absolute;
		left:0;
		top: 0;
		color: var(--colors);
		width: 100%;
		text-align: center;
		
	}
	
	.ut-image-upload-progress{
		background-color: #fff;
	}
</style>
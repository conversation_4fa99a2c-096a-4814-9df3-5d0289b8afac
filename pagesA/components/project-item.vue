<template>
  <view class="flex">
    <view class="social-security-number padding-top-xs flex justify-center">
      <text class="social-security-text text-center radius" :style="{ background: colors }">{{ detail.govCode }}</text>
    </view>
    <view class="flex-sub padding-bottom-sm padding-left-sm">
      <view class="flex align-center">
        <view class="text-df text-bold">{{ detail.projectName }}</view>
        <view v-if="detail.suggestRemark" class="text-sm text-gray padding-left-sm suggest-remark">{{
          detail.suggestRemark
        }}</view>
      </view>
      <view class="flex text-sm">
        <view>
          <text class="text-gray">社保编号：</text>
          <text>{{ detail.govCode }}</text>
        </view>
        <view class="margin-left-xl">
          <text class="text-gray">推荐用时：</text>
          <text v-if="detail.requireMinDuration" class="margin-left-xs">{{ detail.requireMinDuration }}--</text>
          <text v-if="detail.requireMaxDuration">{{ detail.requireMaxDuration }}</text>
          <text v-if="detail.requireMinDuration || detail.requireMaxDuration">分钟</text>
        </view>
      </view>
    </view>
    <view v-if="tip" class="tip">{{ tip }}</view>
  </view>
</template>

<script>
export default {
  components: {},
  props: {
    colors: {
      type: String,
      default: '',
    },
    detail: {
      type: Object,
      default: () => {},
    },
    tip: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  methods: {},
}
</script>

<style scoped lang="scss">
.social-security-number {
  .social-security-text {
    font-size: 28rpx;
    font-weight: bold;
    width: 48rpx;
    height: 48rpx;
    line-height: 48rpx;
    color: white;
  }
}

.suggest-remark {
  color: #ffa435;
}
</style>

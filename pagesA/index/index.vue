<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="管理设置" navbarType="2" />
		<template v-for="(item,index) in newMenu">
			<view :key="index">
				<view v-if="item.menus.length" class="list bg-white ut-radius margin padding-xs">
					<list-cell>{{item.title}}</list-cell>
					<ut-grid :colors="colors" :list="item.menus" :count="item.menus.length>4?4:item.menus.length" :show-background-color="true"></ut-grid>
				</view>
			</view>
		</template>
	</ut-page>
	
</template>

<script>
let app = getApp();

import {menu} from './menu.js'
import {
	mapMutations,
	mapActions,
	mapState
} from 'vuex';
export default {
	mixins: [], // 使用mixin
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			nfc:null,
			readText:'',
			uid:'',
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
		
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
		newMenu(){
			return menu;
		}
	},
	watch:{
	},
	onLoad: async function (options) {
		await this.$onLaunched

	},
	onUnload() {

	},
	methods: {
		
	},
}
</script>

<style>
</style>
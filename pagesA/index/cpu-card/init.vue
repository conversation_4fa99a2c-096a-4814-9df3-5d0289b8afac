<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="CPU卡出厂设置" navbarType="2" />
		<view class="padding-lg"></view>
		<view>
			<view v-if="!nfc || !nfc.adapter" class="text-center padding-tb-xl">
				请打开NFC
			</view>
			<view v-else>
				<view class="text-center">把卡放到NFC感应处...</view>
				<view v-if="uid">卡号：{{uid}}</view>
				<template v-for="(item,index) in textList">
					<view :key="index" class="text-content">
						<text class="margin-right-xs">{{item.title}}</text>
						<text>{{item.content}}</text>
					</view>
				</template>
			
			</view>
		</view>
	</ut-page>
	
</template>

<script>
let app = getApp();

import {
	mapMutations,
	mapActions,
	mapState
} from 'vuex';
import NFC from '@/pagesA/nfc/nfc.js'
import CryptoJS from '@/pagesA/nfc/crypto-js.min'
export default {
	mixins: [], // 使用mixin
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			nfc:null,
			uid:'',
			textList:[],
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
		
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	watch:{
	},
	onLoad: async function (options) {
		await this.$onLaunched
		if(app.globalData.nfc==null){
			app.globalData.nfc=new NFC()
		}
		app.globalData.nfc.on('connected',this.cardOp)		
		app.globalData.nfc.on('error',(err)=>{
			console.log(err)
		});
		this.nfc=app.globalData.nfc
	},
	onUnload() {
		if(app.globalData.nfc!=null){
			app.globalData.nfc.off('connected')
			app.globalData.nfc.off('error')
		}
	},
	methods: {
		async cardOp(uid,type){
			this.uid=uid
			
			this.textList=[]
			
			const dir=await this.nfc.apdu('00A40000023F00')
			this.textList.push({cmd:'00A40000023F00',title:'主目录',content:dir})
			
			//2获取8位随机码
			const random=await this.nfc.apdu('0084000004')			
			this.textList.push({cmd:'0084000004',title:'随机码',content:random})
			
			//3得到外部认证指令
			const sendRandomKey=this.nfc.randomKey(random,'FFFFFFFFFFFFFFFF');			
			this.textList.push({cmd:'~',title:'AES加密',content:sendRandomKey})
			//4建立外部认证指令
			const randomKey=await this.nfc.apdu(sendRandomKey)	
			this.textList.push({cmd:sendRandomKey,title:'外部校验',content:randomKey})
			
			//5.1卡片擦除所有数据
			var res=await this.nfc.apdu('800E000000')
			this.textList.push({cmd:'800E000000',title:'擦除所有数据',content:res})
			
			//5.2建立MF目录下的密钥文件
			res=await this.nfc.apdu('00A4000000')
			this.textList.push({cmd:'00A4000000',title:'选择根目录',content:res})
			//5.3创建密钥文件
			res=await this.nfc.apdu('80E00000073F00B001F0FFFF')
			this.textList.push({cmd:'80E00000073F00B001F0FFFF',title:'创建MF目录下的密钥文件',content:res})
			//5.4密钥文件设置密码
			res=await this.nfc.apdu('80D401000D39F002AA997574313030202020')
			this.textList.push({cmd:'80D401000D39F002AA997574313030202020',title:'给密钥文件设置密码',content:res})
			
			//6.1应用目录
			res=await this.nfc.apdu('80E03F010D380600F0FA95FFFF4144463031')
			this.textList.push({cmd:'80E03F010D380600F0FA95FFFF4144463031',title:'建立食堂专用目录',content:res})
			
			uni.showToast({
				title:'出厂设置完成'
			})	
			
			this.textList.push({cmd:'~',title:'出厂设置完成',content:''})
		},
		
	},
}
</script>

<style>
</style>
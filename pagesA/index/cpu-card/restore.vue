<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="CPU卡恢复到原始" navbarType="2" />
		<view class="padding-lg"></view>
		<view>
			<view v-if="!nfc || !nfc.adapter" class="text-center padding-tb-xl">
				请打开NFC
			</view>
			<view v-else>
				<view class="text-center">把卡放到NFC感应处...</view>
				<view v-if="uid">卡号：{{uid}}</view>
				<template v-for="(item,index) in textList">
					<view :key="index" class="text-content">
						<text class="margin-right-xs">{{item.title}}</text>
						<text>{{item.content}}</text>
					</view>
				</template>
			</view>
		</view>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import NFC from '@/pagesA/nfc/nfc.js'
import CryptoJS from '@/pagesA/nfc/crypto-js.min'

export default {
	mixins: [], // 使用mixin
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			cardKey:'',
			nfc:null,
			err:'',
			uid:'',
			textList:[],
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
				
		if(app.globalData.nfc==null){
			app.globalData.nfc=new NFC()
		}
		app.globalData.nfc.on('connected',this.cardOp)		
		app.globalData.nfc.on('error',(err)=>{
			console.log(err)
		});
		this.nfc=app.globalData.nfc
		
	},
	onUnload() {
		if(app.globalData.nfc!=null){
			app.globalData.nfc.off('connected')
			app.globalData.nfc.off('error')
		}
	},
	methods: {
		async cardOp(uid,type){
			this.uid=uid;
			this.textList=[]
			
			const dir=await this.nfc.apdu('00A40000023F00')
			this.textList.push({cmd:'00A40000023F00',title:'主目录',content:dir})

			//2获取8位随机码
			const random=await this.nfc.apdu('0084000004')
			this.textList.push({cmd:'0084000004',title:'随机码',content:random})
			
			//3得到外部认证指令
			let hex = Buffer.from('ut100   ').toString('hex').padEnd(16,0)
			const sendRandomKey=this.nfc.randomKey(random,hex);	
			this.textList.push({cmd:'~',title:'AES加密',content:sendRandomKey})
			
			//4建立外部认证指令
			const randomKey=await this.nfc.apdu(sendRandomKey)
			this.textList.push({cmd:sendRandomKey,title:'外部校验',content:randomKey})
			
			//读卡内容
			var res=await this.nfc.apdu('800E000000')
			this.textList.push({cmd:'800E000000',title:'擦除所有数据',content:res})
			
			res=await this.nfc.apdu('80E00000073F00B001F0FFFF')
			this.textList.push({cmd:'80E00000073F00B001F0FFFF',title:'创建MF目录下的密钥文件',content:res})	
					
			res=await this.nfc.apdu('80D401000D39F0F0AA99FFFFFFFFFFFFFFFF')
			this.textList.push({cmd:'80D401000D39F0F0AA99FFFFFFFFFFFFFFFF',title:'给密钥文件设置密码',content:res})
			
			uni.showToast({
				title:'初始化完成!请不要重复操作，会锁卡',
				icon:'none'
			})
			
			
			this.textList.push({cmd:'~',title:'已恢复，可以进行正常开卡操作了',content:''})
				
		},
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>

</style>

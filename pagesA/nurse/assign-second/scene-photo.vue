<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="现场照片" navbarType="2" />
		</ut-top>
		
		<view class="bg-white padding-tb-sm padding-lr-sm" :style="{minHeight:'calc(100vh - '+topWrapHeight+'px)'}">
			<view v-if="setInfo.minCount" class="margin-tb padding-lr-xs" :style="{color:colors}">至少需要 {{setInfo.minCount}}张现场照片</view>
			<view class="image-box">
				<ut-image-upload
					ref="upload"
					name="file"
					v-model="myPhotos"
					mediaType="image"
					:colors="colors"
					:max="20"
					:headers="headers"
					:action="uploadInfo.server+uploadInfo.single||''"
					:preview-image-width="1200"
					:width="236"
					:height="180"
					:border-radius="8"
					:add="isEdit==true"
					:remove="isEdit==true"
					@uploadSuccess="uploadFaceSuccess"
					@imgDelete="imgDelete">
				</ut-image-upload>
			</view>
			<view class="margin-top-lg text-content">
				<view>描述：</view>
				<u--textarea v-model="form.remark" placeholder="请输入内容" :maxlength="500" count confirmType="done"></u--textarea>
			</view>
		</view>

		<ut-fixed safe-area-inset position="bottom" background="#fff" v-if="isEdit==true">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :color="colors" text="保存现场照片" size="normal"  @click="saveScene"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight:0,
			secondId:'',
			isEdit:false,
			
			myPhotos: [],
			
			setInfo:{},
			form:{},
			remark:'',
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers(){
			return {
				Token:this.uploadInfo.token
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.secondId) this.secondId = options.secondId
		if (options.isEdit) this.isEdit = options.isEdit =='true'
		
		this.getSceneSetInfo()
		this.getSceneInfo()
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		uploadFaceSuccess(res) {
			res.forEach(img=>{
				const item=img.data
				//this.myPhotos.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
				this.myPhotos.push({
					id:'',
					url:this.uploadInfo.preview + '?file=' + item.name + item.ext,
				})
			})

		},
		imgDelete(e) {
			console.log(e)
			//this.form.imgHead = ''
		},
		async getSceneSetInfo() {
			if (!this.secondId) return
			const {data} = await this.$ut.api('mang/nurse/second/scene/setInfo', {
				communityId: this.community.id
			})
			this.setInfo=data
		},
		async getSceneInfo() {
			if (!this.secondId) return
			const {data} = await this.$ut.api('mang/nurse/second/scene/list', {
				communityId: this.community.id,
				secondId:this.secondId,
			})
			this.form=data
			data.imgs.forEach(item=>{
				this.myPhotos.push({
					id:item.id,
					url:item.url,
				})
			})
		},
		saveScene(){
			if (!this.secondId) return
			this.$ut.api('mang/nurse/second/scene/save', {
				communityId: this.community.id,
				secondId:this.secondId,
				remark:this.form.remark,
				imgs:this.myPhotos,
			}).then(()=>{
				uni.showToast({
					title:'保存成功',
					icon:'none',
				})
				setTimeout(()=>{
					this.$tools.back('loadInfo()')
				},1500)
				//this.$tools.back('getFirstStateInfo')
			})
		}
	},
}
</script>

<style lang="scss" scoped>
	:deep(.ut-image-upload-list) {
		justify-content: flex-start;
		align-items: center;
	}
</style>

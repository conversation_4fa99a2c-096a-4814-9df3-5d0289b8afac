<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="文件资料" navbarType="2" />
		
		<view>
			<template v-for="(item,index) in fileData">
			<view :key="index" class="padding-sm bg-white text-content item-box">
				<view class="text-bold text-lg">{{item.title}}</view>
				<template v-for="(detail,dIndex) in item.details">
				<view :key="dIndex" class="padding-left-lg">
					<view>
						<text>{{detail.title}}</text>
						<text v-if="detail.require" class="text-xs margin-left-xs" :style="{color:colors}">(必填)</text>
						<text v-else class="text-xs margin-left-xs" >(选填)</text>
					</view>
					<view class="image-box">
						<ut-image-upload
							ref="upload"
							name="file"
							v-model="detail.files"
							mediaType="image"
							:colors="colors"
							:max="20"
							:headers="headers"
							:action="uploadInfo.server+uploadInfo.single||''"
							:preview-image-width="1200"
							:width="220"
							:height="180"
							:border-radius="8"
							:disabled="false"							
							:add="isEdit==true"
							:remove="isEdit==true"
							@uploadSuccess="uploadFaceSuccess($event,detail)"
							>
						</ut-image-upload>
					</view>
					<view class="sub-box"></view>
				</view>
				</template>
				
			</view>
			</template>
		</view>
		<view class="padding-tb-xl margin-bottom-lg"></view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff" v-if="isEdit==true">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :color="colors" text="保存文件资料" size="normal" @click="showConfirmHandle"></u-button>
			</view>
		</ut-fixed>
		
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			customerId:'',
			fileData:[],
			
			secondId:'',
			isEdit:false,
			fileData:[],
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers(){
			return {
				Token:this.uploadInfo.token
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.secondId) this.secondId = options.secondId
		if (options.isEdit) this.isEdit = options.isEdit =='true'
		
		this.getCustomerFileData()
		
	},
	methods: {
		async getCustomerFileData(){
			if(!this.secondId) return
			const {data} = await this.$ut.api('mang/nurse/second/fileData/allList', {
				communityId:this.community.id,
				secondId:this.secondId,
			})
			this.fileData=data
		},
		uploadFaceSuccess(files,item){
			files.forEach(file=>{
				item.files.push({
					id:'',
					url:this.uploadInfo.preview + '?file=' + file.data.name + file.data.ext
				})
			})
		},
		showConfirmHandle(){
			let datas=[]
			this.fileData.forEach(item=>{
				item.details.forEach(detail=>{
					let di={dataId:detail.id}
					di.urls=[]
					detail.files.forEach(file=>{
						di.urls.push(file.url)
					})
					datas.push(di)
				})
				
			})

			this.$ut.api('mang/nurse/second/fileData/save', {
				communityId:this.community.id,
				secondId:this.secondId,
				datas:datas,
			}).then(()=>{
				uni.showToast({
					title:'保存成功',
					icon:'none',
				})
				setTimeout(()=>{
					this.$tools.back('loadInfo()')
				},1500)
			})
		}
	},
}
</script>

<style lang="scss" scoped>
.sub-box{
	padding-top: 10rpx;
	border-bottom: 1rpx dashed #a0a0a0;
	margin-bottom: 10rpx;
}

.item-box{
	border-bottom: 1rpx solid #a0a0a0;
	
	&:last-child{
		border-bottom: none;
	}
}
</style>
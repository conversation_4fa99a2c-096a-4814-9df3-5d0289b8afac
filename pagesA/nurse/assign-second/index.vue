<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="复评人员" navbarType="1">
<!-- 				<template slot='right'>
					<view class="text-white text-sm">搜索</view>
				</template> -->
			</f-navbar>
			<view :style="{backgroundColor:'#fff'}" class="padding-sm">
				 <u-input prefixIcon="search" placeholder="输入关键字搜索" v-model="pageReq.key" shape='circle' border="surround" clearable>
					<template slot="suffix">
						<u-button v-if='pageReq.key' text="搜索" type="success" size="mini"	 @click="downCallback"></u-button>
					</template>
				  </u-input>
			</view>
		</ut-top>
		<mescroll-body
			@init="mescrollInit"
			:top="topWrapHeight+'px'"
			:top-margin="-topWrapHeight+'px'"
			bottom="20"
			:up="upOption"
			@down="downCallback"
			@up="upCallback"
			@emptyclick="emptyClick"
		>

			<template v-for="(item,index) in dataList">	
				<customer-item :key="index" :detail="item" :colors="colors" @select="select">
					<template #op>
						<u-button type="primary" shape="circle" :color="colors" text="复评" :plain="false" size="small" @tap="select(item)"/>
					</template>
					
				</customer-item>
			</template>
		</mescroll-body>
		
		<u-popup v-if="showCheckIn" :show="showCheckIn" mode="bottom" round="10" :closeable="true"
			:safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
			:overlay-style="{zIndex:998}" @close="showCheckIn=false">
			<view class="padding-tb-xs text-center">签到</view>
			<!-- <punch-off :range-info="rangeInfo" :work-id="workId" :customer="customer" :checkInfo="checkOutInfo" :location="location" :markers="markers" :colors="colors" @close="handlePunchOffClose" /> -->
			<check-in :customer="selectItem" @close="handleCheckInClose"></check-in>
		</u-popup>
		
		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import CustomerItem from '@/pagesA/components/customer-item.vue'
import checkIn from './check-in.vue'

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
		CustomerItem,
		checkIn,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			dataList:[],
			selectItem:{},
			showCheckIn:false,
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	watch:{
		'pageReq.key':{
			handler(v){
				if(!v) this.downCallback()
			}
		}
	},
	onLoad: async function (options) {
		await this.$onLaunched

	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
		},
		async upCallback(page) {
			this.isShow = false

			this.$ut.api('mang/nurse/second/listpg', {
				communityId:this.community.id,
				...this.pageReq,
			}).then(({data}) => {
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	

			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		emptyClick() {

		},
		jumpSecond(){
			this.$tools.routerTo('/pagesA/nurse/assign-second/second', { secondId: this.selectItem.id })
		},
		select(item) {
			this.selectItem=item
			if(item.nurseCheckInTime){
				this.jumpSecond()
				return
			}
			if(item.longitude && item.latitude){
				this.showCheckIn=true
				
			}else{
				this.jumpSecond()
			}			
		},
		handleCheckInClose(){
			this.downCallback()
			this.jumpSecond()
		}

	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
.page {
	background-color: #fff;
}

</style>

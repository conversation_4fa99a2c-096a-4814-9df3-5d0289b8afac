<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="现场打卡定位" navbarType="2" />

		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<view class="flex">
				<view class="img-head">
					<image :src="userInfo.headimgurl?userInfo.headimgurl:headUrl" v-if="userInfo"></image>
					<image :src="require('@/pages/images/logo.png')" v-else @click.stop="myLogin"></image>
				</view>
				<view class="padding-lr text-content">
					<view class="text-bold text-xl">昵称：{{userInfo.nickname}}</view>
					<view>昵称：{{userInfo.nickname}}</view>
					<view>电话：{{userInfo.phone}}</view>
				</view>
			</view>
		</view>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			
			<view v-if="signInfo && signInfo.longitude && signInfo.latitude" class="padding-lr-sm">
				<map style="width:100%;height:450rpx" id="myMap" :scale="16" :latitude="signInfo.latitude" :longitude="signInfo.longitude" :markers="markers" :circles="circles" show-location></map>
			</view>
			<view class="text-content">
				<view class="basis-df bg-gray padding-lr padding-tb-sm margin-bottom">
					<view v-if="signInfo.checkInTime">签到</view>
					<view v-if="signInfo.checkInTime">
						<view>时间：{{signInfo.checkInTime}}</view>
						<view>地点：{{signInfo.address}}</view>
						<view v-if="signInfo.inRange" class="text-center">
							<text :style="{color:colors}">规定范围内</text>
						</view>
					</view>
					<view v-else>还未签到，开启定位功能，点击下方签到</view>
				</view>
			</view>
			<view v-if="isEdit==true" class="flex flex-direction justify-center align-center padding-tb-lg">
				<view v-if="rangeInfo.inRange && !isSave" class="btn-round margin-bottom-xl bg-green" @click="sign">
					<view>签到</view>
				</view>
				<view v-if="isSave" class="btn-round margin-bottom-xl bg-blue">
					<view>已签</view>
				</view>
				<view v-else class="btn-round margin-top-xl bg-gray">
					<view class="text-green">无法签到</view>
				</view> 
			</view>
			<view v-else>
				已经提交，无需重复操作
			</view>
			<view v-if="addressInfo.address" class="text-content">当前住置：{{addressInfo.address}}</view>
			<view v-if="isGPS" class="text-content text-center">
				<view v-if="!rangeInfo.inRange">
					<view v-if="!rangeInfo.inRange" class="text-red">当前位置位置不在签到范围内</view>
					<view @tap="openMap">点击查看位置</view>
				</view>
				<view v-else class="text-green">位置在规定范围内，可以打卡！</view>
			</view>
		</view>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<view class="text-df text-gray">提示：</view>
			<view class="text-df text-gray" @tap="openMap">要求在规定范围
				<text :style="{color:colors}" class="text-bold">{{signInfo.requireDistance}}米</text>
				内,点击<text :style="{color:colors}">此处</text>查看</view>
		</view>

		<view class="padding-tb-xl margin-bottom-lg"></view>
		<view></view>

	</ut-page>
</template>

<script>
	var app = getApp()
	import {
		mapState
	} from 'vuex'
	export default {
		components: {},
		options: {
			styleIsolation: 'shared',
		},
		data() {
			return {
				colors: '',
				noClick: true,
				secondId: '',
				signInfo:{},
				addressInfo:{},
				isGPS:false,
				rangeInfo:{},
				location:{},
				isSave:false,
				isEdit:false,
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				token: state => state.user.token,
				userInfo: state => state.user.info,
				community: state => state.init.community,
				uploadInfo: state => state.init.oss,
			}),
			position() {
				return {
					latitude: this.signInfo.requireLatitude,
					longitude: this.signInfo.requireLongitude,
					name: this.signInfo.requireName,
					address: this.signInfo.requireAddress,
				}
			},
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor,
			})
		},
		onLoad: async function(options) {
			await this.$onLaunched

			if (options.secondId) this.secondId = options.secondId
			if (options.isEdit) this.isEdit = options.isEdit =='true'

			this.getPositionInfo()
		},
		methods: {
			async getPositionInfo() {
				if (!this.secondId) return
				const {data} = await this.$ut.api('mang/nurse/second/checkin/info', {
					communityId: this.community.id,
					secondId: this.secondId,
				})
				this.signInfo=data
				if(data.inRange)this.isSave=true
				if (!data.checkInTime && !this.isSave) {
					this.getLocation()
				}

			},
			
			getLocationInfo(location){
				this.$ut.api('comm/locationInfo',{
					commKey:this.commKey,
					longitude:location.longitude,
					latitude:location.latitude
				}).then((res)=>{
					this.addressInfo=res.data
				})
			},
			async getLocation() {
				if (!this.secondId) return

				this.$wxsdk.getLocationToAddress().then(async (location) => {
					this.location=location
					if(!location) return
					this.isGPS=true
					const {data} = await this.$ut.api('mang/nurse/second/checkin/range',{
						communityId: this.community.id,
						secondId: this.secondId,
						latitude:location.latitude,
						longitude:location.longitude,
					})
					this.rangeInfo=data
					//this.getLocationInfo(location)
				}).catch(err => {
					console.log(err)
				})
			},
			openMap(){
				this.$wxsdk.openLocation(this.position)
			},
			sign(){
				this.$ut.api('mang/nurse/second/checkin/save',{
					communityId: this.community.id,
					secondId: this.secondId,
					latitude:this.location.latitude,
					longitude:this.location.longitude,
					address:this.addressInfo.address,
				}).then(()=>{
					this.isSave=true
					this.getPositionInfo()
				})
			}
		},
	}
</script>

<style lang="scss" scoped>
	.img-head image {
		flex-shrink: 0;
		width: 130upx;
		height: 130upx;
		border: 5upx solid #fff;
		border-radius: 50%;
	}

	.btn-round {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		align-content: center;
		justify-content: center;
		letter-spacing: 4rpx;
	}
</style>
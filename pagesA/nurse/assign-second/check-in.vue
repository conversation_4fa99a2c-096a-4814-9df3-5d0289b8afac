<template>
	<view>
		<view v-if="customer && customer.longitude && customer.latitude" class="padding-lr-sm">
			<map v-if="step==0" style="width:100%;height:450rpx" id="myMap" :scale="16" :latitude="customer.latitude" :longitude="customer.longitude" :markers="markers" :circles="circles" show-location></map>
		</view>
		<view v-if="step==0" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<view class="text-content text-sm" v-if="!rangeInfo.inRange">
				<view class="basis-df bg-gray padding-lr padding-tb-sm margin-bottom">
					<view v-if="signInfo.requireLocationAddress">规定位置：{{signInfo.requireLocationAddress}}</view>
					<view class="text-center">请您在红圈内，并点击下方签到</view>
				</view>
			</view>
			<view class="flex flex-direction justify-center align-center padding-tb-sm">
				<view v-if="rangeInfo.inRange" class="btn-round margin-bottom-xl" :class="rangeInfo.isCustomerLocate?'bg-green':'bg-blue'" @click="sign_step0">
					<view>签到</view>
				</view>
				<view v-else class="btn-round bg-gray">
					<view class="text-green">无法签到</view>
				</view>
			</view>
			<view>
				<view  class="text-content text-center">
					<view v-if="!rangeInfo.inRange">
						<view v-if="!rangeInfo.inRange" class="text-red">当前位置不在签到范围内</view>
						<!-- <view @tap="showMap" class="text-gray">点击查看打卡位置</view> -->
					</view>
					<view v-else class="text-green">位置在规定范围内，可以签到！</view>
					
					<view v-if="!rangeInfo.inRange && rangeInfo.distance"  class="text-center text-content text-xs">距离目标位置：{{rangeInfo.distance}}km</view>
				</view>
			</view>
		</view>
<!-- 		<view v-if="step==0 && !rangeInfo.inRange" class="cu-card text-sm margin-sm radius padding-sm  shadow s-gray bg-white">
			<view class="text-gray" @tap="showMap">要求在规定范围
				<text v-if="rangeInfo.requireDistance" :style="{color:colors}" class="text-bold">{{rangeInfo.requireDistance}}米</text>
				<text v-else :style="{color:colors}" class="text-bold">(距离无要求)</text>
				内,点击<text :style="{color:colors}">此处</text>查看打卡位置
			</view>
		</view> -->
		
		<view v-if="step==1">
			<view class="bg-white padding-tb-sm padding-lr-sm" style="min-height: 50vh;">
				<view v-if="signInfo.imgMinCount" class="margin-tb padding-lr-xs" :style="{color:colors}">至少需要 {{signInfo.imgMinCount}}张现场照片</view>
				<view class="image-box">
					<ut-image-upload
						ref="upload"
						name="file"
						v-model="myPhotos"
						mediaType="image"
						:colors="colors"
						:max="signInfo.imgMaxCount?signInfo.imgMaxCount:20"
						:headers="headers"
						:action="uploadInfo.server+uploadInfo.single||''"
						:sourceType="['camera']"
						:preview-image-width="1200"
						:width="180"
						:height="180"
						:border-radius="8"
						@uploadSuccess="uploadFaceSuccess"
						@imgDelete="imgDelete">
					</ut-image-upload>
				</view>
				<view class="margin-top-lg padding-lr-sm text-content">
					<view>描述：</view>
					<u--textarea v-model="remark" placeholder="请输入内容" :maxlength="500" count confirmType="done"></u--textarea>
				</view>
			</view>
			
			<ut-fixed safe-area-inset position="bottom" background="#fff">
				<view class="padding-tb-sm padding-lr-lg">
					<u-button type="primary" shape="circle" :color="colors" :disabled="(!myPhotos.length || (myPhotos.length<rangeInfo.imgMinCount && rangeInfo.imgMinCount>0))" text="保存签退" size="normal"  @click="$shaken(work)"></u-button>
				</view>
			</ut-fixed>
		</view>
		
		
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		

	</view>
</template>

<script>
	var app = getApp()
	import {
		mapState
	} from 'vuex'
	export default {
		components: {},
		options: {
			styleIsolation: 'shared',
		},
		props:{
			colors:{
				type:String,
			},
			date:{
				type:String,
			},
			workId:{
				type:String,
			},
			customer:{
				type:Object,
				default:()=>{},
			},
			checkInfo:{
				type:Object,
				default:()=>{},
			},
		},
		data() {
			return {
				noClick: true,
				step:0,
				myPhotos: [],
				remark:'',
				rangeInfo:{},
				markers:[],
				circles:[],
				intervalTime:{},
				location:{},
				signInfo:{},
				
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				token: state => state.user.token,
				userInfo: state => state.user.info,
				community: state => state.init.community,
				uploadInfo: state => state.init.oss,
			}),
			headers(){
				return {
					Token:this.uploadInfo.token
				}
			},
		},
		onShow() {
			this.setData({colors: app.globalData.newColor})
		},
		onLoad: async function(options) {
			await this.$onLaunched	
		},
		mounted() {
			this.step=0
			this.getSignInfo()
		},
		destroyed() {
			clearInterval(this.intervalTime)
		},
		methods: {
			openMap() {
				this.$wxsdk.openLocation({
					latitude: this.customer.latitude,
					longitude: this.customer.longitude,
					name: this.signInfo.requireName,
					address: this.customer.name,
				})
			},
			async getSignInfo() {
				uni.showLoading({
					title:'请等待...'
				})
				if (!this.customer.id) return
				const {data} = await this.$ut.api('mang/nurse/second/checkin/info', {
					communityId: this.community.id,
					secondId: this.customer.id,
				}).finally(()=>{
					uni.hideLoading()
				})
				this.signInfo=data
				this.circles=[{
						longitude: this.customer.longitude,
						latitude: this.customer.latitude,
						fillColor: "#FF2B431A",
						color: "#FF0000",
						radius: this.signInfo?.requireDistance?this.signInfo?.requireDistance:2000,
						strokeWidth: 2
					}]
						
					this.getLocation()
		
				
			
			},
			sign_step0() {
				this.$emit('stopTime')
				//this.checkInfo.needImg=true
				if(this.signInfo.needImg){ 
					setTimeout(()=>{
						this.step=1
					},200)
					
				}else
				{
					this.work()
				}
			},
			work(){
				this.$ut.api('mang/nurse/second/checkin/save', {
					communityId: this.community.id,
					secondId: this.customer.id,
					latitude:this.location.latitude,
					longitude:this.location.longitude,
					address:'',
					photoRemark:this.remark,
					imgs:this.myPhotos,
				}).then((res) => {
					this.$emit('close',res.data)
				})
			},
			showMap() {
				console.log(this.checkInfo)
				this.$wxsdk.openLocation({
					latitude: this.checkInfo.requireLatitude,
					longitude: this.checkInfo.requireLongitude,
					name: '我',
					address: '规定的打卡位置',
				})
			},
			uploadFaceSuccess(res) {
				res.forEach(img=>{
					const item=img.data
					this.myPhotos.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
				})
			
			},
			imgDelete(e) {
				console.log(e)
				//this.form.imgHead = ''
			},
			async getLocation() {			
				this.$wxsdk.getLocationToAddress().then(async (location) => {
					this.location = location
					if (!location) return
					const {data} = await this.$ut.api('mang/nurse/second/checkin/range',{
						communityId: this.community.id,
						secondId: this.customer.id,
						latitude:location.latitude,
						longitude:location.longitude,
					})
					this.rangeInfo = data ||{}
					
					if(!this.rangeInfo || !this.rangeInfo.inRange){
						clearInterval(this.intervalTime)
						this.intervalTime = setInterval(() => {
						  this.getLocation();  // 接口方法
						}, 5000);
					}
					
					
				}).catch(err => {
					console.log(err)
				})
			},
		},
	}
</script>

<style lang="scss" scoped>
	.img-head image {
		flex-shrink: 0;
		width: 130upx;
		height: 130upx;
		border: 5upx solid #fff;
		border-radius: 50%;
	}

	.btn-round {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		align-content: center;
		justify-content: center;
		letter-spacing: 4rpx;
	}
</style>
<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" :title="attendantName+'护理员客户'" navbarType="1"></f-navbar>
		</ut-top>
		<view class="content" :style="{height:'calc(100vh - '+topWrapHeight+'px)'}">
			<map id="map" :latitude="latitude" :longitude="longitude" :show-location="false"
				:style="{ width: '100%', height:  '100%' }" :scale="12" @markertap="markTap" @labeltap="markTap"></map>
		</view>
		<u-popup :show="showDetail" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="998" :overlay-style="{zIndex:998}" @close="showDetail=false">
		
			<view class="pop-content">
				<view class="item-box">
						<view class="head-image">
							<text v-if="!detail.imgHead" class="cuIcon-people" />
							<u-lazy-load v-else :image="$tools.showImg(detail.imgHead)" width="120" height="160" border-radius="4" @click="$tools.previewImage([detail.imgHead],0,800)" />
						</view>
						<view class="content text-content">
							<view class="flex justify-between ">
								<view>
									<view class="flex justify-start align-center">
										<view class="text-xl text-bold" >{{detail.name}}</view>
										<view class="text-df text-blue margin-left-lg" v-if="detail.sex==1">男<text class="cuIcon-male" /></view>
										<view class="text-df text-pink margin-left-lg" v-if="detail.sex==2">女<text class="cuIcon-female" /></view>
									</view>
									
									<view><text v-if="detail.phone" class="cuIcon-phone" />{{detail.phone}}</view>
								</view>
		
							</view>
							
							<view class="text-xs text-gray address">{{detail.address}}</view>
						
						</view>
					</view>
			</view>
		</u-popup>
	</ut-page>
</template>

<script>
var app = getApp()
import {mapState} from 'vuex'

export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			
			attendantId:'',
			attendantName:'',
			
			_mapContext: null,
			latitude: 0,
			longitude: 0,
			
			mapMarkers:[],
			customerList:[],
			showDetail:false,
			selIndex:0,
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	onReady() {
		this._mapContext = uni.createMapContext("map", this);
		
	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.attendantId) {
			this.attendantId = options.attendantId
		}
		
		this.getList()
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
		detail(){
			if(this.selIndex<=0) return {}
			if(!this.mapMarkers || !this.mapMarkers.length) return {}
			return this.mapMarkers[this.selIndex-1]
		}
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		markTap(e) {
			this.selIndex=e.markerId
			this.showDetail=true
		},

		init() {
			let obj=this.mapMarkers.find(u=>u.type=='attendant')
			if(obj){
				this.latitude=obj.latitude
				this.longitude=obj.longitude
			}else{
				obj=this.mapMarkers.find(u=>u.type=='new')
				if(obj){
					this.latitude=obj.latitude
					this.longitude=obj.longitude
				}else{
					this.latitude=this.mapMarkers[0].latitude
					this.longitude=this.mapMarkers[0].longitude
				}
			}
			
			// 仅调用初始化，才会触发 on.("markerClusterCreate", (e) => {})
			this._mapContext.initMarkerCluster({
				enableDefaultStyle: false, // 是否使用默认样式
				zoomOnClick: true, // 点击聚合的点，是否改变地图的缩放级别
				gridSize: 30, // 聚合计算时网格的像素大小，默认60
				complete(res) {
					console.log('initMarkerCluster', res)
				}
			})
			this._mapContext.on("markerClusterCreate", (res) => {
				console.log("markerClusterCreate", res);
				const clusters = res.clusters
				const markers = clusters.map(cluster => {
					const {	center,	clusterId,	markerIds} = cluster
					return {
						...center,
						iconPath: '/static/imgs/map/none.png',
						width: 1,
						height: 1,
						clusterId, // 必须
						label: {
							content: markerIds.length + '',
							fontSize: 16,
							color: '#ffffff',
							width: 40,
							height: 40,
							bgColor: '#00A3FA',
							borderRadius: 20,
							textAlign: 'center',
							anchorX: 0,
							anchorY: -30,
						}
					}
				})
				this._mapContext.addMarkers({
					markers,
					clear: false,
					complete(res) {
						console.log('clusterCreate addMarkers', res)
					}
				})
			});
			this._mapContext.on('markerClusterClick', (res) => {
				console.log('markerClusterClick', res)
			})
			this.addMarkers();
		},

		// 添加标记点
		addMarkers() {
			const markers = []
			this.mapMarkers.forEach((p, i) => {
				var path='/static/imgs/map/marker_blue.png'
				if(p.type=='new') path='/static/imgs/map/small.png'
				if(p.type=='customer')path='/static/imgs/map/big.png'
				markers.push(
					Object.assign({}, {
						id: p.id,
						latitude:p.latitude,
						longitude:p.longitude,
						iconPath: path,
						width: 28,
						height: 36,
						joinCluster: true, // 指定了该参数才会参与聚合
						callout: {
							bgColor: "#5AC2EB",
							color: "#fff",
							content: `${p.name}`,
							display: "ALWAYS",
							fontSize: "13",
							fontWeight: "bold",
							padding: 4,
							textAlign: "center",
							borderRadius:2,
						}
					})
				)
			})
			this._mapContext.addMarkers({
				markers,
				clear: false,
				complete(res) {
					console.log('addMarkers', res)
				}
			})
		},
	
		async getList(){
			const {data} = await this.$ut.api('mang/nurse/attendant/customer/locationAll', {
				communityId:this.community.id,
				module:'long',
				attendantId:this.attendantId,
			})
			
			this.customerList=data.customers || []
			this.mapMarkers=[]
			var index=1
			if(data){
				this.attendantName=data.name
				this.mapMarkers.push({
					id:index,
					type:'attendant',
					name:data.name,
					latitude:data.latitude,
					longitude:data.longitude,
					imgHead:data.imgHead?data.imgHead:'',
					address:data.address?data.address:'',
				})
				index++
			}
			if(data && data.customers){
				data.customers.forEach(item=>{
					if(item.longitude && item.latitude){
						this.mapMarkers.push({
							id:index,
							type:'customer',
							name:item.name,
							latitude:item.latitude,
							longitude:item.longitude,
							customerName:item.name,
							imgHead:item.imgHead,
							address:item.address,
						})
						index++
					}
				})
			}
			if(data && data.newCustomer){
			  this.mapMarkers.push({
				  id:index,
				  type:'new',
				  name:'新客户',
				  lat:data.newCustomer.latitude,
				  lng:data.newCustomer.longitude,
			  })
			}
			this.init();
		}
	},
}
</script>
<style lang="scss" scoped>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 50rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}
	
	.pop-content{
		margin-top: 60rpx;
	}
	
	.item-box{
		padding: 20rpx 30rpx; 
		background-color: white; 
		margin-bottom: 10rpx;
		display: flex;
		position: relative;
		overflow: hidden;
		.content{
			flex: 1;
			padding: 0 20rpx;
		}
		
	}
	
	.block-btn {
		width: 190rpx;
		height: 60rpx;
		color:#fff;
		background: linear-gradient(90deg, var(--colors), var(--colors));
		border: 2rpx solid rgba(230, 184, 115, 0.3);
		border-radius: 80rpx;
		padding: 8rpx 20rpx;
		font-size: 28rpx;
		font-weight: 700;
	}
	
	.cuIcon-people{
		font-size: 116rpx;
		color: gray;
		border: 1rpx solid #ccc;
		width: 116rpx;
		height: 156rpx;
		line-height: 156rpx;
		border-radius: 6rpx;
		display: block;
	}
	
	.address{
		height: 36rpx;
		overflow: hidden; /* 隐藏溢出的内容 */
		text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
		-o-text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
	
</style>
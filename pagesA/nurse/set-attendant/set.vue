<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="指派护理员" navbarType="2"></f-navbar>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :colors="colors" />
		</view>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white"  @click="show=true">
			<text v-if="!selAttendant.id">点击这里选择护理员</text>
			<view v-else class="flex justify-between">护理员：
				<view>
					<text class="text-lg text-bold">{{selAttendant.name}}</text>
				</view>
				<view>
					<text class="cuIcon-right" />
				</view>
			</view>
			
		</view>
		
		<u-popup :show="show" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" height="400" close-icon-pos="top-left" @close="show=false">
			<view class="pop-title">选择护理员</view>
			<view class="clear" @click="selectAttendant({})">清除</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<template v-for="(item,index) in attendants">
					<attendant-item :key="index" :colors="colors" :detail="item" @detail="goDetail" @select="selectAttendant" />
				</template>
			</scroll-view>
		</u-popup>
		
		<view class="padding-tb-xl margin-bottom-lg"></view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :disabled="!selAttendant.id" :color="colors" :text="!selAttendant.id?'请选择护理员':'确定指派'" size="normal" @click="showConfirmHandle"></u-button>
			</view>
		</ut-fixed>
		
		
		<u-modal :show="showConfirm" :showConfirmButton="true" :showCancelButton="true" :buttonReverse="true" @confirm="$shaken(save)" @cancel="showConfirm=false">
			确认把该顾客指派到 {{selAttendant.name}} 护理员吗？
		</u-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
import AttendantItem from '@/pagesA/components/attendant-item.vue'

export default {
	mixins: [], 
	components: {
		CustomerInfo,
		AttendantItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick:true,
			showConfirm:false,
			
			customer:{},
			show:false,
			attendants:[],
			selAttendant:{},
			
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			
			customerId:'',
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),

	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.customerId) {
			this.customerId = options.customerId
		}
		
		this.getCustomerInfo()
		this.getGroupAttendant()
	},
	methods: {
		async getGroupAttendant(){
			const {data} = await this.$ut.api('mang/nurse/attendant/listpg', {
				communityId:this.community.id,
				module:'long',
				...this.pageReq,
			})
			this.attendants=data.info||[]
		},
		selectAttendant(item){
			this.selAttendant=item
			this.show=false
		},
		async getCustomerInfo(){
			if(!this.customerId) return
			const {data} = await this.$ut.api('mang/nurse/customer/info', {
				communityId:this.community.id,
				module:'long',
				id:this.customerId,
			})
			this.customer=data
		},
		showConfirmHandle(){
			this.noClick=true
			this.showConfirm = true
		},
		save(){
			this.showConfirm = false
			if(!this.customerId || !this.selAttendant.id) return
			this.$ut.api('mang/nurse/setAttendant/set', {
				communityId:this.community.id,
				module:'long',
				customerIds:[this.customerId],
				attendantId:this.selAttendant.attendantId,
			}).then(()=>{
				
				this.$tools.back('downCallback()')
			})
			// this.$tools.routerTo('/pagesA/nurse/set-attendant/index')
		},
		goDetail(item){
			this.$tools.routerTo('/pagesA/nurse/attendant/customer-list', { attendantId: item.attendantId })
			//console.log(item)
		}
		

	},
}
</script>

<style>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}
</style>
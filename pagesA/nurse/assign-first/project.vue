<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="项目评定" navbarType="1" />
		</ut-top>
		<mescroll-body
			ref="mescrollRef"
			:top="topWrapHeight+'px'"
			:top-margin="-topWrapHeight+'px'"
			bottom="140"
			:up="upOption"
			:safearea="true"
			@init="mescrollInit"
			@down="downCallback"
			@up="upCallback"
			@emptyclick="emptyClick"
		>
			<u-swipe-action ref="swipeUserList">
			<template v-for="(item,index) in dataList">	
				<u-swipe-action-item
					:key="index"
					:name="item.id"
					:options="getActionOption(item)"
					@longpress="longpress(item)"
					@click="actionOp">
					<assign-project-item :detail="item" :colors="colors" @select="select" :all-project="projectData" :tip="item.isSubmit?'已提交':''" :is-edit="isEdit" @valChange="valChange">
						<template #op>
							<u-button type="primary" shape="circle" :color="colors" :text="item.isSubmit?'查看':'选择'" :plain="false" size="small" @tap="select(item)"/>
						</template>
					</assign-project-item>
				</u-swipe-action-item>
				
			</template>
			</u-swipe-action>
		</mescroll-body>
		
		<ut-fixed safe-area-inset position="bottom" v-if="isEdit==true">
			<view class="margin bg-white">
				<u-button type="primary" shape="circle" :color="colors" text="新增项目" size="normal"  @click="handleAdd"></u-button>
			</view>
		</ut-fixed>
		
		<u-popup :show="showProject" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="998" :overlay-style="{zIndex:998}" @close="showProject=false">

			<view class="pop-title">项目选择</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<view style="max-height: 75vh;">
				<u-checkbox-group v-model="checkboxValue1"  placement="column">
					<template v-for="(item,index) in projectData">
						<view class="flex justify-between padding-lr-xl padding-tb-sm">
							<view class="text-df text-bold">
								{{item.name}}
								<text class="text-no text-sm text-gray">({{item.govCode}})</text>
							</view>
							<view>
								<u-checkbox v-model="item.checked" :activeColor="colors"  :name="item.id"></u-checkbox>
							</view>
						</view>
					</template>
					</u-checkbox-group>
				</view>
			</scroll-view>
			<view safe-area-inset position="bottom">
				<view class="margin">
					<u-button type="primary" shape="circle" :color="colors" text="增加选中" size="normal" :disabled="!checkboxValue1.length"  @click="handleSave"></u-button>
				</view>
			</view>
		</u-popup>
		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import AssignProjectItem from '@/pagesA/components/assign-project-item.vue'

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
		AssignProjectItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 50,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			dataList:[],
			firstId:'',
			showProject:false,
			projectData:[],
			checkboxValue1:[],
			checked:false,
			isEdit:false,
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
		if(options.firstId)this.firstId = options.firstId		
		if (options.isEdit) this.isEdit = options.isEdit =='true'
		
		this.getAllProject()
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
			
			this.$refs['swipeUserList'].closeAll()
		},
		async upCallback(page) {
			if(!this.firstId) return 
			this.$ut.api('mang/nurse/first/project/listpg', {
				communityId:this.community.id,
				firstId:this.firstId,
				...this.pageReq,
			}).then(({data}) => {		
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	
			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		emptyClick() {

		},
		getActionOption(item) {
			const btnSubmitCancel = {
				text: '取消项目',
				code:'submitCancel',
				style: {
					backgroundColor: '#ffaa7f'
				}
			}
							
			const btnDelete = {
				text: '删除项目',
				code:'delete',
				style: {
					backgroundColor: '#f56c6c'
				}
			}

			let data = []
	
			if (item.isSubmit) {
				data.push(btnSubmitCancel)
			} else {
				data.push(btnDelete)
			}
			
			return data
		},
		longpress(item) {
			console.log(item)
		},
		select(item) {
			//this.$tools.routerTo('/pagesA/person/my-apply/info', { firstId: item.id })
		},

		deleteProject(id){
			return new Promise((resolve, reject) => {
				this.$ut.api('mang/nurse/first/project/delete', {
					communityId:this.community.id,
					firstId:this.firstId,
					ids:[id],
				}).then(res=>{
					resolve(res)
				}).catch(err=>{
					reject(err)
				})
			})
		},
		actionOp(data) {
			let that = this
			let content = ''
			if (data.code == 'delete') content = '确认删除该项目吗？'
			uni.showModal({
				title: '操作提示',
				content: content,
				success: res=> {
					if (res.confirm) {
						if (data.code == 'delete') {
							this.deleteProject(data.name).then(()=>{
								let objIndex = this.dataList.findIndex(u => u.id == data.name)
								this.dataList.splice(objIndex, 1)
							})							
						}
					} else if (res.cancel) {
					}
					this.$refs['swipeUserList'].closeAll()
				}
			})
		},
		handleAdd(){
			this.showProject=true
		},
		handleSave(){
			this.$ut.api('mang/nurse/first/project/add',{
				communityId:this.community.id,
				firstId:this.firstId,
				projectIds:this.checkboxValue1,
			}).then(()=>{
				this.showProject=false
				this.downCallback()
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
			})
		},
		async getAllProject(){
			if(!this.firstId) return 
			const {data}= await this.$ut.api('mang/nurse/first/project/allList',{
				communityId:this.community.id,
				firstId:this.firstId,
			})

			this.projectData=data
		},
		valChange(item){
			this.$ut.api('mang/nurse/first/project/time',{
				communityId:this.community.id,
				firstId:this.firstId,
				projectId:item.id,
				monthTimes:item.monthTimes,
			})
		}
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
.page {
	background-color: #fff;
}


.pop-title {
	padding-top: 20rpx;
	text-align: center;
}


</style>

<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="重定位" navbarType="2" />
		</ut-top>
		
		<view class="bg-white padding-tb-sm padding-lr-sm" :style="{minHeight:'calc(100vh - '+topWrapHeight+'px)'}">
			<view class="margin-top-lg padding-lr-sm text-content">
				<view v-if="customerLocation.longitude">
					<view v-if="customerLocation.longitude" @tap="openMapSource" class="text-gray">点击这里查看原始坐标</view>
				</view>
				<view v-else>未记录有任何定位信息</view>
			</view>
			<view class="padding-lr-sm">
				<map style="width:100%;height:600rpx" id="myMap" :scale="18" :latitude="location.latitude" :longitude="location.longitude" :markers="markers" :circles="circles" show-location></map>
			</view>
			<view v-if="isGPS" class="margin-top-lg padding-lr-sm text-content">
				<view v-if="addressInfo && addressInfo.address" class="text-content">当前住置：{{addressInfo.address}}</view>
				<view v-if="addressInfo.location.longitude">
					坐标：{{addressInfo.location.longitude}},{{addressInfo.location.latitude}}
				</view>
				<view v-if="addressInfo.location.longitude" class="text-gray" @tap="openMap">地图上显示</view>
			</view>
			<view v-else>未获取当前信息,请稍等。如果长时间未获得信息，请检查定位权限是否开启。</view>
			<view class="text-center">
				<view v-if="loadingLocation==1">正在获取定位</view>
				<view v-else-if="loadingLocation==2">获取定位完成</view>
				<view v-if="loadingLocation==2" @click="loadGPS">重新获取</view>
			</view>
		</view>
		
		
		<ut-fixed safe-area-inset position="bottom" background="#fff" v-if="isEdit==true">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :disabled="!isGPS" :color="colors" text="保存位置" size="normal"  @click="saveLocation"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			firstId:'',
			isEdit:false,
			topWrapHeight:0,
			
			markers:[],
			circles:[],
			addressInfo:{},
			location:{},
			customerLocation:{},
			gpsCount:0,
			intervalTime:{},
			loadingLocation:0,
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		position() {
			return {
				latitude: this.location.latitude,
				longitude: this.location.longitude,
				address: '当前位置',
			}
		},
		isGPS(){
			if(this.location && this.location.longitude && this.location.latitude) return true
			return false
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.firstId) this.firstId = options.firstId		
		if (options.isEdit) this.isEdit = options.isEdit =='true'
		
		this.getInfo()
		this.loadGPS()
	},
	destroyed() {
		clearInterval(this.intervalTime)
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		async getInfo(){
			if (!this.firstId) return
			uni.showLoading({
				title:'请稍等...'
			})
			const {data} = await this.$ut.api('mang/nurse/first/location/info', {
				communityId: this.community.id,
				firstId: this.firstId,
			}).finally(()=>{uni.hideLoading()})
			this.customerLocation=data
		},
		
		openMapSource(){
			this.$wxsdk.openLocation({
				latitude: this.customerLocation.latitude,
				longitude: this.customerLocation.longitude,
				address: this.customerLocation.address,
			})
		},
		loadGPS(){
			this.gpsCount=0;
			this.getLocation()
			this.loadingLocation=1
			clearInterval(this.intervalTime)
			this.intervalTime = setInterval(() => {
			  this.getLocation();  // 接口方法
			  this.gpsCount++
			  if(this.gpsCount>5){
				  clearInterval(this.intervalTime)
				  this.loadingLocation=2
			  }
			}, 3000);
		},
		getLocationInfo(location){
			this.$ut.api('comm/locationInfo',{
				commKey:this.commKey,
				longitude:location.longitude,
				latitude:location.latitude
			}).then((res)=>{
				this.addressInfo=res.data
			})
		},
		async getLocation() {
			this.$wxsdk.getLocationToAddress().then(location => {
				this.location=location				
				this.circles=[{
						longitude: location.longitude,
						latitude: location.latitude,
						fillColor: "#FF2B431A",
						color: "#FF0000",
						radius: 1,
						strokeWidth: 2
					}]
				
			}).catch(err => {
			})
		},
		openMap(){
			this.$wxsdk.openLocation(this.position)
		},
		saveLocation(){
			if (!this.firstId) return
			this.$ut.api('mang/nurse/first/location/set', {
				communityId: this.community.id,
				firstId: this.firstId,
				longitude:this.location.longitude,
				latitude:this.location.latitude,
				address:this.addressInfo.address,
			}).then(()=>{
				uni.showToast({
					title:'保存成功',
					icon:'none',
				})
				setTimeout(()=>{
					this.$tools.back('loadInfo()')
				},1500)
			})
		}
	},
}
</script>

<style>
</style>
<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="初评" navbarType="2"></f-navbar>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :show-all="false" :colors="colors" :file-data="fileData" />
			<view class="flex align-center">
				<text class="text-sm" :style="{color:colors}">({{customer.groupName}})</text>
				<text v-if="customer.attendantName">护理员：</text>
				<text v-if="customer.attendantName" class="text-lg text-bold">{{customer.attendantName}}</text>
				<text v-if="!customer.attendantName" class="text-red text-bold">未指定护理员</text>
			</view>
		</view>
		
		<view class="cu-card margin-sm radius shadow padding-sm s-gray bg-white">
			<u--form ref="form" labelPostion="left" :model="form" :rules="rules" labelWidth="auto" :labelStyle="{color:'#a0a0a0'}">
				<view class="date text-df">
					<u-form-item label="有效时间(起)" prop="validBegin" borderBottom>
						<view class="full-width" @click="$refs.dateValidBeginPicker.show()" >
							<u--input v-model.trim="form.validBegin" border="none" disabled disabledColor="#ffffff"
								placeholder="请选择开始有效时间" inputAlign='center' style="pointer-events:none" />
						</view>
					</u-form-item>

					<u-form-item label="有效时间(止)" prop="validEnd" borderBottom>
						<view class="full-width" @click="$refs.dateValidEndPicker.show()" >
							<u--input v-model.trim="form.validEnd" border="none" disabled disabledColor="#ffffff"
								placeholder="请选择截止有效时间" inputAlign='center' style="pointer-events:none" />
						</view>
					</u-form-item>
					
				</view>
			</u--form>
		</view>
		
		<view class="cu-card margin-sm radius shadow padding-tb s-gray bg-white">
			<hh-grid  :colors="colors" :list="menus" :count="menus.length>4?4:menus.length" :cell-color="true" :jump-param="jumpParam" />
		</view>
		
		<view class="cu-card margin-sm radius shadow padding-sm s-gray bg-white">
			<view class="text-content">
				<view>备注：</view>
				<u--textarea v-model="form.remark" placeholder="请输入内容" :maxlength="500" count confirmType="done"></u--textarea>
			</view>
		</view>
		
		<view class="padding-tb-xl margin-lg"></view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button v-if="isEdit" type="primary" shape="circle" :color="colors" text="提交初评" size="normal"  @click="goSubmit"></u-button>
				<u-button v-else shape="circle" :disabled="true" :color="colors" text="已经提交,无需重复提交" size="normal"></u-button>
			</view>
		</ut-fixed>
		
		
		<pick-date ref="dateValidBeginPicker" :start-year="new Date().getFullYear()" 
			:time-init="0"
			:time-hide="[true, true, true, false, false, false]" 
			:time-label="['年', '月', '日', '时', '分', '秒']"
			@submit="validBeginSelect" />
			
		<pick-date ref="dateValidEndPicker" :start-year="new Date().getFullYear()" 
			:time-init="0"
			:time-hide="[true, true, true, false, false, false]" 
			:time-label="['年', '月', '日', '时', '分', '秒']"
			@submit="validEndSelect" />

	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
import HhGrid from '@/pagesA/components/hh-grid.vue'
import PickDate from '@/pagesA/components/pickDate.vue'

export default {
	mixins: [], 
	components: {
		CustomerInfo,
		HhGrid,
		PickDate,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			
			firstId:'',
			customer:{},
			show:false,
			attendants:[],
			selAttendant:{},
			fileData:[],
			
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			isEdit:false,
			form:{
				validBegin:'',
				validEnd:'',
				remark:'',
			},
			rules: {
				validBegin: [{
					required: true,
					message: '请选择开始时间',
					trigger: ['blur', 'change']
				}],
				validEnd: [{
					required: true,
					message: '请选择结束时间',
					trigger: ['blur', 'change']
				}],
			},
			
			menus:[
				// {
				// 	'name': '打卡定位',
				// 	'url': '/pagesA/nurse/assign-first/position',
				// 	'image': 'https://oss.afjy.net/api/file/preview?file=e0K3xVj',
				// 	'code':'location',
				// 	'num': 0,
				// 	'star':false,
				// },
				{
					'name': '现场照片',
					'url': '/pagesA/nurse/assign-first/scene-photo',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K7iQQ',
					'code':'photo',
					'num': 0,
					'star':false,
				},{
					'name': '文件资料',
					'url': '/pagesA/nurse/assign-first/file-data',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',
					'code':'data',
					'num': 0,
					'star':false,
				},{
					'name': '项目评定',
					'url': '/pagesA/nurse/assign-first/project',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',
					'code':'project',
					'num': 0,
					'star':false,
				},{
					'name': '头像修改',
					'url': '/pagesA/nurse/assign-first/img-head',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',
					'code':'imgHead',
					'num': 0,
					'star':false,
				},{
					'name': '信息修改',
					'url': '/pagesA/nurse/assign-first/save',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',
					'code':'info',
					'num': 0,
					'star':false,
				},{
					'name': '位置重定位',
					'url': '/pagesA/nurse/assign-first/location',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',
					'code':'reLocation',
					'num': 0,
					'star':false,
				},
			],
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
		jumpParam(){
			return {firstId:this.firstId,isEdit:this.isEdit}
		}
	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.firstId) {
			this.firstId = options.firstId
		}
		
		this.loadInfo()
	},
	methods: {
		loadInfo(){
			this.getCustomerInfo()
			this.getFirstStateInfo()
		},
		async getCustomerInfo(){
			if(!this.firstId) return
			const {data} = await this.$ut.api('mang/nurse/first/info', {
				communityId:this.community.id,
				firstId:this.firstId,
			})
			this.customer=data ||{}
		},
		setStar(code,v){
			let obj=this.menus.find(u=>u.code==code)
			if(obj)this.$set(obj,'star',v===true)
		},
		async getFirstStateInfo(){
			if(!this.firstId) return
			const {data} = await this.$ut.api('mang/nurse/first/state_info', {
				communityId:this.community.id,
				firstId:this.firstId,
			})
			this.setStar('location',data.needLocate)
			this.setStar('photo',data.needPhoto)
			this.setStar('data',data.needData)
			this.setStar('project',data.needProject)
			this.setStar('imgHead',data.needImgHead)
			this.isEdit=data.isEdit
			this.form.validBegin=data.validBegin
			this.form.validEnd=data.validEnd
			this.form.remark=data.remark
		},
		async goSubmit(){
			let go=true
			await this.$refs.form.validate().then(res => {
			}).catch(errors => {
				go=false
				uni.$u.toast('请检查必须填写的内容！')
				return
			})
			if(!go) return
			
			this.$ut.api('mang/nurse/first/submit', {
				communityId:this.community.id,
				id:this.firstId,
				...this.form,
			}).then(()=>{
				uni.showToast({
					title:'提交成功',
					icon:'none',
				})
				
				setTimeout(()=>{
					this.$tools.back('downCallback()')
				},1500)
			})
		},
		validBeginSelect(e){
			this.form.validBegin=`${e.year}-${e.month}-${e.day}`
		},
		validEndSelect(e){
			this.form.validEnd=`${e.year}-${e.month}-${e.day}`
		},
	},
}
</script>

<style>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}
</style>
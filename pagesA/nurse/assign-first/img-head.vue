<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="头像修改" navbarType="2"></f-navbar>
		<view class="padding-lg flex justify-center">
			<diy-person-upload
				v-model="myPhotos"
				mediaType="image"
				:disabled="false"
				:colors="colors"
				:max="1"
				:width="500"
				:height="600"
				:preview-image-width="1200"
				:border-radius="8"
				:headers="headers"
				:action="uploadInfo.server+uploadInfo.single"
				:add="isEdit==true"
				:remove="isEdit==true"
				@uploadSuccess="uploadFaceSuccess"
				@imgDelete="imgDelete"
			 />
		 </view>

		<view class="padding-lr padding-tb" v-if="isEdit==true">
			<u-button type="primary" shape="circle" :color="colors" text="保存头像" :plain="false" size="normal" @click="save"/>
		</view>
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
import DiyPersonUpload from '@/pagesA/components/ut-person-upload/up-person-upload.vue'
export default {
	components: {
		DiyPersonUpload,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			myPhotos: '',
			form: {
				imgHead: '',
			},
			firstId:'',
			isEdit:false,
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers() {
			return {
				Token: this.uploadInfo.token,
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if(options.firstId)this.firstId = options.firstId		
		if (options.isEdit) this.isEdit = options.isEdit =='true'
		
		this.getCustomerInfo()
	},
	methods: {
		uploadFaceSuccess(res) {
			const item=res.data
			this.$set(this.form, 'imgHead', this.uploadInfo.preview + '?file=' + item.name + item.ext)
		},
		imgDelete(e) {
			this.form.imgHead = ''
		},
		async getCustomerInfo(){
			if(!this.firstId) return
			const {data} = await this.$ut.api('mang/nurse/first/imgHead/info', {
				communityId:this.community.id,
				firstId:this.firstId,
			})
			this.form=data
			if (this.form.imgHead) {
				this.form.imgHead = data.imgHead + '&token=' + this.uploadInfo.token
				this.myPhotos = this.form.imgHead
			}
		},
		save(){
			this.$ut.api('mang/nurse/first/imgHead/save', {
				communityId:this.community.id,
				firstId:this.firstId,
				imgHead:this.form.imgHead,
			}).then(()=>{
				uni.showToast({
					title:'保存成功',
					icon:'none',
				})
				setTimeout(()=>{
					this.$tools.back('loadInfo()')
				},1500)
				
			})
		}
	},
}
</script>

<style>
	.page {
		background-color: #fff;
		height: 100vh;
	}
	
</style>
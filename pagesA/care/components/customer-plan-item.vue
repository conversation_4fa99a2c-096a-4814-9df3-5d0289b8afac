<template>
	<view>
		<view class="item-box">
			<view class="head-image">
				<text v-if="!detail.imgHead" class="cuIcon-people" />
				<u-lazy-load v-else :image="$tools.showImg(detail.imgHead)" width="120" height="160" border-radius="4" />
			</view>
			<view class="content text-content" style="overflow: hidden;" @click="itemClick">
				<view class="flex justify-start align-center">
					<view class="text-df text-bold" >{{detail.name}}</view>
					<view class="text-sm text-blue margin-left-lg" v-if="detail.sex==1">男<text class="cuIcon-male" /></view>
					<view class="text-sm text-pink margin-left-lg" v-if="detail.sex==2">女<text class="cuIcon-female" /></view>
				</view>
				<view class="flex justify-between align-center">
					<view class="flex justify-between">
						<view class="flex">
							<view class="text-df text-bold">{{detail.begin}}</view>
							<view class="margin-left-sm">-</view>
							<view class="margin-left-sm text-df text-bold">{{detail.end}}</view>
						</view>
						
					</view>
					<slot name="op"></slot>
					
				</view>
				
				<view class="text-xs text-gray address">{{detail.address}}</view>
				<view class="text-sm">签到时间：
					<text v-if="detail.checkInTime">{{detail.checkInTime}}</text>
					<text v-else class="text-red">还未签到</text>
				</view>
				<view  v-if="detail.checkOutTime" class="text-sm">签退时间：
					<text>{{detail.checkOutTime}}</text>
				</view>
	<!-- 			<view class="text-sm text-cut">
					<template v-for="(item,index) in detail.projects">
						<view class="margin-right-sm">{{item.projectName}}</view>
					</template>
				</view> -->
				
			</view>
		</view>
		<view class="padding-top-xs flex justify-between">
			<view v-if="detail.checkOutTime && detail.checkInTime" style="width: 140rpx;">
				<u-button v-if="!detail.uploadData" type="primary"  :color="colors" text="护理资料上传" :plain="false" size="mini" @click="dataUpload"/>
				<u-button v-else-if="!detail.workIsAudit" type="primary"  :color="colors" text="护理资料修改" :plain="true" size="mini" @click="dataUpload"/>
				<u-button v-else type="primary"  :color="colors" text="护理资料查看" :plain="true" size="mini" @click="dataUpload"/>
			</view>
			<view v-else-if="detail.checkInTime" style="width: 140rpx;">
				<u-button v-if="!detail.uploadData" type="primary"  :color="colors" text="护理资料上传" :plain="false" size="mini" @click="dataUploadAlert"/>
			</view>
			<view>
				<u-tag v-if="detail.workIsAudit" text="护理已审核"  size="mini"/>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {
		},
		props:{
			colors:{
				type:String,
				default:'',
			},
			detail:{
				type:Object,
				default:()=>{},
			},
			tip:{
				type:String,
				default:'',
			}
		},
		data() {
			return {
			}
		},
		methods:{
			dataUpload(){
				this.$emit('dataUpload',this.detail)
			},
			itemClick(){
				this.$emit('itemClick',this.detail)
			},
			dataUploadAlert(){
				uni.showToast({
					title:'签退以后才能上传护理资料',
					icon:'none',
					duration:3000,
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.item-box{
		background-color: white; 
		display: flex;
		position: relative;
		overflow: hidden;
		.content{
			flex: 1;
			padding: 0 20rpx;
		}
		
	}
	
	.block-btn {
		width: 190rpx;
		height: 60rpx;
		color:#fff;
		background: linear-gradient(90deg, var(--colors), var(--colors));
		border: 2rpx solid rgba(230, 184, 115, 0.3);
		border-radius: 80rpx;
		padding: 8rpx 20rpx;
		font-size: 28rpx;
		font-weight: 700;
	}
	
	.cuIcon-people{
		font-size: 116rpx;
		color: gray;
		border: 1rpx solid #ccc;
		width: 116rpx;
		height: 156rpx;
		line-height: 156rpx;
		border-radius: 6rpx;
		display: block;
	}
	
	.address{
		height: 36rpx;
		overflow: hidden; /* 隐藏溢出的内容 */
		text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
		-o-text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
	
	.select-btn{
		:deep(.u-button--small) {
			height: 48rpx !important;
			letter-spacing: 2px;
		}
	}
	
	.tip{
		position: absolute;
		background-color: var(--colors);
		overflow: hidden;
		white-space:nowrap;
		color: #fff;
		transform: rotate(-45deg);
		box-shadow: 0 0 20rpx #888;
		min-width: 150rpx;
		text-align: center;
		left:-40rpx;
		top:20rpx;
		font-size: 22rpx;
		line-height: 1.6;
	}
	.distance{
		position: absolute;
		right: 0;
		top:0;
		font-size: 20rpx;
		color: #a0a0a0;
	}
</style>

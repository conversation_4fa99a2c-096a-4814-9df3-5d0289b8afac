<template>
  <view>
    <view v-if="step == 0 && customer.longitude && customer.latitude" class="padding-lr-sm">
      <map
        style="width: 100%; height: 450rpx"
        id="myMap"
        :scale="16"
        :latitude="customer.latitude"
        :longitude="customer.longitude"
        :markers="markers"
        :circles="circles"
        show-location></map>
    </view>
    <view v-if="step == 0" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
      <view class="text-content text-sm">
        <view class="basis-df bg-gray padding-lr padding-tb-sm margin-bottom">
          <view v-if="rangeInfo.inRange">
            <view v-if="rangeInfo.requireLocationAddress">规定位置：{{ rangeInfo.requireLocationAddress }}</view>
            <view v-if="rangeInfo.requireLocationAddress">
              <text>距离规定位置</text>
              <text class="padding-lr-sm" :style="{ color: colors }">{{ rangeInfo.distance }}</text>
              <text>km</text>
            </view>
          </view>
          <view v-else class="text-center"
            >请您在红圈内，并点击下方签到
            <text v-if="customer.longitude && customer.latitude">(地图可缩放)</text>
          </view>
        </view>
      </view>
      <view class="flex flex-direction justify-center align-center padding-tb-sm">
        <view
          v-if="rangeInfo.inRange"
          class="btn-round margin-bottom-xl"
          :class="rangeInfo.isCustomerLocate ? 'bg-green' : 'bg-orange'"
          @click="sign_step0">
          <view>立即签到</view>
        </view>
        <view v-else class="btn-round bg-gray">
          <view class="text-green">无法签到</view>
        </view>
      </view>
      <view style="height: 70rpx">
        <view class="text-content text-center">
          <view v-if="!rangeInfo.inRange">
            <view v-if="!rangeInfo.inRange" class="text-red">当前位置不在签到范围内</view>
            <!-- <view @tap="showMap" class="text-gray">点击查看打卡位置</view> -->
          </view>
          <view v-else class="text-green">位置在规定范围内，可以打卡！</view>

          <!-- <view  class="text-center text-content text-xs">当前位置：{{rangeInfo.address}}</view> -->
        </view>
      </view>
    </view>
    <!-- 		<view v-if="step==0 && !rangeInfo.inRange" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<view class="text-sm text-gray">提示：</view>
			<view class="text-sm text-gray" @tap="showMap">要求在规定范围
				<text v-if="radius" :style="{color:colors}" class="text-bold">{{radius}}米</text>
				<text v-else :style="{color:colors}" class="text-bold">(距离无要求)</text>
				内,点击<text :style="{color:colors}">此处</text>查看打卡位置
			</view>
		</view> -->

    <view v-if="step == 1">
      <view class="bg-white padding-tb-sm padding-lr-sm" style="min-height: 50vh">
        <view v-if="rangeInfo.imgMinCount" class="margin-tb padding-lr-xs" :style="{ color: colors }"
          >至少需要 {{ rangeInfo.imgMinCount }}张现场照片</view
        >
        <view class="image-box">
          <ut-image-upload
            ref="upload"
            name="file"
            v-model="myPhotos"
            mediaType="image"
            :sourceType="['camera']"
            :colors="colors"
            :max="rangeInfo.imgMaxCount ? rangeInfo.imgMaxCount : 20"
            :headers="headers"
            :action="uploadInfo.server + uploadInfo.single || ''"
            :preview-image-width="1200"
            :width="180"
            :height="180"
            :border-radius="8"
            @uploadSuccess="uploadFaceSuccess"
            @imgDelete="imgDelete">
          </ut-image-upload>
        </view>
        <view class="margin-top-lg padding-lr-sm text-content">
          <view>描述：</view>
          <u--textarea
            v-model="remark"
            placeholder="请输入内容"
            :maxlength="500"
            count
            confirmType="done"></u--textarea>
        </view>
      </view>

      <ut-fixed safe-area-inset position="bottom" background="#fff">
        <view class="padding-tb-sm padding-lr-lg">
          <u-button
            type="primary"
            shape="circle"
            :color="colors"
            :disabled="!myPhotos.length || (myPhotos.length < rangeInfo.imgMinCount && rangeInfo.imgMinCount > 0)"
            text="保存签到"
            size="normal"
            @click="work"></u-button>
        </view>
      </ut-fixed>
    </view>

    <view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
  </view>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
export default {
  components: {},
  options: {
    styleIsolation: 'shared',
  },
  props: {
    colors: {
      type: String,
    },
    date: {
      type: String,
    },
    workKey: {
      type: Object,
      default: () => {},
    },
    customer: {
      type: Object,
      default: () => {},
    },
    markers: {
      type: Array,
      default: () => [],
    },
    radius: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      noClick: true,
      step: 0,
      myPhotos: [],
      remark: '',
      rangeInfo: {},
      intervalTime: {},
      location: {},
    }
  },
  computed: {
    ...mapState({
      commKey: (state) => state.init.template.commKey,
      token: (state) => state.user.token,
      userInfo: (state) => state.user.info,
      community: (state) => state.init.community,
      uploadInfo: (state) => state.init.oss,
    }),
    headers() {
      return {
        Token: this.uploadInfo.token,
      }
    },
    circles() {
      if (!this.customer.longitude || !this.customer.latitude) return []
      return [
        {
          longitude: this.customer.longitude,
          latitude: this.customer.latitude,
          fillColor: '#FF2B431A',
          color: '#FF0000',
          radius: this.radius,
          strokeWidth: 1,
        },
      ]
    },
  },
  onShow() {
    this.setData({ colors: app.globalData.newColor })
  },
  onLoad: async function (options) {
    await this.$onLaunched
  },
  mounted() {
    this.step = 0
    this.loadLocation()
  },
  destroyed() {
    clearInterval(this.intervalTime)
  },
  watch: {
    step: {
      handler(v) {
        if (v == 0) {
          this.loadLocation()
        } else {
          clearInterval(this.intervalTime)
        }
      },
    },
  },
  methods: {
    openMap() {
      this.$wxsdk.openLocation({
        latitude: this.rangeInfo.requireLatitude,
        longitude: this.rangeInfo.requireLongitude,
        name: this.signInfo.requireName,
        address: this.signInfo.requireAddress,
      })
    },
    async getLocation() {
      this.$wxsdk
        .getLocationToAddress()
        .then(async (location) => {
          this.location = location
          const { data } = await this.$ut.api('mang/care/work/checkin/info', {
            communityId: this.community.id,
            planId: this.workKey.planId,
            customerId: this.customer.id,
            latitude: location.latitude,
            longitude: location.longitude,
          })
          this.rangeInfo = data

          //await this.getLocationInfo(location)
        })
        .catch((err) => {
          uni.showToast({
            title: '请开启定位功能',
            icon: 'nono',
            duration: 3000,
          })
          console.log(err)
        })
    },
    async getLocationInfo(location) {
      const { data } = await this.$ut.api('comm/locationInfo', {
        commKey: this.commKey,
        longitude: location.longitude,
        latitude: location.latitude,
      })
      this.$set(this.rangeInfo, 'address', data.address)
    },
    loadLocation() {
      this.getLocation()
      clearInterval(this.intervalTime)
      this.intervalTime = setInterval(() => {
        this.getLocation() // 接口方法
      }, 5000)
    },
    sign_step0() {
      this.$emit('stopTime')
      if (this.rangeInfo.needImg) {
        setTimeout(() => {
          this.step = 1
        }, 200)
      } else {
        this.work()
      }
    },
    work() {
      this.$ut
        .api('mang/care/work/checkin/save', {
          communityId: this.community.id,
          ...this.workKey,
          latitude: this.location.latitude,
          longitude: this.location.longitude,
          address: this.rangeInfo.address,
          workDate: this.date,
          photoRemark: this.remark,
          imgs: this.myPhotos,
        })
        .then((res) => {
          this.$emit('close', res.data)
        })
    },
    showMap() {
      this.$wxsdk.openLocation({
        latitude: this.rangeInfo.requireLatitude,
        longitude: this.rangeInfo.requireLongitude,
        name: '我',
        address: '规定的打卡位置',
      })
    },
    uploadFaceSuccess(res) {
      res.forEach((img) => {
        const item = img.data
        this.myPhotos.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
      })
    },
    imgDelete(e) {
      console.log(e)
      //this.form.imgHead = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.img-head image {
  flex-shrink: 0;
  width: 130upx;
  height: 130upx;
  border: 5upx solid #fff;
  border-radius: 50%;
}

.btn-round {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  color: #fff;
  font-size: 28rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  align-content: center;
  justify-content: center;
  letter-spacing: 4rpx;
}
</style>

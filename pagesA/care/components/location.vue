<template>
	<view  style="max-height: 80vh;overflow-y: scroll;">
		<view class="padding-lr-sm">
			<map style="width:100%;height:600rpx" id="myMap" :scale="18" :latitude="location.latitude" :longitude="location.longitude" :markers="markers" :circles="circles" show-location></map>
		</view>
<!-- 		<view class="bg-white padding-tb-sm padding-lr-sm">
			<view class="margin-tb-lg  padding-lr-sm text-left">请填写变更原因并提交后台申请，新的位置才能生效。</view>
			<view v-if="isGPS" class="margin-top-lg padding-lr-sm text-content">
				<view v-if="addressInfo && addressInfo.address" class="text-content">变更为：{{addressInfo.address}}</view>
				<view v-if="addressInfo.location.longitude">
					坐标：{{addressInfo.location.longitude}},{{addressInfo.location.latitude}}
				</view>
				<view v-if="addressInfo.location.longitude" class="text-gray margin-top-lg" @tap="openMap">地图上显示</view>
			</view>
			<view v-else>未获取当前信息,请稍等。如果长时间未获得信息，请检查定位权限是否开启。</view>
		</view> -->
		<view v-if="isGPS" class="margin-top-sm padding-lr-sm text-content">
			<view v-if="addressInfo && addressInfo.address" class="text-content">当前住置：{{addressInfo.address}}</view>
			<view v-if="addressInfo.location.longitude">
				坐标：{{addressInfo.location.longitude}},{{addressInfo.location.latitude}}
			</view>
			<view v-if="addressInfo.location.longitude" class="text-gray" @tap="openMap">地图上显示</view>
		</view>
		<view v-else>未获取当前信息,请稍等。如果长时间未获得信息，请检查定位权限是否开启。</view>
		<view class="text-center">
			<view v-if="loadingLocation==1">正在获取定位(红点为定位点，圆圈为签到范围)</view>
			<view v-else-if="loadingLocation==2">获取定位完成(红点为定位点，圆圈为签到范围)</view>
			<view v-if="loadingLocation==2" @click="loadGPS">重新获取</view>
		</view>
		<view class="padding-sm margin-bottom-lg">
			<u--textarea v-model="remark" placeholder="请输入位置变更原因" :maxlength="200" count confirmType="done"></u--textarea>
		</view>
		<view class="padding-lg"></view>
		
		
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg" style="width: 100vw;">
				<u-button type="primary" shape="circle" :disabled="!isGPS || !remark" :color="colors" text="保存客户位置" size="normal"  @click="$shaken(saveLocation)"></u-button>
			</view>
		</ut-fixed>
	</view>
</template>

<script>

var app = getApp()
import  {mapMutations, mapActions, mapState}  from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	props:{
		customerId:{
			type:String,
			default:'',
		},
		radius:{
			type:Number,
			default:100,
		}
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight:0,
			addressInfo:{},
			location:{},
			myLocation:{},
			remark:'',
			markers:[],
			circles:[],
			gpsCount:0,
			intervalTime:{},
			loadingLocation:0,
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,			
			locationAddress: state => state.init.locationAddress,
		}),
		// position() {
		// 	return {
		// 		latitude: this.locationAddress.location.latitude,
		// 		longitude: this.locationAddress.location.longitude,
		// 		address: this.locationAddress.info.address,
		// 	}
		// },
		isGPS(){
			if(this.location && this.location.longitude && this.location.latitude) return true
			return false
		},
	},
	onShow() {
		
	},
	onLoad: async function (options) {
		await this.$onLaunched
	},
	mounted() {
		
		this.loadGPS()
		//this.getLocation()
	},
	destroyed() {
		clearInterval(this.intervalTime)
	},
	methods: {
		...mapActions(['setLocationAddress', 'setCity']),
		getHeight(h) {
			this.topWrapHeight = h
		},
		loadGPS(){
			this.gpsCount=0;
			this.getLocation()
			this.loadingLocation=1
			clearInterval(this.intervalTime)
			this.intervalTime = setInterval(() => {
			  this.getLocation();  // 接口方法
			  this.gpsCount++
			  if(this.gpsCount>5){
				  clearInterval(this.intervalTime)
				  this.loadingLocation=2
			  }
			}, 3000);
		},
		// async getInfo(){
		// 	const {data} = await this.$ut.api('mang/care/myLocation/info', {
		// 		communityId: this.community.id,
		// 		module: 'long',
		// 	})
		// 	this.myLocation=data
		// },
		getLocation() {
			// uni.showLoading({
			// 	title:'请稍等...'
			// })
			this.$wxsdk.getLocationToAddress().then(location => {
				this.location=location
				this.circles=[{
						longitude: location.longitude,
						latitude: location.latitude,
						fillColor: "#FF2B431A",
						color: "#FF0000",
						radius: 1,
						strokeWidth: 2
					},
					{
							longitude: location.longitude,
							latitude: location.latitude,
							fillColor: "#FF2B4330",
							color: "#aa3690",
							radius: this.radius?this.radius:100,
							strokeWidth: 2
						}]
				// this.$ut.api('comm/locationInfo',{
				// 	commKey:this.commKey,
				// 	longitude:location.longitude,
				// 	latitude:location.latitude
				// }).then((res)=>{
				// 	this.addressInfo=res.data
				// 	if (res.data && res.data.city) {
				// 		this.setCity(res.data.city)
				// 	}
				// 	uni.hideLoading()
				// })
				
			}).catch(err => {
					uni.hideLoading()
			})
		},
		openMap(){
			this.$wxsdk.openLocation({
				latitude:this.location.latitude,
				longitude:this.location.longitude,
				address:'我的当前位置'
			})
		},

		saveLocation(){
			if (!this.location || !this.location.longitude || !this.location.latitude){
				uni.showToast({
					title:'请先打开定位功能'
				})
				return
			}
			uni.showLoading({
				title:'请稍等'
			})
			this.$ut.api('mang/care/customer/location/apply', {
				communityId: this.community.id,
				customerId:this.customerId,
				module: 'long',
				longitude:this.location.longitude,
				latitude:this.location.latitude,
				locationAddress: this.addressInfo.address,
				remark:this.remark,
			}).finally(()=>{uni.hideLoading()})
			.then(()=>{
				uni.showToast({
					title:'修改成功！管理员审核后生效。',
					icon:'none'
				})
				setTimeout(()=>{					
					this.$emit('fetchData')
					this.$emit('close')
				},200)
			})
		}
	},
}
</script>

<style>
</style>
<template>
	<view class="padding-sm text-content">
		<view class="flex">
			<text>顾客：</text>
			<view class="flex flex-sub justify-between">
				<view>
					<text class="text-bold text-df margin-right">{{detail.name}}</text>
					<text v-if="detail.sex==1" class="text-sm text-gray">男</text>
					<text v-else-if="detail.sex==2" class="text-sm text-gray">女</text>
				</view>
				<view>
					<text class="text-sm text-gray">{{detail.phone}}</text>
				</view>
			</view>
		</view>
		<view>
			<text>护理日期：</text>
			<text class="text-bold">{{detail.workDate}}</text>
		</view>
		<view class="sub-box"></view>
		<view >
			<scroll-view  scroll-y="true" class="scroll-box" >
			<template v-for="(item,index) in fileData">
			<view :key="index" class="bg-white text-content item-box">
				<view class="text-bold text-lg">{{item.title}}</view>
				<template v-for="(detail,dIndex) in item.details">
				<view :key="dIndex" class="padding-left-lg">
					<view class="margin-left-xs">
						<text>{{detail.title}}</text>
						<text v-if="detail.require" class="text-xs margin-left-xs" :style="{color:colors}">(必填)</text>
						<text v-else class="text-xs margin-left-xs" >(选填)</text>
					</view>
					<view class="image-box">
						<ut-image-upload
							ref="upload"
							name="file"
							v-model="detail.files"
							mediaType="image"
							:colors="colors"
							:max="20"
							:headers="headers"
							:action="uploadInfo.server+uploadInfo.single||''"
							:preview-image-width="1200"
							:width="200"
							:height="160"
							:border-radius="8"
							:disabled="false"							
							:add="isEdit==true"
							:remove="isEdit==true"
							@uploadSuccess="uploadFaceSuccess($event,detail)"
							>
						</ut-image-upload>
					</view>
				</view>
				</template>
			</view>
			</template>
			</scroll-view>
		</view>
		<view v-if="isEdit">
			<u-button type="primary" shape="circle" :color="colors" text="保存资料" size="normal" @click="$emit('save',fileData)"></u-button>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default {
		components: {
		},
		props:{
			colors:{
				type:String,
				default:'',
			},
			detail:{
				type:Object,
				default:()=>{},
			},
			fileData:{
				type:Array,
				default:()=>[],
			},
			isEdit:{
				type:Boolean,
				default:true,
			}
		},
		data() {
			return {
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				token: state => state.user.token,
				userInfo: state => state.user.info,
				community: state => state.init.community,
				uploadInfo: state => state.init.oss,
			}),
			headers(){
				return {
					Token:this.uploadInfo.token
				}
			},
		},
		methods:{
			uploadFaceSuccess(files,item){
				files.forEach(file=>{
					if(!item.files) item.files=[]
					item.files.push(this.uploadInfo.preview + '?file=' + file.data.name + file.data.ext)
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.sub-box{
	padding-top: 10rpx;
	border-bottom: 2rpx solid #a0a0a0;
	margin-bottom: 10rpx;
}

.item-box{
	border-bottom: 1rpx dashed #a0a0a0;
	padding-top: 10rpx;
	padding-bottom: 10rpx;
	
	&:last-child{
		border-bottom: none;
	}
}

.scroll-box{
	max-height: 800rpx;
}


</style>
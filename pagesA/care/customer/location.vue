<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="客户位置定位" navbarType="1" />
		</ut-top>
		
		<view class="bg-white padding-tb-sm padding-lr-sm" :style="{minHeight:'calc(100vh - '+topWrapHeight+'px)'}">
			<view class="padding-lr-sm">
				<map style="width:100%;height:600rpx" id="myMap" :scale="18" :latitude="location.latitude" :longitude="location.longitude" :markers="markers" :circles="circles" show-location></map>
			</view>
			<view class="margin-tb-lg text-center text-bold">该客户没有定位信息</view>
			<view class="margin-tb-lg text-center">需要您为该客户进行准确定位：</view>
			<view v-if="isGPS" class="margin-top-lg padding-lr-sm text-content">
				<view v-if="addressInfo && addressInfo.address" class="text-content">当前住置：{{addressInfo.address}}</view>
				<view v-if="addressInfo.location.longitude">
					坐标：{{addressInfo.location.longitude}},{{addressInfo.location.latitude}}
				</view>
				<view v-if="addressInfo.location.longitude" class="text-gray" @tap="openMap">地图上显示</view>
			</view>
			<view v-else>未获取当前信息,请稍等。如果长时间未获得信息，请检查定位权限是否开启。</view>
		</view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :disabled="!isGPS" :color="colors" text="保存客户位置" size="normal"  @click="saveLocation"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>

var app = getApp()
import  {mapMutations, mapActions, mapState}  from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight:0,
			addressInfo:{},
			location:{},
			myLocation:{},
			customerId:'',
			markers:[],
			circles:[],
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,			
			locationAddress: state => state.init.locationAddress,
		}),
		// position() {
		// 	return {
		// 		latitude: this.locationAddress.location.latitude,
		// 		longitude: this.locationAddress.location.longitude,
		// 		address: this.locationAddress.info.address,
		// 	}
		// },
		isGPS(){
			if(this.addressInfo && this.addressInfo.location && this.addressInfo.location.longitude) return true
			return false
		},
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
		
		// this.getInfo()
		this.getLocation()
	},
	onLoad: async function (options) {
		await this.$onLaunched
		if (options.customerId)this.customerId = options.customerId
	},
	methods: {
		...mapActions(['setLocationAddress', 'setCity']),
		getHeight(h) {
			this.topWrapHeight = h
		},
		// async getInfo(){
		// 	const {data} = await this.$ut.api('mang/care/myLocation/info', {
		// 		communityId: this.community.id,
		// 		module: 'long',
		// 	})
		// 	this.myLocation=data
		// },
		getLocation() {
			this.$wxsdk.getLocationToAddress().then(location => {
				this.location=location
				this.circles=[{
						longitude: location.longitude,
						latitude: location.latitude,
						fillColor: "#FF2B431A",
						color: "#FF0000",
						radius: 1,
						strokeWidth: 2
					}]
				// this.$ut.api('comm/locationInfo',{
				// 	commKey:this.commKey,
				// 	longitude:location.longitude,
				// 	latitude:location.latitude
				// }).then((res)=>{
				// 	this.addressInfo=res.data
				// 	if (res.data && res.data.city) {
				// 		this.setCity(res.data.city)
				// 	}
				// })
				
			}).catch(err => {
			})
		},
		openMap(){
			this.$wxsdk.openLocation({
				latitude:this.location.latitude,
				longitude:this.location.longitude,
				address:'我的当前位置'
			})
		},
		openMapSource(){
			this.$wxsdk.openLocation({
				latitude: this.myLocation.latitude,
				longitude: this.myLocation.longitude,
				address: this.myLocation.address,
			})
		},
		saveLocation(){
			if (!this.location || !this.location || !this.location.longitude){
				uni.showToast({
					title:'请先打开定位功能'
				})
				return
			}
			this.$ut.api('mang/care/customer/locationSave', {
				communityId: this.community.id,
				customerId:this.customerId,
				module: 'long',
				longitude:this.location.longitude,
				latitude:this.location.latitude,
				address: this.addressInfo.address,
			}).then(()=>{
				this.$tools.back('init')
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
			})
		}
	},
}
</script>

<style>
</style>
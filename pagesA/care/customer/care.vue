<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="护理客户" navbarType="2"></f-navbar>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :show-all="true" :colors="colors" :file-data="fileData" />
			<view class="flex align-center justify-between margin-top-xs">
				<view>
					<text class="text-sm">护理员：</text>
					<text class="text-sm">{{customer.attendantName}}</text>
				</view>
<!-- 				<view>
					<text class="text-df" @click="showLocation=true">重新定位地址</text>
				</view> -->
			</view>
		</view>
		<view v-if="workState && workState.begin" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<view class="content text-content margin-top-xs">
				<view>
					<text class="text-sm text-title" style="line-height: 2;">服务时间：</text>
					<text class="text-bold text-df">{{workState.workDate}}</text>
					<text class="text-bold text-df margin-left-lg">{{workState.begin}}</text>
					<text class="text-bold text-df">-</text>
					<text class="text-bold text-df">{{workState.end}}</text>
				</view>
				<view class="flex">
					<text class="text-sm text-title" style="line-height: 2;">服务项目：</text>
					<view class="flex-sub">
						<template v-for="(item,index) in  workState.projects">
							<view :key="index">
								<text>{{item.govCode}}. </text>
								<text>{{item.name}}</text>
								<text>(</text>
								<text v-if="item.minDuration">{{item.minDuration}}--</text>
								<text v-if="item.maxDuration">{{item.maxDuration }}</text>
								<text v-if="item.minDuration || item.maxDuration">分钟</text>
								<text>)</text>
							</view>
						</template>

					</view>
				</view>
			</view>
		</view>
		<view v-if="workState  && loadWork && !workState.isSubmit"
			class="cu-card margin-sm radius shadow s-gray bg-white text-center padding-lr-lg padding-top-lg" :class="!workState.id?'padding-bottom-lg':''">
			<!-- <button v-if="!customer.latitude" class="cu-btn normal-btn" @click="goLocation">该客户需要先定位准确位置</button> -->
			<button v-if="!workState.id" class="cu-btn start-btn margin-tb-lg" @tap="handleStart">开始服务</button>
			<button v-else class="cu-btn start-btn" @tap="go(false)">继续</button>

			<view v-if="workState.id" class="margin-tb-lg padding-top-lg" @tap="cancel">取消服务</view>
		</view>
		<view v-else-if="workState.isSubmit" class="cu-card margin-sm radius shadow s-gray bg-white text-center padding-lg">
			<view class="text-df text-bold">已服务完成</view>
			<view class="text-sm">签到时间：{{workState.checkInTime}}</view>
			<view class="text-sm">签退时间：{{workState.checkOutTime}}</view>
			
			<view v-if="!workState.uploadData" class="padding-top">
				<u-button type="primary" shape="circle" :color="colors" text="护理资料上传" size="normal" @click="openFileData"></u-button>
			</view>
		</view>
		
		<view v-if="workState.isSubmit" class="cu-card margin-sm radius shadow s-gray bg-white padding-lg">
			<view class="flex">
				<text class="text-sm text-title" style="line-height: 2;">签到说明：</text>
				<text class="text-bold text-df">{{workState.checkInRemark}}</text>
			</view>
			<view class="flex">
				<text class="text-sm text-title" style="line-height: 2;">现场照片：</text>
				<view>
					<template v-for="(item,index) in workState.checkInPhotos">
						<view>
							<u-lazy-load :image="$tools.showImg(item.url)" width="120" height="120" border-radius="4"
							 @click="$tools.previewImage(getItemImages(workState.checkInPhotos),index,800)"/>
				
						</view>
					</template>
				</view>
			</view>			
		</view>
		
		<view v-if="workState.isSubmit" class="cu-card margin-sm radius shadow s-gray bg-white padding-lg">
			<view class="flex">
				<text class="text-sm text-title" style="line-height: 2;">签退说明：</text>
				<text class="text-bold text-df">{{workState.checkOutRemark}}</text>
			</view>
			<view class="flex">
				<text class="text-sm text-title" style="line-height: 2;">现场照片：</text>
				<view>
					<template v-for="(item,index) in workState.checkOutPhotos">
						<view>
							<u-lazy-load :image="$tools.showImg(item.url)" width="120" height="120" border-radius="4"
							 @click="$tools.previewImage(getItemImages(workState.checkOutPhotos),index,800)"/>
				
						</view>
					</template>
				</view>
			</view>			
		</view>
		
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		<view class="padding-bottom-sm"></view>

		<!-- 		<view v-if="workState && workState.workId && loadWork" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-center padding-lg">
			<button class="cu-btn start-btn" @tap="go(false)">继续</button>
			
			<view class="margin-tb-lg" @tap="cancel">取消服务</view>
			
		</view> -->

		<view class="cu-modal" v-if="showStart" cathctouchmove @tap.stop="showStart=false">
			<view class="cu-dialog" @tap.stop style="background: none;overflow: visible;">
				<view class="modal-box">
					<image class="head-bg" src="https://oss.afjy.net/api/file/preview?file=eWtn09p.png&width=750"
						mode="">
					</image>
					<view class="detail">
						<view class="date margin-tb-xl text-bold text-xl">
							<view v-if="!date" @tap="$refs.datePicker.show()">选择时间</view>
							<view v-else @tap="$refs.datePicker.show()">
								服务时间：{{date}}
							</view>

						</view>
						<button class="cu-btn start-btn" @tap="$shaken(work)">立即开始</button>
					</view>

				</view>
			</view>
		</view>
		

		<u-popup v-if="showLocation" :show="showLocation" mode="bottom" round="10" :closeable="true"
			:safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
			:overlay-style="{zIndex:998}" @close="showLocation=false">
			<view class="pop-title">重新申请客户位置</view>
			
			<location :customer-id="customerId" @fetchData="getCustomerInfo" @close="showLocation=false" />
		</u-popup>

		<u-popup v-if="showPosition" :show="showPosition" mode="bottom" round="10" :closeable="true"
			:safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
			:overlay-style="{zIndex:998}" @close="showPosition=false">
			<view class="pop-title">签到打卡</view>
			
			<position :work-key="workKey"  :customer="customer" :markers="markers" :radius="workState.requireDistance" :location="location" :colors="colors" 
			@stopTime="handleStopTime"
			@close="handlePositionClose" />
		</u-popup>

		<u-popup v-if="showDataUpload" :show="showDataUpload" mode="bottom" round="10" :closeable="true"
			:safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
			:overlay-style="{zIndex:998}" @close="showDataUpload=false">
			<view class="padding-tb-xs text-center">护理资料上传</view>
			<care-data-upload :colors="colors" :detail="customer" :fileData="careFileData" @save="saveFileData"></care-data-upload>
		</u-popup>

		<pick-date ref="datePicker" :start-year="new Date().getFullYear()" :end-year="new Date().getFullYear()"
			:time-init="0" :time-hide="[true, true, true, false, false, false]"
			:time-label="['年', '月', '日', '时', '分', '秒']" @submit="dateSelect" />
	</ut-page>
</template>

<script>
	var app = getApp()
	import {
		mapState
	} from 'vuex'
	import CustomerInfo from '@/pagesA/components/customer-info.vue'
	import PickDate from '@/pagesA/components/pickDate.vue'
	import Location from '../components/location.vue'
	import Position from '../components/position.vue'	
	import CareDataUpload from '../components/care-data-upload.vue'

	export default {
		mixins: [],
		components: {
			CustomerInfo,
			PickDate,
			Location,
			Position,
			CareDataUpload,
		},
		options: {
			styleIsolation: 'shared',
		},
		data() {
			return {
				colors: '',
				noClick: true,
				planId: '',
				customerId: '',
				customer: {},
				show: false,
				attendants: [],
				selAttendant: {},
				fileData: [],

				pageReq: {
					pagesize: 20,
					pageindex: 1,
					key: '',
				},
				showStart: false,
				date: '',
				schedulingId: '',
				workState: {},
				loadWork: false,
				showPosition: false,
				showLocation: false,
				location: {},
				isGPS:false,
				
				intervalTime:{},				
				circles: [],	// 中心签到圈
				markers: [],
				
				showDataUpload:false,
				careFileData:[],
			}
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor
			})
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				token: state => state.user.token,
				userInfo: state => state.user.info,
				community: state => state.init.community,
			}),
			workKey() {
				return {
					planId: this.schedulingId,
					customerId: this.customerId,
					date: this.date,

				}
			}
		},
		onLoad: async function(options) {
			await this.$onLaunched

			if (options.customerId) this.customerId = options.customerId
			if (options.planId) this.planId = options.planId


			this.date = this.today()
			this.getCustomerInfo()
			this.getCustomerFileData()

			await this.getWorkState()
			//this.getLocation()
		},
		onUnload() {
			clearInterval(this.intervalTime)
		},
		watch:{
			// showPosition:{
			// 	handler(v){
			// 		if(v==false){
			// 			clearInterval(this.intervalTime)
			// 		}
			// 	}
			// }
		},
		methods: {
			init() {
				this.getCustomerInfo()
				this.getCustomerFileData()

				this.getWorkState()
				
			},
			async getCustomerInfo() {
				if (!this.customerId) return
				const {
					data
				} = await this.$ut.api('mang/care/customer/info', {
					communityId: this.community.id,
					module: 'long',
					id: this.customerId,
				})
				this.customer = data
				
			},
			async getCustomerFileData() {
				if (!this.customerId) return
				const {
					data
				} = await this.$ut.api('mang/care/customer/fileData/allList', {
					communityId: this.community.id,
					customerId: this.customerId,
				})
				this.fileData = data
			},
			dateSelect(e) {
				this.date = `${e.year}-${e.month}-${e.day}`
			},
			today() {
				const now = new Date();
				const year = now.getFullYear();
				const month = (now.getMonth() + 1).toString().padStart(2, '0');
				const day = now.getDate().toString().padStart(2, '0');

				this.$refs.datePicker.value = [0, month - 1, day - 1]
				return `${year}-${month}-${day}`;
			},
			async getWorkState() {
				const {
					data
				} = await this.$ut.api('mang/care/customer/workState', {
					communityId: this.community.id,
					planId: this.planId,
					customerId: this.customerId,
				})
				this.workState = data
				this.loadWork = true
				if (data && data.schedulingId) this.schedulingId = data.schedulingId
				
			},
			
			
			handleStart() {
				if (!this.schedulingId) {
					this.showStart = true
				} else {
					this.showPosition = true
				}
			},
			work() {
				this.showStart = false
				this.showPosition = true
				
			},
			async handlePositionClose(workId){
				this.showPosition=false
				await this.getWorkState()
				this.$tools.routerTo('/pagesA/care/work/start', {
					workId: workId,
					customerId: this.customerId
				})
			},
			handleStopTime(){
				clearInterval(this.intervalTime)
			},
			go() {
				this.$tools.routerTo('/pagesA/care/work/start', {
					workId: this.workState.id,
					customerId: this.customerId
				})

			},
			handleCancel() {
				uni.showLoading({
					title: '请稍等...'
				})
				this.$ut.api('mang/care/work/cancel', {
					communityId: this.community.id,
					workId: this.workState.id,
				}).finally(() => {
					uni.hideLoading()
				}).then(async (res) => {
					this.getWorkState()
					setTimeout(() => {
						uni.showToast({
							title: '取消成功'
						})
					}, 100)
					// this.$tools.back('downCallback()')
				})
			},
			cancel() {
				uni.showModal({
					title: '询问',
					content: `确定取消当前服务吗？`,
					confirmText: '确定',
					success: res => {
						if(!res.cancel){							
							this.handleCancel()
						}
					},
					fail: err => {
						console.log(`%cuni.showModal失败：`, 'color:green;background:yellow');
					}
				})
			},
			goLocation() {
				this.$tools.routerTo('/pagesA/care/customer/location', {
					customerId: this.customerId
				})
			},
			getItemImages(items){
				return items.map(u=>(u.url));
			},
			async openFileData(){
				uni.showLoading({
					title:'请稍等...'
				})
				const {data} = await this.$ut.api('mang/care/work/dataFile/allList',{
					communityId:this.community.id,
					workId:this.customer.id,
				}).finally(()=>{uni.hideLoading();})
				this.careFileData=data
				
				this.showDataUpload=true
			},
			saveFileData(newFileData){
				let datas=[]
				newFileData.forEach(item=>{
					item.details.forEach(detail=>{
						let di={dataId:detail.id}
						di.urls=[]
						detail.files.forEach(file=>{
							di.urls.push(file)
						})
						datas.push(di)
					})
					
				})
				uni.showLoading({
					title:'请稍等...'
				})
				this.$ut.api('mang/care/work/dataFile/save',{
					communityId:this.community.id,
					workId:this.workState.id,
					datas:datas,
				}).finally(()=>{uni.hideLoading()})
				.then(res=>{
					this.getWorkState()
					this.showDataUpload=false
				})
				
			}
		},
	}
</script>

<style>
	.cu-modal {
		opacity: 1;
		pointer-events: all;
		z-index: 999;
	}
</style>
<style lang="scss" scoped>
	.card-box {
		margin: 30rpx 20rpx;
		padding: 40rpx 30rpx 20rpx 30rpx;
		background-size: 100% 100%;
		border-radius: 10rpx;
		background-color: #fff;
		overflow: hidden;
		bottom: 15rpx;
	}

	.scroll-box {
		padding-top: 60rpx;
		padding-bottom: 10rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		min-height: 30%;
		max-height: 80%;
	}

	.pop-title {
		padding-top: 20rpx;
		text-align: center;
	}

	// .pop-title {
	// 	position: absolute;
	// 	left: 0;
	// 	right: 0;
	// 	padding: 15rpx;
	// 	margin: auto;
	// 	font-size: 30rpx;
	// 	font-weight: bold;
	// 	text-align: center;
	// }

	.clear {
		position: absolute;
		right: 0;
		padding: 15rpx 30rpx 15rpx 15rpx;
		font-size: 30rpx;
		font-weight: bold;
		text-align: center;
	}


	.start-btn {
		width: 492rpx;
		height: 70rpx;
		background: linear-gradient(90deg, var(--colors), var(--colors2));
		box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
		border-radius: 35rpx;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
	}

	.normal-btn {
		width: 492rpx;
		height: 70rpx;
		background: linear-gradient(90deg, var(--colors), var(--colors));
		box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
		border-radius: 35rpx;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
	}



	.modal-box {
		width: 610rpx;
		border-radius: 20rpx;
		background: #fff;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 30rpx;

		.head-bg {
			width: 100%;
			height: 210rpx;
		}


		.btn-box {
			margin-top: 80rpx;

		}
	}

	.text-title {
		width: 120rpx;
	}
</style>
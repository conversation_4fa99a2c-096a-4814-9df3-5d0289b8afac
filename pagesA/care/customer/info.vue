<template>
  <ut-page>
    <f-navbar fontColor="#fff" :bgColor="colors" title="客户信息" navbarType="2"></f-navbar>
    <view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
      <customer-info :detail="customer" :show-all="true" :colors="colors" :file-data="fileData" />
      <view class="flex align-center justify-between margin-top-xs">
        <view>
          <text class="text-sm">护理员：</text>
          <text class="text-sm">{{ customer.attendantName }}</text>
        </view>
        <!-- 				<view>
                  <text class="text-df" @click="showLocation=true">重新定位地址</text>
                </view> -->
      </view>
    </view>
    <view v-if="workState && workState.begin && false"
      class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
      <view class="content text-content margin-top-xs">
        <view>
          <text class="text-sm text-title" style="line-height: 2;">服务时间：</text>
          <text class="text-bold text-df">{{ workState.workDate }}</text>
          <text class="text-bold text-df margin-left-lg">{{ workState.begin }}</text>
          <text class="text-bold text-df">-</text>
          <text class="text-bold text-df">{{ workState.end }}</text>
        </view>
        <view class="flex">
          <text class="text-sm text-title" style="line-height: 2;">服务项目：</text>
          <view class="flex-sub">
            <view v-for="(item,index) in  workState.projects" :key="index">
              <text>{{ item.govCode }}.</text>
              <text>{{ item.name }}</text>
              <text>({{ item.duration }}分钟)</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="!customer.latitude" class="cu-card margin-sm radius shadow s-gray bg-white text-center padding-lg">
      <view v-if="!customer.locationWaitChange">
        <button v-if="!customer.latitude" class="cu-btn normal-btn" @click="showLocation=true">申请定位该客户位置
        </button>
      </view>
      <view v-else class="text-center text-lg text-bold">
        申请的位置正在审核中
      </view>
    </view>
    <!-- 		<view v-if="workState  && loadWork && !workState.isSubmit"
          class="cu-card margin-sm radius shadow s-gray bg-white text-center padding-lr-lg padding-top-lg" :class="!workState.id?'padding-bottom-lg':''">
          <button v-if="!workState.id" class="cu-btn start-btn margin-tb-lg" @tap="handleStart">开始服务</button>
          <button v-else class="cu-btn start-btn" @tap="go(false)">继续</button>

          <view v-if="workState.id" class="margin-tb-lg" @tap="cancel">取消服务</view>
        </view>
        <view v-else-if="workState.isSubmit" class="cu-card margin-sm radius shadow s-gray bg-white text-center padding-lg">
          今日已服务过
        </view> -->

    <view class="cu-card margin-sm radius shadow s-gray bg-white padding-lg">
      <view>
        <u-subsection :list="list"
          :current="detailIndex"
          @change="sectionChange"
          :activeColor="colors"
          inactiveColor="gray"
          fontSize="28rpx"></u-subsection>
        <view v-if="detailIndex==0" class="text-center text-sm padding-top-xs" :style="{color:colors}">
          点击下方排班日期，可以进行调班操作
        </view>
      </view>
      <view class="padding-tb-sm">
        <view>
          <text class="text-sm">计划时长：</text>
          <text class="text-sm">{{ schedulingData.planDration }}</text>
        </view>
        <view>
          <text class="text-sm">工作时长：</text>
          <text class="text-sm">{{ schedulingData.actualDration }}</text>
        </view>
      </view>
      <view>
        <u-cell-group>
          <template v-for="(item,index) in schedulingData.dates">
            <u-cell :icon="getItemIcon(item)">
              <view slot="title" @click="handleChangeScheduling(item)">
                <view class="flex align-center justify-between">
                  <view>
                    <text class="text-bold">{{ item.day }}日</text>
                    <text class="text-sm">{{ item.begin }} -- {{ item.end }}</text>
                    <text class="text-sm text-gray" v-if="item.isWork">(完成)</text>
                    <text class="text-sm text-gray" v-else-if="item.isWait">(待定)</text>
                    <text class="text-sm text-gray" v-else-if="item.isChange">(已调)</text>
                    <text class="text-sm text-gray" v-else-if="item.isCheckIn">(已签到)</text>

                  </view>
                  <view class="flex">
                    <view v-if="item.isWait || item.isChange">
                      <text v-if="!item.isAudit" class="text-xs">未审</text>
                      <text v-else class="text-xs">已审</text>
                    </view>
                    <text v-if="item.isWork" class="text-xs">已工作</text>

                  </view>
                  <u-tag
                    :text="item.duration+'分钟'"
                    plain
                    size="mini"
                    type="warning"
                  >
                  </u-tag>


                </view>
                <view class="text-gray text-sm" v-if="item.projects">
                  <text>服务项目：</text>
                  <template v-for="(projectItem,projectIndex) in item.projects">
                    <view class="flex justify-between">
                      <view>
                        <text>{{ projectItem.projectName }}</text>
                        <text v-if="projectItem.requireMinDuration">{{ projectItem.requireMinDuration }}--</text>
                        <text v-if="projectItem.requireMaxDuration">{{ projectItem.requireMaxDuration }}</text>
                        <text v-if="projectItem.requireMinDuration || projectItem.requireMaxDuration">分钟</text>
                      </view>
                      <u-tag :text="projectItem.govCode" plain size="mini" />
                    </view>
                  </template>

                </view>
              </view>
            </u-cell>
          </template>


        </u-cell-group>
      </view>
    </view>

    <view v-if="detailIndex==0" class="cu-card margin-sm radius shadow s-gray bg-white padding-lg">
      <view>本月剩余未排班时长：{{ schedulingData.surplusDuration || 0 }}
        <text class="margin-left-xs">分钟</text>
      </view>
      <view v-if="schedulingData && schedulingData.surplusDuration" class="padding-lg">
        <button class="cu-btn start-btn" @tap="$shaken(addScheduling)">增加排班</button>
      </view>

    </view>

    <view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
    <view class="padding-bottom-sm"></view>

    <!-- 		<view v-if="workState && workState.workId && loadWork" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-center padding-lg">
      <button class="cu-btn start-btn" @tap="go(false)">继续</button>

      <view class="margin-tb-lg" @tap="cancel">取消服务</view>

    </view> -->

    <view class="cu-modal" v-if="showStart" cathctouchmove @tap.stop="showStart=false">
      <view class="cu-dialog" @tap.stop style="background: none;overflow: visible;">
        <view class="modal-box">
          <image class="head-bg" src="https://oss.afjy.net/api/file/preview?file=eWtn09p.png&width=750">
          </image>
          <view class="detail">
            <view class="date margin-tb-xl text-bold text-xl">
              <view v-if="!date" @tap="$refs.datePicker.show()">选择时间</view>
              <view v-else @tap="$refs.datePicker.show()">
                服务时间：{{ date }}
              </view>

            </view>
            <button class="cu-btn start-btn" @tap="$shaken(work)">立即开始</button>
          </view>

        </view>
      </view>
    </view>


    <u-popup v-if="showLocation" :show="showLocation" mode="bottom" round="10" :closeable="true"
      :safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
      :overlay-style="{zIndex:998}" @close="showLocation=false">
      <view class="pop-title">重新申请客户位置</view>
      <location :customer-id="customerId"
        :radius="workState.requireDistance"
        @fetchData="getCustomerInfo"
        @close="showLocation=false" />
    </u-popup>

    <u-popup v-if="showPosition" :show="showPosition" mode="bottom" round="10" :closeable="true"
      :safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
      :overlay-style="{zIndex:998}" @close="showPosition=false">
      <view class="pop-title">签到打卡</view>
      <position :work-key="workKey"
        :range-info="rangeInfo"
        :date="date"
        :location="location"
        :colors="colors"
        @close="handlePositionClose" />
    </u-popup>

    <u-popup v-if="showChangeScheduling" :show="showChangeScheduling" mode="bottom" round="10" :closeable="true"
      :safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
      :overlay-style="{zIndex:998}" @close="showChangeScheduling=false">
      <view class="pop-title">计划信息</view>
      <view class="padding-sm text-content">
        <view class="text-df" v-if="changeSchedulingItem">
          <text>计划日期：</text>
          <text class="text-lg text-bold">{{ changeSchedulingItem.day }}日</text>
          <text class="margin-left-xs">{{ changeSchedulingItem.begin }} -- {{ changeSchedulingItem.end }}</text>
        </view>
        <view class="text-df" v-if="changeSchedulingItem">
          <text>计划时长：</text>
          <text class="text-bold">{{ changeSchedulingItem.duration }}分钟</text>
        </view>
        <view v-if="changeSchedulingItem && changeSchedulingItem.isWork" class="text-bold text-lg">
          已经完成工作，不允许修改
        </view>
        <!-- <view v-else-if="changeSchedulingItem.isWait" class="text-bold text-lg">已经调整为待定服务</view> -->
        <view v-else-if="changeSchedulingItem && changeSchedulingItem.isWait && !changeSchedulingItem.isAudit"
          class="text-bold text-lg">调整为待定服务
          <text v-if="changeSchedulingItem.isAudit">(已审)</text>
          <text v-else>(待审)</text>
        </view>
        <view v-else-if="changeSchedulingItem && changeSchedulingItem.isChange && !changeSchedulingItem.isAudit"
          class="text-bold text-lg">调整为其它天
          <text v-if="changeSchedulingItem.changeAudit">(已审)</text>
          <text v-else>(待审)</text>
        </view>
        <view v-else-if="changeSchedulingItem && changeSchedulingItem.isCheckIn">
          <view class="margin-tb text-center" style="min-height: 200rpx;">
            <view class="padding-lr">
              <u--form ref="form" labelPostion="left" :model="form" :rules="rulesCheckIn">
                <u-form-item label="时长" prop="editDuration" borderBottom>
                  <view class="full-width flex text-content align-center" style="height: 60rpx;">
                    <u-number-box v-model="form.editDuration" :min="0" :max="300" :step="10"></u-number-box>
                    <text class="margin-left-xs">分钟</text>
                  </view>
                </u-form-item>
                <u-form-item label="事由" prop="reason" required borderBottom>
                  <u--input v-model.trim="form.reason" border="none"
                    placeholder="请输入变更调整的事由" inputAlign='center' />
                </u-form-item>
              </u--form>
            </view>
            <view class="text-sm text-gray margin-top margin-left text-left">
              <text>签到后只能调整时长</text>
            </view>
            <view class="padding-top-lg">
              <button class="cu-btn start-btn" @click="handleScheduling">申请调整</button>
            </view>
          </view>
        </view>
        <view v-else>
          <view v-if="changeSchedulingItem" class="flex margin-tb">
            <text class="margin-right">申请变更为：</text>
            <u-radio-group
              class="flex justify-between padding-lr"
              v-model="changeState"
              size="25"
              labelSize="50"
              placement="row">
              <u-radio v-if="!changeSchedulingItem.isWait && !changeSchedulingItem.isAudit"
                activeColor="red"
                label="取消排班"
                name="wait"></u-radio>
              <u-radio activeColor="blue" label="调到其它天" name="change"></u-radio>
            </u-radio-group>
          </view>
          <view class="margin-tb text-center" style="min-height: 200rpx;">
            <view class="padding-lr">
              <u--form ref="form" labelPostion="left" :model="form" :rules="changeState==='change'?rules:rules0">
                <u-form-item label="日期" prop="changeDate" required borderBottom v-if="changeState==='change'">
                  <view class="full-width" @click="showChangeDate=true">
                    <u--input v-model.trim="form.changeDate" border="none" disabled disabledColor="#ffffff"
                      placeholder="请选择需要工作的日期" inputAlign='center' style="pointer-events:none" />
                  </view>
                  <u-icon slot="right" name="arrow-right" />
                </u-form-item>
                <u-form-item label="时间" prop="beginTime" required borderBottom v-if="changeState==='change'">
                  <view class="full-width" @click="showChangeTime=true">
                    <u--input v-model.trim="form.beginTime" border="none" disabled disabledColor="#ffffff"
                      placeholder="请选择服务开始时间" inputAlign='center' style="pointer-events:none" />
                  </view>
                  <u-icon slot="right" name="arrow-right" />
                </u-form-item>
                <u-form-item label="时长" prop="editDuration" borderBottom v-if="changeState==='change'">
                  <view class="full-width flex text-content align-center" style="height: 60rpx;">
                    调整为
                    <u-switch v-if="changeSchedulingItem" v-model="form.editTime" @change="editTime"></u-switch>
                    <u-number-box v-if="form.editTime"
                      v-model="form.editDuration"
                      :min="0"
                      :max="300"
                      :step="10"
                      class="margin-left-lg"></u-number-box>
                    <text v-if="form.editTime">分钟</text>
                    <text v-else-if="changeSchedulingItem" class="margin-left-lg">
                      {{ changeSchedulingItem.duration }}分钟
                    </text>
                  </view>
                </u-form-item>
                <u-form-item label="事由" prop="reason" required borderBottom v-if="changeState">
                  <u--input v-model.trim="form.reason" border="none"
                    placeholder="请输入变更调整的事由" inputAlign='center' />
                </u-form-item>
              </u--form>
            </view>
            <view class="padding-top-lg">
              <button class="cu-btn start-btn" :disabled="!changeState" @click="handleScheduling">申请调整</button>
              <view class="margin-top-lg text-left text-sm text-gray">
                <text>注意：时间不确定的调整为待定，当确定时需要从待定里操作。</text>
                <text>调整到其它天，只能向当前时间之后操作。</text>
                <text>申请在审核后生效，请注意提交申请。</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <u-calendar ref="calendar" :show="showChangeDate" showLunar :closeOnClickOverlay="true"
      :maxDate="lastDay" @confirm="changeDateSelect"
      @close="showChangeDate=false"></u-calendar>

    <u-datetime-picker :show="showChangeTime" v-model="form.beginTime" mode="time" :closeOnClickOverlay="true"
      @confirm="changeTimeSelect" @close="showChangeTime=false" @cancel="showChangeTime=false"
    ></u-datetime-picker>
    <!-- 		<pick-date ref="changeTimePicker" :start-year="currentYear" :end-year="currentYear" :time-init="0"
          :time-hide="[false, false, false, true, true, false]" :time-label="['年', '月', '日', '时', '分', '秒']"
          @submit="changeTimeSelect" /> -->

    <pick-date ref="datePicker" :start-year="new Date().getFullYear()" :end-year="new Date().getFullYear()"
      :time-init="0" :time-hide="[true, true, true, false, false, false]"
      :time-label="['年', '月', '日', '时', '分', '秒']" @submit="dateSelect" />
  </ut-page>
</template>

<script>
const app = getApp()
import {
  mapState,
} from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
import PickDate from '@/pagesA/components/pickDate.vue'
import Location from '../components/location.vue'
import Position from '../components/position.vue'

export default {
  mixins: [],
  components: {
    CustomerInfo,
    PickDate,
    Location,
    Position,
  },
  options: {
    styleIsolation: 'shared',
  },
  data() {
    return {
      colors: '',
      noClick: true,
      planId: '',
      customerId: '',
      customer: { latitude: 1 },
      show: false,
      attendants: [],
      selAttendant: {},
      fileData: [],

      pageReq: {
        pagesize: 20,
        pageindex: 1,
        key: '',
      },
      showStart: false,
      date: '',
      schedulingId: '',
      workState: {},
      loadWork: false,
      showPosition: false,
      showLocation: false,
      location: {},
      isGPS: false,
      rangeInfo: {},
      list: ['本月排班详细', '上月详细'],
      detailIndex: 0,
      schedulingData: {},
      lastDay: '',

      showChangeScheduling: false,
      changeSchedulingItem: {},
      changeState: '',
      form: {
        changeDate: '',
        beginTime: '',
        reason: '',
        editTime: false,
        editDuration: 0,
      },
      showChangeDate: false,
      showChangeTime: false,

      rules0: {
        reason: [
          {
            required: true,
            message: '请输入事由',
            trigger: ['blur', 'change'],
          }],
      },
      rules: {
        changeDate: [
          {
            required: true,
            message: '请选择日期',
            trigger: ['blur', 'change'],
          }],
        beginTime: [
          {
            required: true,
            message: '请选择时间',
            trigger: ['blur', 'change'],
          }],
        reason: [
          {
            required: true,
            message: '请输入事由',
            trigger: ['blur', 'change'],
          }],
      },
      rulesCheckIn: {
        reason: [
          {
            required: true,
            message: '请输入事由',
            trigger: ['blur', 'change'],
          }],
      },
    }
  },
  onReady() {
    // 如果需要兼容微信小程序的话，需要用此写法
    this.$refs.calendar.setFormatter(this.formatter)
  },
  onShow() {
    this.setData({
      colors: app.globalData.newColor,
    })
  },
  computed: {
    ...mapState({
      commKey: state => state.init.template.commKey,
      token: state => state.user.token,
      userInfo: state => state.user.info,
      community: state => state.init.community,
    }),
    workKey() {
      return {
        planId: this.schedulingId,
        customerId: this.customerId,
        date: this.date,

      }
    },
    currentYear() {
      return new Date().getFullYear()
    },
  },
  onLoad: async function (options) {
    await this.$onLaunched

    if (options.customerId) this.customerId = options.customerId
    if (options.planId) this.planId = options.planId

    const new_year = new Date().getFullYear()
    const new_month = new Date().getMonth() + 1
    this.lastDay = new_year + '-' + new_month + '-' + new Date(new_year, new_month, 0).getDate()

    this.date = this.today()
    this.getCustomerInfo()
    this.getCustomerFileData()

    await this.getWorkState()

    //this.getLocation()
  },
  methods: {
    init() {
      this.getCustomerInfo()
      this.getCustomerFileData()

      this.getWorkState()

    },
    async getCustomerInfo() {
      if (!this.customerId) return
      uni.showLoading({
        title: '请稍等...',
      })
      const {
        data,
      } = await this.$ut.api('mang/care/customer/info', {
        communityId: this.community.id,
        module: 'long',
        id: this.customerId,
      }).finally(() => {
        uni.hideLoading()
      })
      this.customer = data
      this.getSchedulingMonth()
    },
    async getCustomerFileData() {
      if (!this.customerId) return
      const {
        data,
      } = await this.$ut.api('mang/care/customer/fileData/allList', {
        communityId: this.community.id,
        customerId: this.customerId,
      })
      this.fileData = data
    },
    dateSelect(e) {
      this.date = `${e.year}-${e.month}-${e.day}`
    },
    today() {
      const now = new Date()
      const year = now.getFullYear()
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const day = now.getDate().toString().padStart(2, '0')

      this.$refs.datePicker.value = [0, month - 1, day - 1]
      return `${year}-${month}-${day}`
    },
    async getWorkState() {
      const {
        data,
      } = await this.$ut.api('mang/care/customer/workState', {
        communityId: this.community.id,
        planId: this.planId,
        customerId: this.customerId,
      })
      this.workState = data
      this.loadWork = true
      if (data && data.schedulingId) this.schedulingId = data.schedulingId

    },
    async getLocationInfo(location) {
      const { data } = await this.$ut.api('comm/locationInfo', {
        commKey: this.commKey,
        longitude: location.longitude,
        latitude: location.latitude,
      })
      this.$set(this.rangeInfo, 'address', data.address)
    },
    async getLocation() {
      uni.showLoading({
        title: '请稍等...',
      })
      this.$wxsdk.getLocationToAddress().then(async (location) => {
        this.location = location
        if (!location) return
        this.isGPS = true
        const {
          data,
        } = await this.$ut.api('mang/care/work/checkin/info', {
          communityId: this.community.id,
          planId: this.planId,
          customerId: this.customerId,
          latitude: location.latitude,
          longitude: location.longitude,
        })
        this.rangeInfo = data

        await this.getLocationInfo(location)

        uni.hideLoading()
      }).catch(err => {
        uni.hideLoading()
        console.log(err)
      })
    },
    handleStart() {
      if (!this.schedulingId) {
        this.showStart = true
      } else {
        this.showPosition = true
      }
    },
    work() {
      this.showStart = false
      this.showPosition = true
    },
    async handlePositionClose(workId) {
      this.showPosition = false
      await this.getWorkState()
      this.$tools.routerTo('/pagesA/care/work/start', {
        workId: workId,
        customerId: this.customerId,
      })
    },
    go() {
      this.$tools.routerTo('/pagesA/care/work/start', {
        workId: this.workState.id,
        customerId: this.customerId,
      })

    },
    handleCancel() {
      uni.showLoading({
        title: '请稍等...',
      })
      this.$ut.api('mang/care/work/cancel', {
        communityId: this.community.id,
        workId: this.workState.id,
      }).finally(() => {
        uni.hideLoading()
      }).then(async (res) => {
        await this.getWorkState()
        uni.showToast({
          title: '取消成功',
        })
        // this.$tools.back('downCallback()')
      })
    },
    cancel() {
      uni.showModal({
        title: '询问',
        content: `确定取消当前服务吗？`,
        confirmText: '确定',
        success: res => {
          if (!res.cancel) {
            this.handleCancel()
          }
        },
        fail: err => {
          console.log(`%cuni.showModal失败：`, 'color:green;background:yellow')
        },
      })
    },
    goLocation() {
      this.$tools.routerTo('/pagesA/care/customer/location', {
        customerId: this.customerId,
      })
    },
    sectionChange(index) {
      if (this.detailIndex == index) return
      this.detailIndex = index
      this.getSchedulingMonth()
    },
    async getSchedulingMonth() {
      uni.showLoading({
        title: '请稍等...',
      })
      const { data } = await this.$ut.api('mang/care/customer/scheduling/month', {
        communityId: this.community.id,
        customerId: this.customerId,
        type: this.detailIndex == 1 ? 'pre' : 'curr',
      }).finally(() => {
        uni.hideLoading()
      })

      this.schedulingData = data
    },
    getItemIcon(item) {
      if (item.isWork) { return 'checkmark-circle'} else if (item.isWait) {return 'info-circle'} else return 'clock'
    },
    handleChangeScheduling(item) {
      if (this.detailIndex == 1) return
      // 如果已经完成工作，不允许修改
      if (item.isWork) {
        uni.showToast({
          title: '已完成工作，不允许修改',
          icon: 'none',
          duration: 2000,
        })
        return
      }
      this.showChangeScheduling = true
      this.changeSchedulingItem = item
      this.changeState = ''
      this.form.changeDate = ''
      this.form.beginTime = ''
      this.form.editTime = false,
        this.form.editDuration = 0,
        this.form.reason = ''
      if (item.isCheckIn) {
        this.form.editTime = true
        this.form.editDuration = item.duration || 0
        this.form.workDate = item.workDate
        this.form.beginTime = item.begin
      } else if (this.changeSchedulingItem.isWait && this.changeSchedulingItem.isAudit) {
        this.changeState = 'change'
      }
    },
    changeDateSelect(e) {
      // this.$set(this.form, 'changeTime', `${year}-${month}-${e.day} ${e.hour}:${e.minute}`)
      this.$set(this.form, 'changeDate', e[0])
      this.showChangeDate = false
      if (!this.form.beginTime && this.changeSchedulingItem) {
        this.$set(this.form, 'beginTime', this.changeSchedulingItem.begin)
      }
    },
    changeTimeSelect(e) {
      this.$set(this.form, 'beginTime', e.value)
      this.showChangeTime = false
    },
    async handleScheduling() {
      // if(!this.changeSchedulingItem) return
      let go = true
      await this.$refs.form.validate().then(res => {
      }).catch(errors => {
        go = false
        uni.$u.toast('请检查必须填写的内容！')
      })
      if (!go) return

      const isCheckedIn = this.changeSchedulingItem && this.changeSchedulingItem.isCheckIn

      uni.showModal({
        title: '操作提示',
        content: '确认提交变更申请吗？',
        success: res => {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍等...',
            })
            let apiParams = {
              communityId: this.community.id,
              sechedulingId: this.changeSchedulingItem ? this.changeSchedulingItem.id : '',
              customerId: this.customerId,
              isWait: this.changeState == 'wait',
              workDate: this.form.changeDate,
              beginTime: this.form.beginTime,
              reason: this.form.reason,
              editTime: this.form.editTime,
              editDuration: this.form.editDuration,
            }

            if (isCheckedIn) {
              delete apiParams.isWait
              apiParams.workDate = this.form.workDate
              apiParams.beginTime = this.form.beginTime
            }

            this.$ut.api('mang/care/customer/scheduling/apply', apiParams).then(() => {
              this.showChangeScheduling = false
              this.getSchedulingMonth()
            }).finally(() => {
              uni.hideLoading()
            }).catch(res => {
              uni.showToast({
                title: res.message,
                duration: 3000,
                icon: 'none',
              })
            })

          }
        },
      })
    },
    formatter(day) {
      if (!this.schedulingData || !this.schedulingData.dates) return day
      const d = new Date()
      let month = d.getMonth() + 1
      const findObj = this.schedulingData.dates.find(u => u.day == day.day)
      if (findObj != null && day.month == month) {
        day.bottomInfo = findObj.begin
        day.dot = findObj.isCheckIn
        // day.disabled=true
      }

      return day
    },
    editTime() {
      if (!this.changeSchedulingItem) return
      if (!this.form.editDuration || this.form.editDuration <=
        0) {
        this.form.editDuration = this.changeSchedulingItem.duration
      }
    },
    addScheduling() {
      if (this.detailIndex != 0) return
      this.showChangeScheduling = true
      this.changeSchedulingItem = null
      this.changeState = 'change'
      this.form.changeDate = ''
      this.form.beginTime = ''
      this.form.editTime = true,
        this.form.editDuration = 10,
        this.form.reason = ''
    },

  },
}
</script>

<style>
.cu-modal {
  opacity: 1;
  pointer-events: all;
  z-index: 999;
}
</style>
<style lang="scss" scoped>
.card-box {
  margin: 30rpx 20rpx;
  padding: 40rpx 30rpx 20rpx 30rpx;
  background-size: 100% 100%;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;
  bottom: 15rpx;
}

.scroll-box {
  padding: 60rpx 20rpx 10rpx;
  min-height: 30%;
  max-height: 80%;
}

.pop-title {
  padding-top: 20rpx;
  text-align: center;
}

// .pop-title {
// 	position: absolute;
// 	left: 0;
// 	right: 0;
// 	padding: 15rpx;
// 	margin: auto;
// 	font-size: 30rpx;
// 	font-weight: bold;
// 	text-align: center;
// }

.clear {
  position: absolute;
  right: 0;
  padding: 15rpx 30rpx 15rpx 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  text-align: center;
}


.start-btn {
  width: 492rpx;
  height: 70rpx;
  background: linear-gradient(90deg, var(--colors), var(--colors2));
  box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
  border-radius: 35rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.normal-btn {
  width: 492rpx;
  height: 70rpx;
  background: linear-gradient(90deg, var(--colors), var(--colors));
  box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
  border-radius: 35rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}


.modal-box {
  width: 610rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  padding-bottom: 30rpx;

  .head-bg {
    width: 100%;
    height: 210rpx;
  }


  .btn-box {
    margin-top: 80rpx;

  }
}

.text-title {
  width: 120rpx;
}
</style>

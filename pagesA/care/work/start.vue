<template>
  <ut-page>
    <f-navbar fontColor="#fff" :bgColor="colors" title="护理服务" navbarType="2"></f-navbar>
    <view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
      <customer-info :detail="customer" :show-all="false" :colors="colors" :file-data="fileData" />

      <view class="flex align-center justify-between margin-top-xs">
        <view>
          <text v-if="customer.groupName" class="text-sm" :style="{ color: colors }">({{ customer.groupName }})</text>
          <text class="text-sm">护理员：</text>
          <text class="text-sm">{{ customer.attendantName }}</text>
        </view>
        <view>
          <u-button
            type="primary"
            :color="colors"
            text="客户照片修改"
            :plain="true"
            size="mini"
            @click="jumpImgHead"></u-button>
        </view>
      </view>
    </view>

    <view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-content">
      <view>
        <text class="text-gray">计划时间：</text>
        <text class="text-bold">{{ workInfo.workDate }}</text>
        <text class="margin-left-lg text-bold">{{ workInfo.begin }}</text>
        <text class="margin-lr-sm text-gray">至</text>
        <text class="text-bold">{{ workInfo.end }}</text>
      </view>
      <view>
        <view>
          <text class="text-gray">实际开始：</text>
          <text class="text-bold">{{ workInfo.checkInTime }}</text>
        </view>
      </view>
      <view class="padding-top-sm text-center">
        <view>
          <text class="text-gray">倒计时</text>
        </view>
        <view class="text-bold text-xxl">
          <text :style="{ color: colors }">{{ surplusTime.hour }}</text>
          <text class="text-gray text-df margin-lr-xs">时</text>
          <text :style="{ color: colors }">{{ surplusTime.minute }}</text>
          <text class="text-gray text-df margin-lr-xs">分</text>
          <text :style="{ color: colors }">{{ surplusTime.second }}</text>
          <text class="text-gray text-df margin-lr-xs">秒</text>
        </view>
      </view>
    </view>

    <!-- 		<view class="cu-card margin-sm radius shadow padding-tb s-gray bg-white">
			<hh-grid  :colors="colors" :list="menus" :count="menus.length>4?4:menus.length" :cell-color="true" :jump-param="jumpParam" />
		</view> -->
    <view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-content" style="position: relative">
      <view class="text-lg margin-bottom-sm">服务项目</view>
      <view class="text-sm">
        <project-item
          v-for="(item, index) in projectData"
          :key="index"
          :detail="item"
          :colors="colors"
          @select="select"
          :tip="item.isSubmit ? '已提交' : ''">
        </project-item>
      </view>
      <view class="btn-mang">
        <u-button
          type="primary"
          :color="colors"
          text="项目管理"
          :plain="true"
          size="mini"
          @click="jumpProject"></u-button>
      </view>
    </view>

    <view style="padding-bottom: 90rpx"></view>
    <view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
    <view class="padding-bottom"></view>

    <ut-fixed safe-area-inset position="bottom" background="#fff">
      <view class="padding-tb-sm padding-lr-lg">
        <view v-if="!projectData || !projectData.length">
          <u-button
            type="primary"
            shape="circle"
            color="blue"
            text="需要先选择服务项目后才能签退"
            size="normal"
            @click="jumpProject"></u-button>
        </view>
        <view v-else>
          <u-button
            v-if="!isOver"
            type="primary"
            shape="circle"
            color="red"
            text="我要签退(时间未到)"
            disabled
            size="normal"></u-button>
          <u-button
            v-else
            type="primary"
            shape="circle"
            :color="colors"
            text="我要签退"
            size="normal"
            @click="checkOutCheck"></u-button>
        </view>
      </view>
    </ut-fixed>

    <u-popup
      v-if="showPunchOff"
      :show="showPunchOff"
      mode="bottom"
      round="10"
      :closeable="true"
      :safe-area-inset-bottom="true"
      :mask-close-able="true"
      close-icon-pos="top-left"
      :z-index="998"
      :overlay-style="{ zIndex: 998 }"
      @close="showPunchOff = false">
      <view class="padding-tb-xs text-center">签退</view>
      <punch-off
        :range-info="rangeInfo"
        :work-id="workId"
        :customer="customer"
        :checkInfo="checkOutInfo"
        :location="location"
        :markers="markers"
        :colors="colors"
        @close="handlePunchOffClose" />
    </u-popup>
  </ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
// import HhGrid from '@/pagesA/components/hh-grid.vue'
import ProjectItem from '@/pagesA/components/project-item.vue'
import PunchOff from './punch-off.vue'

export default {
  mixins: [],
  components: {
    CustomerInfo,
    ProjectItem,
    PunchOff,
    // HhGrid,
  },
  options: {
    styleIsolation: 'shared',
  },
  data() {
    return {
      colors: '',

      workId: '',
      begin: false,
      customerId: '',
      customer: {},
      workInfo: {},
      show: false,
      attendants: [],
      selAttendant: {},
      fileData: [],
      projectData: [],

      timeCount: 0,
      surplusTime: {
        hour: '',
        minute: '',
        second: '',
      },
      isOver: false,

      showPunchOff: false,
      rangeInfo: {},
      isGPS: false,
      location: {},
      checkOutInfo: {},

      intervalTime: {},
      circles: [], // 中心签到圈
      markers: [],

      allowStartSelect: false,
    }
  },
  onShow() {
    this.setData({ colors: app.globalData.newColor })
  },
  computed: {
    ...mapState({
      commKey: (state) => state.init.template.commKey,
      token: (state) => state.user.token,
      userInfo: (state) => state.user.info,
      community: (state) => state.init.community,
    }),
    jumpParam() {
      return { workId: this.workId }
    },
  },
  onLoad: async function (options) {
    await this.$onLaunched

    if (options.workId) this.workId = options.workId
    if (options.customerId) this.customerId = options.customerId

    uni.$on('refreshList', this.refresh)

    //this.beginJump()
    this.getCustomerInfo()
    await this.getWorkInfo()
    this.getWorkProject()
    this.timeShow()
    //this.getStateInfo()
  },
  onUnload() {
    clearInterval(this.intervalTime)
    uni.$off('refreshList', this.refresh)
  },

  watch: {
    showPunchOff: {
      handler(v) {
        if (v == false) {
          console.log('watch')
          clearInterval(this.intervalTime)
        }
      },
    },
  },
  methods: {
    jumpImgHead() {
      this.$tools.routerTo('/pagesA/care/work/img-head', {
        workId: this.workId,
      })
    },
    jumpProject() {
      if (!this.allowStartSelect && !this.isOver) {
        uni.showToast({
          title: '请等待时间结束才能选择项目',
          icon: 'none',
          duration: 3000,
        })
        return
      }
      this.$tools.routerTo('/pagesA/care/work/project', {
        workId: this.workId,
      })
    },
    async refresh() {
      this.getWorkInfo()
      this.getWorkProject()
      this.isOver = false
    },

    async getWorkInfo() {
      if (!this.workId) return
      const { data } = await this.$ut.api('mang/care/work/info', {
        communityId: this.community.id,
        workId: this.workId,
      })
      this.workInfo = data || {}
      this.timeCount = 0

      this.allowStartSelect = data.allowStartSelect
    },
    timeShow() {
      if (!this.workInfo.surplusSecond) {
        this.surplusTime.hour = '00'
        this.surplusTime.minute = '00'
        this.surplusTime.second = '00'
        this.isOver = true
        //return
      } else {
        let diffTime = this.workInfo.surplusSecond - this.timeCount
        this.surplusTime.hour = Math.floor((diffTime / 60 / 60) % 24)
        this.surplusTime.hour = this.surplusTime.hour < 10 ? '0' + this.surplusTime.hour : this.surplusTime.hour
        this.surplusTime.minute = Math.floor((diffTime / 60) % 60)
        this.surplusTime.minute = this.surplusTime.minute < 10 ? '0' + this.surplusTime.minute : this.surplusTime.minute
        this.surplusTime.second = Math.floor(diffTime % 60)
        this.surplusTime.second = this.surplusTime.second < 10 ? '0' + this.surplusTime.second : this.surplusTime.second
        this.timeCount++
        if (this.timeCount > this.workInfo.surplusSecond) {
          this.surplusTime.hour = '00'
          this.surplusTime.minute = '00'
          this.surplusTime.second = '00'
          this.isOver = true
          clearInterval(this.intervalTime)
          //return
        }
      }

      setTimeout(this.timeShow, 1000)
    },
    async getCustomerInfo() {
      if (!this.customerId) return
      const { data } = await this.$ut.api('mang/care/customer/info', {
        communityId: this.community.id,
        module: 'long',
        id: this.customerId,
      })
      this.customer = data || {}
    },
    async getWorkProject() {
      const { data } = await this.$ut.api('mang/care/work/project/ruleListpg', {
        communityId: this.community.id,
        workId: this.workId,
        pagesize: 100,
        pageindex: 1,
      })
      this.projectData = data.info
    },

    setStar(code, value) {
      let obj = this.menus.find((u) => u.code == code)
      if (obj) this.$set(obj, 'star', value)
    },
    async getStateInfo() {
      if (!this.workId) return
      const { data } = await this.$ut.api('mang/care/work/state', {
        communityId: this.community.id,
        workId: this.workId,
      })
      this.setStar('location', data.needCheckIn)
      this.setStar('photo', data.needPhoto)
      this.setStar('project', data.needProject)
      this.setStar('imgHead', data.needImgHead)
    },
    handlePunchOffClose() {
      this.showPunchOff = false
      uni.switchTab({
        url: '/pages/customer/index',
      })

      setTimeout(() => {
        uni.showToast({
          title: '提交成功',
        })
      }, 100)
    },
    checkOutCheck() {
      uni.showLoading({
        title: '请稍等...',
      })
      this.$ut
        .api('mang/care/work/checkout/info', {
          communityId: this.community.id,
          workId: this.workId,
        })
        .finally(() => {
          uni.hideLoading()
        })
        .then((res) => {
          uni.hideLoading()
          this.checkOutInfo = res.data || {}

          if (this.checkOutInfo.requireCheck) {
            this.showPunchOff = true

            this.getLocation()
            clearInterval(this.intervalTime)
            this.intervalTime = setInterval(() => {
              this.getLocation() // 接口方法
            }, 5000)
          }
        })
        .catch((res) => {
          uni.showToast({
            title: res.message,
            duration: 3000,
            icon: 'none',
          })
        })
    },
    goSubmit() {
      this.$ut
        .api('mang/care/work/submit', {
          communityId: this.community.id,
          workId: this.workId,
        })
        .then(() => {
          // this.$tools.back('downCallback()')
          uni.switchTab({
            url: '/pages/customer/index',
          })

          setTimeout(() => {
            uni.showToast({
              title: '提交成功',
            })
          }, 100)
        })
    },
    async getLocation() {
      this.$wxsdk
        .getLocationToAddress()
        .then(async (location) => {
          this.location = location
          if (!location) return
          this.isGPS = true
          const { data } = await this.$ut.api('mang/care/work/checkout/range', {
            communityId: this.community.id,
            workId: this.workId,
            latitude: location.latitude,
            longitude: location.longitude,
          })
          this.rangeInfo = data

          //this.rangeInfo.inRange=true   //test

          //await this.getLocationInfo(location)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    async getLocationInfo(location) {
      const { data } = await this.$ut.api('comm/locationInfo', {
        commKey: this.commKey,
        longitude: location.longitude,
        latitude: location.latitude,
      })
      this.$set(this.rangeInfo, 'address', data.address)
    },
  },
}
</script>

<style>
.card-box {
  margin: 30rpx 20rpx;
  padding: 40rpx 30rpx 20rpx 30rpx;
  background-size: 100% 100%;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;
  bottom: 15rpx;
}

.scroll-box {
  padding-top: 60rpx;
  padding-bottom: 10rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  min-height: 30%;
  max-height: 80%;
}

.clear {
  position: absolute;
  right: 0;
  padding: 15rpx 30rpx 15rpx 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  text-align: center;
}

.btn-mang {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
}
</style>

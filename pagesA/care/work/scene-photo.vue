<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="现场照片" navbarType="2" />
		</ut-top>
		
		<view class="bg-white padding-tb-sm padding-lr-sm" :style="{minHeight:'calc(100vh - '+topWrapHeight+'px)'}">
			<view class="image-box">
				<ut-image-upload
					ref="upload"
					name="file"
					v-model="myPhotos"
					mediaType="image"
					:colors="colors"
					:max="20"
					:headers="headers"
					:action="uploadInfo.server+uploadInfo.single||''"
					:preview-image-width="1200"
					:width="345"
					:height="260"
					:border-radius="8"
					@uploadSuccess="uploadFaceSuccess"
					@imgDelete="imgDelete">
				</ut-image-upload>
			</view>
			<view class="margin-top-lg padding-lr-sm text-content">
				<view>描述：</view>
				<u--textarea v-model="remark" placeholder="请输入内容" :maxlength="500" count confirmType="done"></u--textarea>
			</view>
		</view>

		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button v-if="begin" type="primary" shape="circle" :color="colors" text="保存并下一步" size="normal"  @click="saveScene"></u-button>
				<u-button v-else type="primary" shape="circle" :color="colors" text="保存现场照片" size="normal"  @click="saveScene"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight:0,
			workId:'',
			begin:false,
			
			myPhotos: [],
			
			setInfo:{},
			form:{},
			remark:'',
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers(){
			return {
				Token:this.uploadInfo.token
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.workId)  this.workId = options.workId
		if (options.begin)  this.begin = options.begin
		
		this.getSceneSetInfo()
		this.getSceneInfo()
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		uploadFaceSuccess(res) {
			res.forEach(img=>{
				const item=img.data
				this.myPhotos.push({
					id:'',
					url:this.uploadInfo.preview + '?file=' + item.name + item.ext,
				})
			})

		},
		imgDelete(e) {
			console.log(e)
			//this.form.imgHead = ''
		},
		async getSceneSetInfo() {
			if (!this.workId) return
			const {data} = await this.$ut.api('mang/care/work/scene/setInfo', {
				communityId: this.community.id
			})
			this.setInfo=data
		},
		async getSceneInfo() {
			if (!this.workId) return
			const {data} = await this.$ut.api('mang/care/work/scene/list', {
				communityId: this.community.id,
				workId:this.workId,
			})
			this.form=data
			this.remark=data.remark
			data.imgs.forEach(item=>{
				this.myPhotos.push({
					id:item.id,
					url:item.url,
				})
			})
		},
		saveScene(){
			if (!this.workId) return
			this.$ut.api('mang/care/work/scene/save', {
				communityId: this.community.id,
				workId:this.workId,
				remark:this.remark,
				imgs:this.myPhotos,
			}).then(()=>{
				if(this.begin){
					this.$tools.routerTo('/pagesA/care/work/project', {workId:this.workId,begin:this.begin })
				}else{
					this.$tools.back('getStateInfo')
				}
			})
		}
	},
}
</script>

<style lang="scss" scoped>
	:deep(.ut-image-upload-list) {
		justify-content: center;
		align-items: center;
	}
</style>

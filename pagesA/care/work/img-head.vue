<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="头像修改" navbarType="2"></f-navbar>
		<view class="padding-lg flex justify-center">
			<diy-person-upload
				v-model="myPhotos"
				mediaType="image"
				:disabled="false"
				:colors="colors"
				:max="1"
				:width="500"
				:height="600"
				:preview-image-width="1200"
				:border-radius="8"
				:headers="headers"
				:action="uploadInfo.server+uploadInfo.single"
				@uploadSuccess="uploadFaceSuccess"
				@imgDelete="imgDelete"
			 />
		 </view>
		<view class="padding-lr padding-tb">
			<u-button type="primary" shape="circle" :color="colors" text="保存头像" :plain="false" size="normal" @click="save"/>
		</view>
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
import DiyPersonUpload from '@/pagesA/components/ut-person-upload/up-person-upload.vue'
export default {
	components: {
		DiyPersonUpload,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			myPhotos: '',
			imgUrl:'SSS',
			form: {},
			workId:'',
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers() {
			return {
				Token: this.uploadInfo.token,
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.workId) this.workId = options.workId
		
		this.getCustomerInfo()
	},
	methods: {
		uploadFaceSuccess(res) {
			const item=res.data		
			this.imgUrl=this.uploadInfo.preview + '?file=' + item.name + item.ext
		},
		imgDelete(e) {
			this.imgUrl = ''
		},
		async getCustomerInfo(){
			if(!this.workId) return
			const {data} = await this.$ut.api('mang/care/work/imgHead/info', {
				communityId:this.community.id,
				workId:this.workId,
			})
			this.form=data
			if (this.form && this.imgUrl && this.form.imgHead) {
				this.imgUrl = data.imgHead + '&token=' + this.uploadInfo.token
				this.myPhotos = this.imgUrl
			}
		},
		save(){
			this.$ut.api('mang/care/work/imgHead/save', {
				communityId:this.community.id,
				workId:this.workId,
				imgHead:this.imgUrl,
			}).then(()=>{
				this.$tools.back('getStateInfo')
				
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
				
			})
		}
	},
}
</script>

<style>
	.page {
		background-color: #fff;
		height: 100vh;
	}
	
</style>
<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="异常申报" navbarType="1" />
		</ut-top>
		<view class="padding-lr-xs">
			
			<view class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content">
				<view>
					<text>客户：</text>
					<text class="text-df text-bold">{{workErrorInfo.name}}</text>
					<text v-if="workErrorInfo.sex && workErrorInfo.sex==1" class="margin-left-xs">男</text>
					<text v-else-if="workErrorInfo.sex && workErrorInfo.sex==2" class="margin-left-xs">女</text>
					<text class="margin-left-lg">({{workErrorInfo.phone}})</text>
				</view>
				<view v-if="workErrorInfo.idcard">
					<text>证件：</text>
					<text class="">{{workErrorInfo.idcard}}</text>
				</view>
				<view>
					<text>排班计划：</text>
					<text class="text-df text-bold margin-left">{{workErrorInfo.workDate}}</text>
					<text class="text-df text-bold margin-left-sm">{{workErrorInfo.schedulingBegin}}</text>
					<text class="margin-lr-xs">-</text>
					<text class="text-df text-bold">{{workErrorInfo.schedulingEnd}}</text>
					<text class="margin-left-sm text-xs">(</text>
					<text class="text-xs">{{workErrorInfo.schedulingDuration}}</text>
					<text class="text-xs">分钟)</text>
				</view>
				<view class="flex align-center">
					<text style="width:120rpx" class="text-bold text-sm">异常原因：</text>
					<view class="flex flex-sub flex-wrap">
						<u-tag class="margin-left margin-top-sm" v-if="!workErrorInfo.checkInTime" type="warning"text="未签到"  size="mini"/>
						<u-tag class="margin-left margin-top-sm" v-if="!workErrorInfo.checkOutTime" type="warning"text="未签退"  size="mini"/>
						<u-tag class="margin-left margin-top-sm" v-if="!workErrorInfo.uploadData" type="warning"text="未上传护理资料"  size="mini"/>
						<u-tag class="margin-left margin-top-sm" v-if="!workErrorInfo.projects || !workErrorInfo.projects.length" type="warning"text="未有服务项目"  size="mini"/>
						<u-tag class="margin-left margin-top-sm" v-if="workErrorInfo.projectTotalMaxDuration<workErrorInfo.schedulingDuration" type="warning"text="服务总时间不够"  size="mini"/>
						<u-tag class="margin-left margin-top-sm" v-if="workErrorInfo.projectTotalMinDuration>workErrorInfo.schedulingDuration" type="warning"text="服务总时间超出太多"  size="mini"/>
					</view>
				</view>
			</view>
			
			<u--form ref="form" labelPostion="left" :model="form" labelWidth="auto" :labelStyle="{color:'#a0a0a0'}">
			<view v-if="!workErrorInfo.checkInTime || !workErrorInfo.checkOutTime" class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content" >
				
					<u-form-item v-if="!workErrorInfo.checkInTime" label="签到时间" prop="checkInTime" borderBottom>
						<view class="full-width" @click="showCheckInTime=true" >
							<u--input v-model.trim="form.checkInTime" border="none" disabled disabledColor="#ffffff"
								placeholder="请选择签到时间" inputAlign='center' style="pointer-events:none" />
						</view>
					</u-form-item>
					<u-form-item v-if="!workErrorInfo.checkOutTime" label="签退时间" prop="checkOutTime" borderBottom>
						<view class="full-width" @click="showCheckOutTime=true" >
							<u--input v-model.trim="form.checkOutTime" border="none" disabled disabledColor="#ffffff"
								placeholder="请选择签退时间" inputAlign='center' style="pointer-events:none" />
						</view>		
					</u-form-item>
			</view>
			<view v-if="!workErrorInfo.projects || !workErrorInfo.projects.length || workErrorInfo.projectTotalMaxDuration<workErrorInfo.schedulingDuration || workErrorInfo.projectTotalMinDuration>workErrorInfo.schedulingDuration" 
				class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content" >
				<view class="flex justify-between">
					<view class="text-df text-bold">服务项目</view>
					<view>
						<u-button type="primary"  :color="colors" text="增加服务项目" :plain="true" size="mini" @click="addProject"/>
					</view>
				</view>
				<u-swipe-action ref="swipeUserList">
				<template v-for="(item,index) in workErrorInfo.projects">	
					<u-swipe-action-item
						:key="index"
						:name="item.projectId"
						:options="getProjectActionOption(item)"
						@longpress="longpress(item)"
						@click="projectActionOp">
						<view class="padding-lr padding-tb-sm">
							<project-item :detail="item" :colors="colors" @select="select" :tip="item.isApplyError?'已提交':''">
								<template #op>
									<u-button type="primary" shape="circle" :color="colors" :text="item.isApplyError?'查看':'选择'" :plain="false" size="small" @tap="select(item)"/>
								</template>
							</project-item>
						</view>
					</u-swipe-action-item>
					
				</template>
				</u-swipe-action>
			</view>
			<view v-if="fileData && fileData.length && !workErrorInfo.uploadData" class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content" >
				<scroll-view  scroll-y="true" class="scroll-box" >
				<template v-for="(item,index) in fileData">
				<view :key="index" class="bg-white text-content item-box">
					<view class="text-bold text-lg">{{item.title}}</view>
					<template v-for="(detail,dIndex) in item.details">
					<view :key="dIndex" class="padding-left-lg">
						<view class="margin-left-xs">
							<text>{{detail.title}}</text>
							<text v-if="detail.require" class="text-xs margin-left-xs" :style="{color:colors}">(必填)</text>
							<text v-else class="text-xs margin-left-xs" >(选填)</text>
						</view>
						<view class="image-box">
							<ut-image-upload
								ref="upload"
								name="file"
								v-model="detail.files"
								mediaType="image"
								:colors="colors"
								:max="20"
								:headers="headers"
								:action="uploadInfo.server+uploadInfo.single||''"
								:preview-image-width="1200"
								:width="200"
								:height="160"
								:border-radius="8"
								:disabled="false"							
								:add="isEdit==true"
								:remove="isEdit==true"
								@uploadSuccess="uploadFaceSuccess($event,detail)"
								>
							</ut-image-upload>
						</view>
					</view>
					</template>
				</view>
				</template>
				</scroll-view>
			</view>
			<view class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content" >
				<view class="flex justify-between">
					<view class="text-df text-bold">异常证明材料</view>
				</view>
				<view class="flex">
					<view class="image-box">
						<ut-image-upload
							ref="upload"
							name="file"
							v-model="form.applyData"
							mediaType="image"
							:colors="colors"
							:max="10"
							:headers="headers"
							:action="uploadInfo.server+uploadInfo.single||''"
							:preview-image-width="1200"
							:width="128"
							:height="128"
							:border-radius="8"
							@uploadSuccess="uploadProveSuccess">
						</ut-image-upload>
					</view>
					
				</view>
				<view class="flex text-bold align-center">
					<view class="text-df text-bold">异常说明</view>
					<view class="text-red">*</view>
				</view>
				<view >
					<u--textarea v-model="form.reason" placeholder="请输入内容" :maxlength="500" count confirmType="done"></u--textarea>
				</view>
			</view>
			</u--form>
		</view>
		
		<view class="padding-lr padding-tb bg-white">
			<u-button type="primary" shape="circle" :color="colors" text="保存异常申报" :plain="false" size="normal" @click="save"/>
		</view>
		
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		
		<u-datetime-picker :show="showCheckInTime" v-model="form.checkInTime" mode="time" :closeOnClickOverlay="true"
					@confirm="checkInTimeSelect" @close="showCheckInTime=false" @cancel="showCheckInTime=false"></u-datetime-picker>
					
		<u-datetime-picker :show="showCheckOutTime" v-model="form.checkOutTime" mode="time" :closeOnClickOverlay="true"
					@confirm="checkOutTimeSelect" @close="showCheckOutTime=false" @cancel="showCheckOutTime=false"></u-datetime-picker>
					
		<u-popup :show="showProject" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="998" :overlay-style="{zIndex:998}" @close="showProject=false">
			<view class="pop-title">项目选择</view>
				
				<project :project-data="projectData" @selectProject="selectProject"/>
			
		</u-popup>
		

	</ut-page>
	

</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import PickDate from '@/pagesA/components/pickDate.vue'
import ProjectItem from '@/pagesA/components/project-item.vue'
import Project from './project'


export default {
	components: {
		PickDate,
		ProjectItem,
		Project,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight: 0,
			declareId:'',
			form:{
				checkInTime:'',
				checkOutTime:'',
				applyData:[],
				reason:'',
			},
			rules:{
				
			},
			
			workErrorInfo:{},
			fileData:[],
			isEdit:true,
			showCheckInTime:false,
			showCheckOutTime:false,
			showProject:false,
			projectData:[],
			
			
			
		}
	},

	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	onReady() {
			//onReady 为uni-app支持的生命周期之一
	    	this.$refs.form.setRules(this.rules)
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers(){
			return {
				Token:this.uploadInfo.token
			}
		},

	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.declareId) this.declareId = options.declareId
		
		this.getWorkErrorInfo()
		this.dataUpload()
		this.getAllProject()
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		async getWorkErrorInfo(){
			if(!this.declareId) return
			uni.showLoading({
				title:'请稍等...',
			})
			const {data} = await this.$ut.api('mang/care/customer/scheduling/declare/info', {
				communityId:this.community.id,
				id:this.declareId,
			}).finally(()=>{uni.hideLoading()})
			this.workErrorInfo=data ||{}
			
			if(this.workErrorInfo.checkInTime){
				const checkInDT = new Date(this.workErrorInfo.checkInTime)
			    const checkInHour = checkInDT.getHours().toString().padStart(2, '0')
			    const checkInMinute = checkInDT.getMinutes().toString().padStart(2, '0')
				this.form.checkInTime=`${checkInHour}:${checkInMinute}`
			}
			if(this.workErrorInfo.checkOutTime){
				const checkOutDT = new Date(this.workErrorInfo.checkOutTime)
			    const checkOutHour = checkOutDT.getHours().toString().padStart(2, '0')
			    const checkOutMinute = checkOutDT.getMinutes().toString().padStart(2, '0')
				this.form.checkOutTime=`${checkOutHour}:${checkOutMinute}`
			}

		},
		async dataUpload(){
			if(!this.declareId) return
			uni.showLoading({
				title:'请稍等...'
			})
			const {data} = await this.$ut.api('mang/care/customer/scheduling/declare/fileData',{
				communityId:this.community.id,
				schedulingId:this.declareId,
			}).finally(()=>{uni.hideLoading();})
			this.fileData=data
		
		},
		async getAllProject(){
			if(!this.declareId) return 
			const {data}= await this.$ut.api('mang/care/customer/scheduling/declare/project',{
				communityId:this.community.id,
				schedulingId:this.declareId,
				pagesize:100,
			})
			this.projectData=data			
			
		},
		checkInTimeSelect(e){
			this.$set(this.form, 'checkInTime', e.value)
			this.showCheckInTime=false
		},
		checkOutTimeSelect(e){
			this.$set(this.form, 'checkOutTime', e.value)
			this.showCheckOutTime=false
		},
		longpress(item) {
			console.log(item)
		},
		getProjectActionOption(item) {
							
			const btnDelete = {
				text: '删除项目',
				code:'delete',
				style: {
					backgroundColor: '#f56c6c'
				}
			}
		
			let data = []
			data.push(btnDelete)
			
			return data
		},
		projectActionOp(data){
			if (data.code == 'delete'){
				if(this.workErrorInfo.projects && this.workErrorInfo.projects.length){
					var index=this.workErrorInfo.projects.findIndex(u=>u.projectId==data.name)	
					if(index>=0)	this.workErrorInfo.projects.splice(index,1)
				}
			}
			this.$refs['swipeUserList'].closeAll()
		},
		addProject(){
			this.showProject=true
			
			if(this.projectData && this.workErrorInfo.projects && this.workErrorInfo.projects.length){
				this.projectData.forEach(project=>{
					let obj=this.workErrorInfo.projects.find(u=>u.projectId==project.id)
					if(obj){
						this.$set(project,'checked',true)
					}else{
						this.$set(project,'checked',false)
					}
				})
			}
		},
		selectProject(projects){
			this.showProject=false		
				
			if(!this.workErrorInfo.projects) this.$set(this.workErrorInfo,'projects',[])
			if(!projects || !projects.length) return
			projects.forEach(project=>{
				let obj=this.workErrorInfo.projects.find(u=>u.projectId==project.id)
				if(!obj){
					this.workErrorInfo.projects.push({
						projectId:project.id,
						projectName:project.name,
						govCode:project.govCode,
						requireMinDuration:project.minDuration,
						requireMaxDuration:project.maxDuration,						
						
					})
				}
			})
		},
		uploadFaceSuccess(res,detail){
			res.forEach(img=>{
				const item=img.data
				detail.files.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
			})
		},
		uploadProveSuccess(res){
			res.forEach(img=>{
				const item=img.data
				this.form.applyData.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
			})
		},
		save(){
			this.$ut.api('mang/care/customer/scheduling/declare/apply', {
				communityId:this.community.id,
				schedulingId:this.declareId,
				projectIds:this.workErrorInfo.projects.map(u=>u.projectId),
				datas:this.fileData,
				...this.form,
			}).then(()=>{
				this.$tools.back('downCallback()')			
				
				setTimeout(()=>{
					uni.showToast({
						title:'申报成功'
					})
				},100)
				
			})
		},
		itemClick(item){
			this.selectItem=item
		},
	
	},
}
</script>


<style lang="scss" scoped>
.pop-title {
	padding-top: 20rpx;
	text-align: center;
}

:deep(.u-checkbox__icon-wrap) {
	width:48rpx;
	height:48rpx;
	span{
		font-size: 40rpx;
	}
}

</style>

<template>
  <ut-page>
    <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
      <f-navbar fontColor="#fff" :bgColor="colors" title="我的调班申请" navbarType="2"></f-navbar>
    </ut-top>
    <mescroll-body
      ref="mescrollRef"
      :top="topWrapHeight + 'px'"
      :top-margin="-topWrapHeight + 'px'"
      bottom="0"
      :up="upOption"
      :safearea="true"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @emptyclick="emptyClick">
      <u-swipe-action ref="swipeUserList">
        <template v-for="(item, index) in dataList">
          <view class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-content">
            <u-swipe-action-item
              :key="index"
              :name="item.id"
              :options="getActionOption(item)"
              @longpress="longpress(item)"
              @click="actionOp">
              <view>
                <text>客户：</text>
                <text class="text-df text-bold">{{ item.customerName }}</text>
              </view>
              <view>
                <text>原来排班：</text>
                <text class="text-df text-bold">{{ item.currWorkDate }}</text>
                <text class="text-df text-bold margin-left">{{ item.currBegin }}</text>
                <text class="margin-lr-xs">-</text>
                <text class="text-df text-bold">{{ item.currEnd }}</text>
              </view>
              <view v-if="item.destWorkDate">
                <text>调整排班：</text>
                <text class="text-df text-bold">{{ item.destWorkDate }}</text>
                <text class="text-df text-bold margin-left">{{ item.destBegin }}</text>
                <text class="margin-lr-xs">-</text>
                <text class="text-df text-bold">{{ item.destEnd }}</text>
              </view>
              <view v-else>
                <text>调整排班：</text>
                <text>待定</text>
              </view>
              <view>
                <text>申请时间：</text>
                <text class="text-gray text-sm">{{ item.createTime }}</text>
              </view>
              <view>
                <text>审核状态：</text>
                <text v-if="item.auditState">已审</text>
                <text v-else>未审</text>
                <text v-if="item.auditState == 1">(通过)</text>
              </view>
            </u-swipe-action-item>
          </view>
        </template>
      </u-swipe-action>
    </mescroll-body>
    <view v-if="!dataList || !dataList.length" class="tip padding-tb text-center" @click="jumpCustomer"
      >需要调班需跳转到“我的客户”页(点击这里跳转)</view
    >
    <ut-login-modal :colors="colors"></ut-login-modal>
  </ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
// import WorkItem from './work-item.vue'

export default {
  mixins: [MescrollMixin], // 使用mixin
  components: {
    MescrollBody,
    // WorkItem,
  },
  options: {
    styleIsolation: 'shared',
  },
  data() {
    return {
      colors: '',
      topWrapHeight: 0,
      upOption: {
        noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
        empty: {
          icon: require('@/pagesA/components/image/nodata.png'),
          tip: '~ 没有数据 ~', // 提示
        },
      },
      pageReq: {
        pagesize: 10,
        pageindex: 1,
        key: '',
      },
      firstLoad: true,
      dataList: [],
    }
  },
  onShow() {
    this.setData({ colors: app.globalData.newColor })
  },
  computed: {
    ...mapState({
      commKey: (state) => state.init.template.commKey,
      token: (state) => state.user.token,
      userInfo: (state) => state.user.info,
      community: (state) => state.init.community,
    }),
  },
  onLoad: async function (options) {
    await this.$onLaunched
  },
  methods: {
    getHeight(h) {
      this.topWrapHeight = h
    },
    /*下拉刷新的回调 */
    downCallback() {
      this.pageReq.pageindex = 1
      this.mescroll.resetUpScroll()
    },
    async upCallback(page) {
      this.isShow = false
      this.$ut
        .api('mang/care/customer/scheduling/applyList', {
          communityId: this.community.id,
          ...this.pageReq,
        })
        .then(({ data }) => {
          setTimeout(
            () => {
              this.mescroll.endBySize(data.info.length, data.record)
            },
            this.firstLoad ? 0 : 500
          )
          this.firstLoad = false

          if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
          this.pageReq.pageindex++
          this.dataList = this.dataList.concat(data.info)
        })
        .catch((e) => {
          this.pageReq.pageindex--
          this.mescroll.endErr()
        })
    },

    longpress(item) {
      console.log(item)
    },
    emptyClick() {},
    jumpCustomer() {
      this.$tools.routerTo('/pagesA/care/customer/index', {})
    },
    getActionOption(item) {
      const btnApplyCancel = {
        text: '取消申请',
        code: 'applyCancel',
        style: {
          backgroundColor: '#ffaa7f',
        },
      }
      let data = []

      if (item.auditState != 1) {
        data.push(btnApplyCancel)
      } else {
        data.push({
          text: '已经审核',
          code: 'applyAuditOK',
        })
      }

      return data
    },
    actionOp(data) {
      if (data.code == 'applyAuditOK') {
        this.$refs['swipeUserList'].closeAll()
        return
      }
      let content = ''
      if (data.code == 'applyCancel') {
        content = '确认取消该申请吗？'
      }
      if (!content) {
        this.$refs['swipeUserList'].closeAll()
        return
      }

      uni.showModal({
        title: '操作提示',
        content: content,
        success: (res) => {
          if (res.confirm) {
            if (data.code == 'applyCancel') {
              this.applyCanceln(data.name)
                .then(() => {
                  let objIndex = this.dataList.findIndex((u) => u.id == data.name)
                  this.dataList.splice(objIndex, 1)
                })
                .catch((err) => {
                  console.error('取消申请失败:', err)
                  uni.showToast({
                    title: '取消申请失败',
                    icon: 'none',
                  })
                })
            }
          }
          this.$refs['swipeUserList'].closeAll()
        },
        fail: () => {
          this.$refs['swipeUserList'].closeAll()
        },
      })
    },
    applyCanceln(id) {
      return new Promise((resolve, reject) => {
        this.$ut
          .api('mang/care/customer/scheduling/applyCancel', {
            communityId: this.community.id,
            ids: [id],
          })
          .then((res) => {
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
  },
}
</script>
<style></style>
<style lang="scss" scoped>
.tip {
  position: absolute;
  bottom: 0;
  width: 100%;
}
</style>

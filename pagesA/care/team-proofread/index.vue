<template>
    <proofread-task-list
        ref="proofreadTaskList"
        api-path="mang/care"
        check-page-path="/pagesA/care/team-proofread/check"
        title="校对任务列表"
        :colors="colors"
    />
</template>

<script>
import ProofreadTaskList from '@/pagesA/components/proofread/proofread-task-list.vue'

const app = getApp()

export default {
    components: {
        ProofreadTaskList,
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            colors: '',
        }
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.$refs.proofreadTaskList?.downCallback()
    },
}
</script>

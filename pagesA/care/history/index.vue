<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="我的护理记录" navbarType="2"></f-navbar>
<!-- 			<view :style="{backgroundColor:'#fff'}" class="padding-sm">
				 <u-input prefixIcon="search" placeholder="输入关键字搜索" v-model="pageReq.key" shape='circle' border="surround" clearable>
					<template slot="suffix">
						<u-button v-if='pageReq.key' text="搜索" type="success" size="mini"	 @click="downCallback"></u-button>
					</template>
				  </u-input>
			</view> -->
		</ut-top>
		<mescroll-body
			ref="mescrollRef"
			:top="topWrapHeight+'px'"
			:top-margin="-topWrapHeight+'px'"
			bottom="0"
			:up="upOption"
			:safearea="true"
			@init="mescrollInit"
			@down="downCallback"
			@up="upCallback"
			@emptyclick="emptyClick"
		>

			<template v-for="(item,index) in dataList">	
				<view class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white" @click="jumpInfo(item)">
					<work-item :key="index" :detail="item" :colors="colors"></work-item>
				</view>
			</template>
		</mescroll-body>
		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import WorkItem from './work-item.vue'

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
		WorkItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 10,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			dataList:[],	
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched

	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
		},
		async upCallback(page) {
			this.isShow = false

			this.$ut.api('mang/care/work/listpg', {
				communityId:this.community.id,
				...this.pageReq,
			}).then(({data}) => {		
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	

			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		emptyClick() {

		},
		jumpInfo(item) {
			this.$tools.routerTo('/pagesA/care/history/info', { workId: item.id,customerId:item.customerId })
		},

	},
}
</script>
<style>


</style>
<style lang="scss" scoped>



</style>

<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="护理信息" navbarType="2"></f-navbar>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :show-all="false" :colors="colors" :file-data="fileData" />
		</view>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-content">
			<view>
				<text class="text-gray">计划时间：</text>
				<text>{{workData.workDate}}</text>
				<text class="margin-left">{{workData.begin}}</text>
				<text class="margin-left">-</text>
				<text class="margin-left">{{workData.end}}</text>
			</view>
			<view>
				<text class="text-gray">签到时间：</text>
				<text>{{workData.checkInTime}}</text>
			</view>
			<view>
				<text class="text-gray">签退时间：</text>
				<text>{{workData.checkOutTime}}</text>
			</view>
			
		</view>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-content">
			<view class="text-lg margin-bottom-sm">服务项目</view>
			<template v-for="(item,index) in workData.projects">
				<project-item :key="index" :detail="item" :colors="colors" @select="select" :tip="item.isSubmit?'已提交':''">
				</project-item>
				
			</template>
		</view>
		
		<view class="cu-card margin-sm radius shadow s-gray bg-white padding-lg">
			<view class="flex">
				<text class="text-sm text-title text-gray">现场照片：</text>
				<view>
					<template v-for="(item,index) in workData.checkInPhotos">
						<view>
							<u-lazy-load :image="$tools.showImg(item.url)" width="120" height="120" border-radius="4"
							 @click="$tools.previewImage(getItemImages(workData.checkInPhotos),index,800)"/>
				
						</view>
					</template>
				</view>
			</view>	
			<view class="flex">
				<text class="text-sm text-title text-gray">签到说明：</text>
				<text class="text-sm text-gray">{{workData.checkInRemark}}</text>
			</view>		
		</view>
		
		<view class="cu-card margin-sm radius shadow s-gray bg-white padding-lg">
			<view class="flex">
				<text class="text-sm text-title text-gray">现场照片：</text>
				<view>
					<template v-for="(item,index) in workData.checkOutPhotos">
						<view>
							<u-lazy-load :image="$tools.showImg(item.url)" width="120" height="120" border-radius="4"
							 @click="$tools.previewImage(getItemImages(workData.checkOutPhotos),index,800)"/>
				
						</view>
					</template>
				</view>
			</view>		
			<view class="flex">
				<text class="text-sm text-title text-gray">签退说明：</text>
				<text class="text-sm text-gray">{{workData.checkOutRemark}}</text>
			</view>	
		</view>
	
		
		
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		<view class="padding-bottom-sm"></view>
		
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
import ProjectItem from '@/pagesA/components/project-item.vue'

export default {
	mixins: [], 
	components: {
		CustomerInfo,
		ProjectItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			
			customerId:'',
			workId:'',
			customer:{},
			show:false,
			attendants:[],
			selAttendant:{},
			fileData:[],
			workData:{},
			
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),

	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.customerId) this.customerId = options.customerId
		if (options.workId) this.workId = options.workId
		
		this.getCustomerInfo()
		this.getCustomerFileData()
		this.getWorkAllInfo()
	},
	methods: {
		async getCustomerInfo(){
			if(!this.customerId) return
			const {data} = await this.$ut.api('mang/care/customer/info', {
				communityId:this.community.id,
				module:'long',
				id:this.customerId,
			})
			this.customer=data
		},
		async getCustomerFileData(){
			if(!this.customerId) return
			const {data} = await this.$ut.api('mang/care/customer/fileData/allList', {
				communityId:this.community.id,
				customerId:this.customerId,
			})
			this.fileData=data
		},
		async getWorkAllInfo(){
			if(!this.workId) return
			const {data} = await this.$ut.api('mang/care/work/allInfo', {
				communityId:this.community.id,
				workId:this.workId,
			})
			this.workData=data
		},
		imgPreview(item, index) {
			if(!this.workData.photos || !this.workData.photos.length) return
			var imgData = this.workData.photos.filter(item => !/.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url)) //只预览图片的
			console.log(imgData)
			let arr = []
			imgData.forEach(item => {
				if (!item) return true
				if (item.url.indexOf('oss.') > 0) {
					arr.push(item.url + "&width=750&quality=70")
				} else {
					arr.push(item.url)
				}
			})
			uni.previewImage({
				urls: arr,
				current: index,
				loop: true,
			});
		},
		getItemImages(items){
			return items.map(u=>(u.url));
		},
	},
}
</script>

<style lang="scss" scoped>
.image-item{
	width:160rpx;
	height: 120rpx;
}

.image-box .image-item{
	margin-right: 10rpx;
	&:last-child{
		margin-right: 0;
	}
}
</style>
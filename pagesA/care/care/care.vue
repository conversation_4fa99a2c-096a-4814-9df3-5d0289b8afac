<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="护理客户" navbarType="1"></f-navbar>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :show-all="true" :colors="colors" :file-data="fileData" />
			<view class="flex align-center">护理员：
				<view>
					<text class="text-lg text-bold">{{customer.attendantName}}</text>
				</view>
			</view>
		</view>
		<view v-if="!workInfo || !workInfo.id && loadWork" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-center padding-lg">
			<button class="cu-btn start-btn" @tap="showStart=true">开始服务</button>
		</view>
		
		<view v-if="workInfo && workInfo.id && loadWork" class="cu-card margin-sm radius padding-sm shadow s-gray bg-white text-center padding-lg">
			<button class="cu-btn start-btn" @tap="go">继续</button>
			
			<view class="margin-tb-lg" @tap="cancel">取消服务</view>
			
		</view>
		
		<view class="cu-modal" v-if="showStart"  cathctouchmove @tap.stop="showStart=false">
			<view class="cu-dialog" @tap.stop style="background: none;overflow: visible;">
				<view class="modal-box">
					<image class="head-bg" src="https://oss.afjy.net/api/file/preview?file=eWtn09p.png&width=750" mode="">
					</image>
					<view class="detail">
						<view class="date margin-tb-xl text-bold text-xl">
							<view v-if="!date" @tap="$refs.datePicker.show()">选择时间</view>
							<view v-else @tap="$refs.datePicker.show()" >
								服务时间：{{date}}
							</view>
							
							
						</view>
						<button class="cu-btn start-btn" @tap="$shaken(work)">立即开始</button>
					</view>
					
				</view>
			</view>
		</view>
		
		<u-popup v-if="showDataUpload" :show="showDataUpload" mode="bottom" round="10" :closeable="true"
			:safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
			:overlay-style="{zIndex:998}" @close="showDataUpload=false">
			<view class="padding-tb-xs text-center">护理资料上传</view>
			<care-data-upload :colors="colors" :detail="customer" :fileData="fileData" @save="saveFileData"></care-data-upload>
		</u-popup>
		
		<pick-date ref="datePicker" :start-year="new Date().getFullYear()" :end-year="new Date().getFullYear()"
			:time-init="0"
			:time-hide="[true, true, true, false, false, false]" 
			:time-label="['年', '月', '日', '时', '分', '秒']"
			@submit="dateSelect" />
			
			
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
import PickDate from '@/pagesA/components/pickDate.vue'
import CareDataUpload from '../components/care-data-upload.vue'

export default {
	mixins: [], 
	components: {
		CustomerInfo,
		PickDate,
		CareDataUpload,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick:true,
			planId:'',
			customerId:'',
			customer:{},
			show:false,
			attendants:[],
			selAttendant:{},
			fileData:[],
			
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			showStart:false,
			date:'',
			workId:'',
			workInfo:{},
			loadWork:false,
			
			showDataUpload:false,
			fileData:[],
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.planId) this.planId = options.planId
		
		this.date=this.today()
		this.getCustomerInfo()
		this.getCustomerFileData()
		
		this.getWorkInfo()	
		
	},
	methods: {
		async getCustomerInfo(){
			if(!this.planId) return
			const {data} = await this.$ut.api('mang/care/plan/customer/info', {
				communityId:this.community.id,
				planId:this.planId,
			})
			this.customer=data
		},
		async getCustomerFileData(){
			if(!this.planId) return
			const {data} = await this.$ut.api('mang/care/customer/fileData/allList', {
				communityId:this.community.id,
				planId:this.planId,
			})
			this.fileData=data
		},
		dateSelect(e){
			this.date=`${e.year}-${e.month}-${e.day}`
		},
		today() {
		  const now = new Date();
		  const year = now.getFullYear();
		  const month = (now.getMonth() + 1).toString().padStart(2, '0');
		  const day = now.getDate().toString().padStart(2, '0');
		  
		  this.$refs.datePicker.value=[0,month-1,day-1]
		  return `${year}-${month}-${day}`;
		},
		async getWorkInfo(){
			const {data} = await this.$ut.api('mang/care/work/info', {
				communityId:this.community.id,
				planId:this.planId,
			})
			this.workInfo=data
			this.loadWork=true
			if(data && data.id)	this.workId=data.id
		},
		work(){
			uni.showLoading({
				title:'请稍等...'
			})
			this.$ut.api('mang/care/plan/start', {
				communityId:this.community.id,
				planId:this.planId,
				date:this.date,
			}).then(async (res)=>{
				this.workId=res.data.workId
				await this.getWorkInfo()
				this.showStart=false
				uni.hideLoading()
				this.go(res.data.customerId)
			}).catch(()=>{
				uni.hideLoading()
			})
			
		},
		go(customerId){
			this.$tools.routerTo('/pagesA/care/work/start', {workId:this.workId,customerId:customerId })
		},
		handleCancel(){
			uni.showLoading({
				title:'请稍等...'
			})
			this.$ut.api('mang/care/work/cancel', {
				communityId:this.community.id,
				workId:this.workId,
			}).finally(()=>{
				uni.hideLoading()
			}).then(async (res)=>{
				this.workInfo={}
				this.workId=''
				setTimeout(()=>{
					uni.showToast({
						title:'取消成功'
					})
				},100)
				// this.$tools.back('downCallback()')
			})
		},
		cancel(){
			uni.showModal({
				title: '询问',
				content: `确定取消当前服务吗？`,
				confirmText: '确定',
				success: res => {
					this.handleCancel()
				},
				fail: err => {
					console.log(`%cuni.showModal失败：`, 'color:green;background:yellow');
				}
			})
		},
		async dataUpload(item){
			this.selectItem=item
			uni.showLoading({
				title:'请稍等...'
			})
			const {data} = await this.$ut.api('mang/care/work/dataFile/allList',{
				communityId:this.community.id,
				workId:item.id,
			}).finally(()=>{uni.hideLoading();})
			this.fileData=data
			
			this.showDataUpload=true
		},
		saveFileData(newFileData){
			console.log(newFileData)
			let datas=[]
			newFileData.forEach(item=>{
				item.details.forEach(detail=>{
					let di={dataId:detail.id}
					di.urls=[]
					detail.files.forEach(file=>{
						di.urls.push(file)
					})
					datas.push(di)
				})
				
			})
			uni.showLoading({
				title:'请稍等...'
			})
			this.$ut.api('mang/care/work/dataFile/save',{
				communityId:this.community.id,
				workId:this.selectItem.id,
				datas:datas,
			}).finally(()=>{uni.hideLoading()})
			.then(res=>{
				this.showDataUpload=false
			})
			
		}
	},
}
</script>

<style>
	.cu-modal{
		opacity: 1;
		pointer-events:all;
		z-index: 999;
	}
</style>
<style lang="scss" scoped>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}


.start-btn {
	width: 492rpx;
	height: 70rpx;
	background: linear-gradient(90deg, var(--colors), var(--colors2));
	box-shadow: 0px 7rpx 6rpx 0px var(--colors3);
	border-radius: 35rpx;
	font-size: 28rpx;
	color: rgba(255,255,255, 0.9);
}



.modal-box {
	width: 610rpx;
	border-radius: 20rpx;
	background: #fff;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
	padding-bottom: 30rpx;

	.head-bg {
		width: 100%;
		height: 210rpx;
	}


	.btn-box {
		margin-top: 80rpx;

	}
}

</style>
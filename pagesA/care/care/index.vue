<template>
	<view>
		<f-navbar fontColor="#fff" :bgColor="colors" title="我的护理任务" navbarType="2"></f-navbar>
			<scroll-view  scroll-y="true" class="scroll-box" >
				<view>
					<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white flex justify-between">
						<view class="text-bold text-lg">今日任务</view>
						<view :style="{color:colors}">共{{todayData.length}}人</view>
					</view>
					<template v-for="(item,index) in todayData">	
						<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white" >
							<customer-plan-item :key="index" :detail="item" :colors="colors" @itemClick="select" @dataUpload="dataUpload">
								<template #op>
									<view style="width:100rpx">
										<u-button v-if="!item.checkOutTime" type="primary" shape="circle" :color="colors" text="护理" :plain="false" size="mini" @tap="select(item)"/>
										<u-button v-else  type="primary" shape="circle" :color="colors" text="查看" :plain="false" size="mini" @tap="select(item)"/>
									</view>
								</template>
							</customer-plan-item>
						</view>
					</template>
					
					<view class="margin-top margin-lr-sm radius padding-sm bg-white flex justify-between">
						<view class="text-gray text-lg">明日任务</view>
						<view class="text-gray">共{{tomorrowData.length}}人</view>
					</view>
					<view v-if="!showTomorrow" class="margin-lr-sm padding-sm bg-white text-gray text-center" @click="showTomorrow=true">展开</view>
					<view v-if="showTomorrow">
						<template v-for="(item,index) in tomorrowData">
							<view class="cu-card margin-lr-sm radius padding-lr-sm padding-tb-sm bg-white">
								<customer-plan-item :key="index" :detail="item" :colors="colors">
									<template #other>
										<u-button>护理工作</u-button>
									</template>
								</customer-plan-item>
							</view>
						</template>
					</view>
				</view>
				
				<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
			</scroll-view>
		
		<u-popup v-if="showDataUpload" :show="showDataUpload" mode="bottom" round="10" :closeable="true"
			:safe-area-inset-bottom="true" :mask-close-able="true" close-icon-pos="top-left" :z-index="998"
			:overlay-style="{zIndex:998}" @close="showDataUpload=false">
			<view class="padding-tb-xs text-center">护理资料上传</view>
			<care-data-upload :colors="colors" :detail="selectItem" :is-edit="!selectItem.workIsAudit" :fileData="fileData" @save="saveFileData"></care-data-upload>
			
			<view class="padding-bottom-lg text-center flex justify-center" v-if="selectItem.workIsAudit">
				<view style="width:220rpx;">
					<u-tag text="本次护理已审核"  size="mini"/>
				</view>
			</view>
		</u-popup>
		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</view>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import CustomerPlanItem from '../components/customer-plan-item.vue'
import CareDataUpload from '../components/care-data-upload.vue'

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
		CustomerPlanItem,
		CareDataUpload,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick:true,
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 10,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			showTomorrow:false,
			todayData:[],
			tomorrowData:[],
			
			showDataUpload:false,
			selectItem:{},
			fileData:[],

		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
		this.getScheduling()
	},
	methods: {
		async getScheduling(){
			uni.showLoading({
				title:'请稍等...'
			})
			const {data} = await this.$ut.api('mang/care/customer/listByScheduling',{
				communityId:this.community.id,
			}).finally(()=>{uni.hideLoading();})
			this.todayData=data.today
			this.tomorrowData=data.tomorrow
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
		},
		async upCallback(page) {
			this.isShow = false

			this.$ut.api('mang/care/customer/listByScheduling', {
				communityId:this.community.id,
			}).then(({data}) => {		
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	

			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		emptyClick() {

		},
		select(item) {
			this.$tools.routerTo('/pagesA/care/customer/care', { planId: item.id,customerId:item.customerId })
		},
		async dataUpload(item){
			this.selectItem=item
			uni.showLoading({
				title:'请稍等...'
			})
			const {data} = await this.$ut.api('mang/care/work/dataFile/allList',{
				communityId:this.community.id,
				workId:item.workId,
			}).finally(()=>{uni.hideLoading();})
			this.fileData=data
			
			this.showDataUpload=true
		},
		saveFileData(newFileData){
			//console.log(newFileData)
			let datas=[]
			newFileData.forEach(item=>{
				item.details.forEach(detail=>{
					let di={dataId:detail.id}
					di.urls=[]
					detail.files.forEach(file=>{
						di.urls.push(file)
					})
					datas.push(di)
				})
				
			})
			uni.showLoading({
				title:'请稍等...'
			})
			this.$ut.api('mang/care/work/dataFile/save',{
				communityId:this.community.id,
				workId:this.selectItem.workId,
				datas:datas,
			}).finally(()=>{uni.hideLoading()})
			.then(res=>{
				this.showDataUpload=false
			})
			
		}

	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
.page {
	background-color: #fff;
}



</style>

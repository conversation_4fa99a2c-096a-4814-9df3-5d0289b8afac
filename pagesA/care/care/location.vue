<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="定位我的位置" navbarType="1" />
		</ut-top>
		
		<view class="bg-white padding-tb-sm padding-lr-sm" :style="{minHeight:'calc(100vh - '+topWrapHeight+'px)'}">
			<view class="margin-top-lg padding-lr-sm text-content">
				<view v-if="myLocation.longitude">
	<!-- 				<view v-if="myLocation.address">原地址：{{myLocation.address}}</view>
					<view v-if="myLocation.longitude">
						原坐标：{{myLocation.longitude}},{{myLocation.latitude}}
					</view> -->
					<view v-if="myLocation.longitude" @tap="openMapSource" class="text-gray">点击这里查看原始坐标</view>
				</view>
				<view v-else>未记录有任何定位信息</view>
			</view>
			<view class="margin-tb-lg text-center text-blue">变更为</view>
			<view class="padding-lr-sm">
				<map style="width:100%;height:600rpx" id="myMap" :scale="18" :latitude="location.latitude" :longitude="location.longitude" :markers="markers" :circles="circles" show-location></map>
			</view>
			<view v-if="isGPS" class="margin-top-lg padding-lr-sm text-content">
				<view v-if="addressInfo && addressInfo.address" class="text-content">当前住置：{{addressInfo.address}}</view>
				<view v-if="addressInfo.location.longitude">
					坐标：{{addressInfo.location.longitude}},{{addressInfo.location.latitude}}
				</view>
				<view v-if="addressInfo.location.longitude" class="text-gray" @tap="openMap">地图上显示</view>
			</view>
			<view v-else>未获取当前信息,请稍等。如果长时间未获得信息，请检查定位权限是否开启。</view>
			<view class="text-center">
				<view v-if="loadingLocation==1">正在获取定位</view>
				<view v-else-if="loadingLocation==2">获取定位完成</view>
				<view v-if="loadingLocation==2" @click="loadGPS">重新获取</view>
			</view>
		</view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :disabled="!isGPS" :color="colors" text="保存位置" size="normal"  @click="saveLocation"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>

var app = getApp()
import  {mapMutations, mapActions, mapState}  from 'vuex'
export default {
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight:0,
			addressInfo:{},
			location:{},
			myLocation:{},
			markers:[],
			circles:[],
			gpsCount:0,
			intervalTime:{},
			loadingLocation:0,
		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,			
			locationAddress: state => state.init.locationAddress,
		}),
		// position() {
		// 	return {
		// 		latitude: this.locationAddress.location.latitude,
		// 		longitude: this.locationAddress.location.longitude,
		// 		address: this.locationAddress.info.address,
		// 	}
		// },
		isGPS(){
			if(this.location && this.location.longitude && this.location.latitude) return true
			return false
		},
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
		
		this.getInfo()
		this.loadGPS()
	},
	destroyed() {
		clearInterval(this.intervalTime)
	},
	onLoad: async function (options) {
		await this.$onLaunched
	},
	methods: {
		...mapActions(['setLocationAddress', 'setCity']),
		getHeight(h) {
			this.topWrapHeight = h
		},
		loadGPS(){
			this.gpsCount=0;
			this.getLocation()
			this.loadingLocation=1
			clearInterval(this.intervalTime)
			this.intervalTime = setInterval(() => {
			  this.getLocation();  // 接口方法
			  this.gpsCount++
			  if(this.gpsCount>5){
				  clearInterval(this.intervalTime)
				  this.loadingLocation=2
			  }
			}, 3000);
		},

		async getInfo(){
			const {data} = await this.$ut.api('mang/care/myLocation/info', {
				communityId: this.community.id,
				module: 'long',
			})
			this.myLocation=data
		},
		getLocation() {
			this.$wxsdk.getLocationToAddress().then(location => {
				this.location=location				
				this.circles=[{
						longitude: location.longitude,
						latitude: location.latitude,
						fillColor: "#FF2B431A",
						color: "#FF0000",
						radius: 1,
						strokeWidth: 2
					}]
				
				// this.$ut.api('comm/locationInfo',{
				// 	commKey:this.commKey,
				// 	longitude:location.longitude,
				// 	latitude:location.latitude
				// }).then((res)=>{
				// 	this.addressInfo=res.data
				// 	if (res.data && res.data.city) {
				// 		this.setCity(res.data.city)
				// 	}
				// })
				
			}).catch(err => {
			})
		},
		openMap(){
			this.$wxsdk.openLocation({
				latitude:this.location.latitude,
				longitude:this.location.longitude,
				address:'我的当前位置'
			})
		},
		openMapSource(){
			this.$wxsdk.openLocation({
				latitude: this.myLocation.latitude,
				longitude: this.myLocation.longitude,
				address: this.myLocation.address,
			})
		},
		saveLocation(){
			if (!this.location || !this.location || !this.location.longitude){
				uni.showToast({
					title:'请先打开定位功能'
				})
				return
			}
			this.$ut.api('mang/care/myLocation/save', {
				communityId: this.community.id,
				module: 'long',
				longitude:this.location.longitude,
				latitude:this.location.latitude,
				address: this.addressInfo.address,
			}).then(()=>{
				this.$tools.back('getNurseMenuCount()')
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
			})
		}
	},
}
</script>

<style>
</style>
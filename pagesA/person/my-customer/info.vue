<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="客户信息" navbarType="2"></f-navbar>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :show-all="true" :colors="colors" :file-data="fileData" />
			<view class="flex align-center">护理员：
				<view>
					<text class="text-lg text-bold">{{customer.attendantName}}</text>
				</view>
			</view>
		</view>

	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'

export default {
	mixins: [], 
	components: {
		CustomerInfo,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			
			customerId:'',
			customer:{},
			show:false,
			attendants:[],
			selAttendant:{},
			fileData:[],
			
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),

	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.customerId) {
			this.customerId = options.customerId
		}
		
		this.getCustomerInfo()
		this.getCustomerFileData()
	},
	methods: {
		async getCustomerInfo(){
			if(!this.customerId) return
			const {data} = await this.$ut.api('mang/nurse/customer/info', {
				communityId:this.community.id,
				module:'long',
				id:this.customerId,
			})
			this.customer=data
		},
		async getCustomerFileData(){
			if(!this.customerId) return
			const {data} = await this.$ut.api('mang/nurse/customer/fileData/allList', {
				communityId:this.community.id,
				customerId:this.customerId,
			})
			this.fileData=data
		},
	},
}
</script>

<style>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}
</style>
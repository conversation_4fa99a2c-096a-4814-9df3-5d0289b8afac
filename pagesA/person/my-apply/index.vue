<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="我的申请人员" navbarType="1" />
		</ut-top>
		<mescroll-body
			ref="mescrollRef"
			:top="topWrapHeight+'px'"
			:top-margin="-topWrapHeight+'px'"
			bottom="140"
			:up="upOption"
			:safearea="true"
			@init="mescrollInit"
			@down="downCallback"
			@up="upCallback"
			@emptyclick="emptyClick"
		>
			<u-swipe-action ref="swipeUserList">
			<template v-for="(item,index) in dataList">	
				<u-swipe-action-item
					:key="index"
					:name="item.id"
					:options="getActionOption(item)"
					@longpress="longpress(item)"
					@click="actionOp">
					<customer-item  :detail="item" :colors="colors" @select="select" :tip="item.isSubmit?'已提交':''">
						<template #op>
							<u-button type="primary" shape="circle" :color="colors" :text="item.isSubmit?'查看':'选择'" :plain="false" size="small" @tap="select(item)"/>
						</template>
					</customer-item>
				</u-swipe-action-item>
				
			</template>
			</u-swipe-action>
		</mescroll-body>
		
		<ut-fixed safe-area-inset position="bottom">
			<view class="margin">
				<u-button type="primary" shape="circle" :color="colors" text="新增申请" size="normal"  @click="goEdit"></u-button>
			</view>
		</ut-fixed>
		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import CustomerItem from '@/pagesA/components/customer-item.vue'

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
		CustomerItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			dataList:[],

		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched

	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
			
			this.$refs['swipeUserList'].closeAll()
		},
		async upCallback(page) {
			this.$ut.api('mang/person/listpg', {
				communityId:this.community.id,
				...this.pageReq,
			}).then(({data}) => {		
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	
			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		emptyClick() {

		},
		getActionOption(item) {
			const btnSubmitCancel = {
				text: '取消提交',
				code:'submitCancel',
				style: {
					backgroundColor: '#ffaa7f'
				}
			}
							
			const btnDelete = {
				text: '删除申请',
				code:'delete',
				style: {
					backgroundColor: '#f56c6c'
				}
			}

			let data = []
	
			if (item.isSubmit) {
				data.push(btnSubmitCancel)
			} else {
				data.push(btnDelete)
			}
			
			return data
		},
		longpress(item) {
			console.log(item)
		},
		select(item) {
			this.$tools.routerTo('/pagesA/person/my-apply/info', { personId: item.id })
		},
		goEdit(){
			this.$tools.routerTo('/pagesA/person/my-apply/save', { personId: ''})
		},
		deletePerson(id){
			return new Promise((resolve, reject) => {
				this.$ut.api('mang/person/delete', {
					communityId:this.community.id,
					ids:[id],
				}).then(res=>{
					resolve(res)
				}).catch(err=>{
					reject(err)
				})
			})
		},
		submitCancel(id){
			return new Promise((resolve, reject) => {
				this.$ut.api('mang/person/submitCancel', {
					communityId:this.community.id,
					id:id,
				}).then(res=>{
					resolve(res)
				}).catch(err=>{
					reject(err)
				})
			})
		},
		actionOp(data) {
			let that = this
			let content = ''
			if (data.code == 'delete') content = '确认删除该成员吗？'
			uni.showModal({
				title: '操作提示',
				content: content,
				success: res=> {
					if (res.confirm) {
						if (data.code == 'delete') {
							this.deletePerson(data.name).then(()=>{
								let objIndex = this.dataList.findIndex(u => u.id == data.name)
								this.dataList.splice(objIndex, 1)
							})							
						}else if(data.code=='submitCancel'){
							this.submitCancel(data.name).then(()=>{
								let obj = this.dataList.find(u => u.id == data.name)
								if(obj) this.$set(obj,'isSubmit',false)
							})
						}
					} else if (res.cancel) {
					}
					this.$refs['swipeUserList'].closeAll()
				}
			})
		},
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
.page {
	background-color: #fff;
}

</style>

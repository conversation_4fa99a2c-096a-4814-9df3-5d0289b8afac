<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="申请" navbarType="1" />
		</ut-top>
		<view class="padding-lr bg-white">
			<u--form ref="form" labelPostion="left" :model="form" :rules="rules" labelWidth="auto" :labelStyle="{color:'#a0a0a0'}">
				<u-form-item label="姓名" prop="name" borderBottom>
					<u--input v-model="form.name" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="性别" prop="sex" borderBottom @click="showSex=true">
					<u--input v-model="sexName" border="none" disabled disabledColor="#ffffff" placeholder="请选择性别" inputAlign='center' />
					<u-icon slot="right" name="arrow-right" />
				</u-form-item>
				<u-form-item label="电话" prop="phone" borderBottom>
					<u--input v-model="form.phone" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="证件号" prop="idcard" borderBottom>
					<u--input v-model="form.idcard" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="出生日期" prop="birthday" borderBottom  @click="$refs.birthdayPicker.show()">
					<u--input v-model="form.birthday" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item>
				<u-form-item label="民族" prop="nation" borderBottom>
					<u--input v-model="form.nation" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="学历" prop="education" borderBottom  @click="educationShow=true">
					<u--input v-model="form.education" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item>
				<u-form-item label="婚姻" prop="marriage" borderBottom  @click="marriageShow=true">
					<u--input v-model="form.marriage" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item>
				<u-form-item label="居住状态" prop="liveState" borderBottom  @click="liveStateShow=true">
					<u--input v-model="form.liveState" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item>
<!-- 				<u-form-item label="失能等级" prop="disabilityLevel" borderBottom  @click="disabilityLevelShow=true">
					<u--input v-model="form.disabilityLevel" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item> -->
				<u-form-item label="政治面貌" prop="politics" borderBottom  @click="politicsShow=true">
					<u--input v-model="form.politics" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item>
				<u-form-item label="方言" prop="dialect" borderBottom>
					<u--input v-model="form.dialect" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="宗教信仰" prop="religion" borderBottom   @click="religionShow=true">
					<u--input v-model="form.religion" border="none" disabled disabledColor="#ffffff" inputAlign='center' />
					<u-icon slot="right" name="arrow-right"/>
				</u-form-item>
				<u-form-item label="地址" prop="address" borderBottom>
					<!-- #ifdef MP -->
					<u-input v-model="form.address" border="none" inputAlign='left' >
						<template #suffix>
							<view @click="chooseLocation"><u-icon name="map" :color="colors" size="38rpx" /></view>
						</template>
					</u-input>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<u--input v-model="form.address" border="none" inputAlign='left' >
						<template #suffix>
							<view @click="chooseLocation"><u-icon name="map" :color="colors" size="38rpx" /></view>
						</template>
					</u--input>
					<!-- #endif -->
				</u-form-item>
				<u-form-item v-if="form.longitude && form.latitude" label="位置坐标" borderBottom>
					<text>{{form.longitude}},{{form.latitude}}</text>
				</u-form-item>
				<u-form-item label="备注" prop="remark" borderBottom>
					<u--textarea v-model="form.remark" confirm-type="done"></u--textarea>
				</u-form-item>
			</u--form>
		</view>
		<view class="padding-lr padding-tb bg-white">
			<u-button type="primary" shape="circle" :color="colors" text="保存申请" :plain="false" size="normal" @click="save"/>
		</view>
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		<u-action-sheet :show="showSex" :actions="actions" title="请选择性别" @close="showSex = false" @select="sexSelect"></u-action-sheet>
		<pick-date ref="birthdayPicker" :start-year="1901" :end-year="new Date().getFullYear()" 
			:time-init="0"
			:time-hide="[true, true, true, false, false, false]" 
			:time-label="['年', '月', '日', '时', '分', '秒']"
			@submit="birthdaySelect" />
		<u-picker :show="educationShow" :columns="educationColumns" @cancel="educationShow = false" @confirm="educationSelect"></u-picker>
		<u-picker :show="marriageShow" :columns="marriageColumns" @cancel="marriageShow = false" @confirm="marriageSelect"></u-picker>
		<u-picker :show="liveStateShow" :columns="liveStateColumns" @cancel="liveStateShow = false" @confirm="liveStateSelect"></u-picker>
		<u-picker :show="politicsShow" :columns="politicsColumns" @cancel="politicsShow = false" @confirm="politicsSelect"></u-picker>
		<u-picker :show="religionShow" :columns="religionColumns" @cancel="religionShow = false" @confirm="religionSelect"></u-picker>
	</ut-page>
	

</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import PickDate from '@/pagesA/components/pickDate.vue'

export default {
	components: {
		PickDate,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight: 0,

			form:{
				birthday:'',
			},
			rules:{
				
			},
			showSex:false,
			sexName:'',
			actions: [{
					id:1,
					name: '男',
				},{	
					id:2,
					name: '女',
				},{
					id:'',
					name: '保密',
				},
			],
			
			educationShow:false,
			educationColumns:[['不详','无学历','小学','初中','中专','大专','本科','硕士','博士']],
			
			marriageShow:false,
			marriageColumns:[['未知','已婚','离异','丧偶']],
			
			liveStateShow:false,
			liveStateColumns:[['未知','独居','与配偶/伴侣居住','与子女居住','与父母居住','与兄弟姐妹居住','与其他亲属居住','与非亲属关系居住']],
			
			// disabilityLevelShow:false,
			// disabilityLevelColumns:[['未知','独居','与配偶/伴侣居住','与子女居住','与父母居住','与兄弟姐妹居住','与其他亲属居住','与非亲属关系居住']],
			
			politicsShow:false,
			politicsColumns:[['未知','群众','共青团员','中共党员']],
			
			religionShow:false,
			religionColumns:[['未知','佛教','道教','天主教','基督教','伊斯兰教','其他']],
		}
	},

	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	onReady() {
			//onReady 为uni-app支持的生命周期之一
	    	this.$refs.form.setRules(this.rules)
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),

	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.personId) {
			this.personId = options.personId
			this.getPersonInfo()
		}
		
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		endYear(){
			return new Date().getFullYear()
		},

		sexSelect(e) {
			this.form.sex = e.id
			this.sexName = e.name
			this.$refs.form.validateField('sex')
		},

		birthdaySelect(e){
			this.form.birthday=`${e.year}-${e.month}-${e.day}`
		},
		educationSelect(e){
			this.educationShow=false
			this.form.education=e.value[0]
		},
		marriageSelect(e){
			this.marriageShow=false
			this.form.marriage=e.value[0]
		},
		liveStateSelect(e){
			this.liveStateShow=false
			this.form.liveState=e.value[0]
		},
		politicsSelect(e){
			this.politicsShow=false
			this.form.politics=e.value[0]
		},
		religionSelect(e){
			this.religionShow=false
			this.form.religion=e.value[0]
		},
		chooseLocation(){
			let that=this
			uni.chooseLocation({
				success: (res) => {
					this.$set(this.form,'address',res.address)
					this.$set(this.form,'latitude',res.latitude)
					this.$set(this.form,'longitude',res.longitude)
				}
			})
		},
		async getPersonInfo(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/info', {
				communityId:this.community.id,
				id:this.personId,
			})
			this.form=data
			if(this.form.sex){
				const obj=this.actions.find(u=>u.id==this.form.sex)
				if(obj)this.sexName=obj.name
			}

		},
		save(){
			this.$ut.api('mang/person/save', {
				communityId:this.community.id,
				...this.form,
			}).then(()=>{
				if(this.personId){
					this.$tools.back('getPersonInfo()')
				}else{
					this.$tools.back('downCallback()')
				}
				
				
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
				
			})
		}
	},
}
</script>


<style lang="scss" scoped>


</style>

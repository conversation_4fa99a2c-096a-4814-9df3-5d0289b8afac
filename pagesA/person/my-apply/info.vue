<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="申请人详情" navbarType="2"></f-navbar>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<person-info :detail="person" :show-all="true" :file-data="fileData" :colors="colors" @info="showFileData=true" />
		</view>
		
		<view class="cu-card margin-sm radius shadow s-gray bg-white">
			<ut-grid  v-if="!person.isSubmit"  :colors="colors" :list="menus" :count="menus.length>4?4:menus.length" :cell-color="true" :jump-param="jumpParam"></ut-grid>
			
			<view v-else class="padding-sm ">数据已经于{{person.submitTime}}提交，请耐心等待审核</view>
		</view>
		
		
		
		<view class="padding-tb-xl margin-bottom-lg"></view>
		
		<ut-fixed v-if="!person.isSubmit" safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :color="colors" text="提交数据" size="normal"  @click="goSubmit"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import PersonInfo from '@/pagesA/components/person-info.vue'

export default {
	mixins: [], 
	components: {
		PersonInfo,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			
			menus:[
				{
					'name': '人员编辑',
					'url': '/pagesA/person/my-apply/save',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K3xVj',
					'code':'myPerson',
					'num': 0,
				},{
					'name': '资料修改',
					'url': '/pagesA/person/my-apply/file-data',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K7iQQ',
					'code':'personSubmit',
					'num': 0,
				},{
					'name': '编辑头像',
					'url': '/pagesA/person/my-apply/img-head',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',
					'code':'personDistribution',
					'num': 0,
				},
			],
			
			personId:'',
			person:{},

			fileData:[],

		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
		jumpParam(){
			return {personId:this.personId}
		}

	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.personId) {
			this.personId = options.personId
		}
		
		this.getPersonInfo()
		this.getPersonFileData()
	},
	methods: {
		async getPersonInfo(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/info', {
				communityId:this.community.id,
				id:this.personId,
			})
			this.person=data
		},
		async getPersonFileData(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/fileData/allList', {
				communityId:this.community.id,
				personId:this.personId,
			})
			this.fileData=data
		},
		goSubmit(){
			if(!this.personId) return
			this.$ut.api('mang/person/submit', {
				communityId:this.community.id,
				id:this.personId,
			}).then(()=>{
				this.$tools.back('downCallback()')
				setTimeout(()=>{
					uni.showToast({
						title:'数据提交成功'
					})
				},100)
				
			})
		}
	},
}
</script>

<style>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}


</style>
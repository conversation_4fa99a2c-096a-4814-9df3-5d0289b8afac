<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="申请人头像" navbarType="2"></f-navbar>
		<view class="padding-lg flex justify-center">
			<diy-person-upload
				v-model="myPhotos"
				mediaType="image"
				:disabled="false"
				:colors="colors"
				:max="1"
				:width="500"
				:height="600"
				:preview-image-width="1200"
				:border-radius="8"
				:headers="headers"
				:action="uploadInfo.server+uploadInfo.single"
				@uploadSuccess="uploadFaceSuccess"
				@imgDelete="imgDelete"
			 />
		 </view>
<!-- 						<ut-image-upload
			name="file"
			v-model="myPhotos"
			mediaType="image"
			:disabled="!edit.editFace"
			:colors="colors"
			:max="1"
			:headers="headers"
			:action="uploadInfo.server+uploadInfo.single"
			:preview-image-width="1200"
			:width="280"
			:height="360"
			:border-radius="8"
			@uploadSuccess="uploadFaceSuccess"
			@imgDelete="imgDelete">
		</ut-image-upload> -->
		<view class="padding-lr padding-tb">
			<u-button type="primary" shape="circle" :color="colors" text="保存头像" :plain="false" size="normal" @click="save"/>
		</view>
	</ut-page>
</template>

<script>

var app = getApp()
import { mapState } from 'vuex'
import DiyPersonUpload from '@/pagesA/components/ut-person-upload/up-person-upload.vue'
export default {
	components: {
		DiyPersonUpload,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			myPhotos: '',
			form: {
				imgHead: '',
			},

		}
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers() {
			return {
				Token: this.uploadInfo.token,
			}
		},
	},
	onShow() {
		this.setData({
			colors: app.globalData.newColor,
		})
	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.personId) {
			this.personId = options.personId
		}
		
		this.getPersonInfo()
	},
	methods: {
		uploadFaceSuccess(res) {
			const item=res.data
			this.$set(this.form, 'imgHead', this.uploadInfo.preview + '?file=' + item.name + item.ext)
		},
		imgDelete(e) {
			this.form.imgHead = ''
		},
		async getPersonInfo(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/info', {
				communityId:this.community.id,
				id:this.personId,
			})
			this.form=data
			if (this.form.imgHead) {
				this.form.imgHead = data.imgHead + '&token=' + this.uploadInfo.token
				this.myPhotos = this.form.imgHead
			}
		},
		save(){
			this.$ut.api('mang/person/savePhoto', {
				communityId:this.community.id,
				id:this.personId,
				imgHead:this.form.imgHead,
			}).then(()=>{
				this.$tools.back('getPersonInfo()')
				
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
				
			})
		}
	},
}
</script>

<style>
	.page {
		background-color: #fff;
		height: 100vh;
	}
	
</style>
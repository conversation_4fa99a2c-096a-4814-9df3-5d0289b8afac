<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="申请人审核" navbarType="2"></f-navbar>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<person-info :detail="person" :show-all="true" :colors="colors" :file-data="fileData" />
		</view>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			提交时间：{{person.submitTime}}
			<view class="padding-top" style="width:200rpx">
				<u-button type="primary" shape="circle" :color="colors" text="退回到提交状态" :plain="true" size="small"  @click="backSubmitHandle"></u-button>
			</view>
			
		</view>
		
		<u-popup :show="showAuditWrapper" mode="top" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="999" :overlay-style="{zIndex:999}" @close="showAuditWrapper=false">
			<view class="pop-title">审核情况</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<view style="min-height: 400rpx" class="flex justify-center align-center flex-direction">
					<view class="flex-sub full-width padding-xs">
						<view>
							<view class="gray">医保审核：</view>
							<u-radio-group v-model="form.isPass" :iconSize="40">
								<u-radio shape="circle" label="通过" :name="true"></u-radio>
								<u-radio shape="circle" label="不通过" :name="false"></u-radio>
							</u-radio-group>
						</view>
						<view v-show="form.isPass">
							<view class="gray">社保通过时间：</view>
				
							  <u-button @click="handlePass">
								  <text v-if="passDateValue">{{passDateValue}}</text>
								  <text v-else>选择社保通过时间</text>
							  </u-button>
						</view>
						<view v-show="form.isPass">
							<view class="gray">备注：</view>
							<u--textarea v-model="form.govAuditRemark" confirm-type="done" ></u--textarea>
						</view>
						<view v-show="form.isPass==false">
							<view class="gray">不通过原因：</view>
							<u--textarea v-model="form.noPassReason" confirm-type="done" ></u--textarea>
						</view>
						<view v-show="form.isPass==false">
							<view class="gray">下次提醒时间：</view> 
							<u-button @click="handleNoPass">
								  <text v-if="noPassDateValue">{{noPassDateValue}}</text>
								  <text v-else>选择下次提醒时间</text>
							</u-button>
						</view>
					</view>
					<view class="padding-xs full-width">
						<u-button type="primary" shape="circle" :color="colors" 
						:disabled="form.isPass==null || (form.isPass && !form.govAuditTime) || (form.isPass==false && (!form.noPassReason || !form.nextGovAuditTime))" 
						text="确定" size="normal"  @click="handleAudit"></u-button>
					</view>
				</view>
			</scroll-view>
		</u-popup>
		
		<u-datetime-picker
				ref="datetimePicker"
				:show="showTime"
				v-model="timeValue"
				:close-on-click-overlay="true"
				mode="date"
				:formatter="formatter"
				@confirm="dateSure"
				@close="showTime=false"
				@cancel="showTime=false"
		></u-datetime-picker>
		
		<view class="padding-tb-xl margin-bottom-lg"></view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :color="colors" text="数据通过审核" size="normal"  @click="goAudit"></u-button>
			</view>
		</ut-fixed>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import PersonInfo from '@/pagesA/components/person-info.vue'

export default {
	mixins: [], 
	components: {
		PersonInfo,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',			
			
			personId:'',
			person:{},
			fileData:[],
			showAuditWrapper:false,
			form: {
				isPass:null,
				noPassReason:'',
				govAuditTime: '',
				govAuditRemark:'',
				nextGovAuditTime:'',
			},
			timeType:'',
			showTime:false,			
			timeValue: Number(new Date()),
			
			passDateValue: '',
			noPassDateValue:'',
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
		jumpParam(){
			return {personId:this.personId}
		}

	},
	onReady() {
		// 微信小程序需要用此写法
		this.$refs.datetimePicker.setFormatter(this.formatter)
	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.personId) {
			this.personId = options.personId
		}
		
		this.getPersonInfo()
		this.getPersonFileData()
	},
	methods: {
		async getPersonInfo(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/info', {
				communityId:this.community.id,
				id:this.personId,
			})
			this.person=data
		},
		async getPersonFileData(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/fileData/allList', {
				communityId:this.community.id,
				personId:this.personId,
			})
			this.fileData=data
		},
		dateSure(e){
			const date = new Date(e.value)
			const year = date.getFullYear()
			const month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
			const day = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())
			switch(this.timeType){
				case 'govPass':
					this.passDateValue = `${year}年${month}月${day}日`
					this.form.govAuditTime= `${year}-${month}-${day}`
					break
				case 'govNoPass':
					this.noPassDateValue = `${year}年${month}月${day}日`
					this.form.nextGovAuditTime= `${year}-${month}-${day}`
					break
			}
			this.showTime = false
		},
		formatter(type, value) {
			if (type === 'year') {
				return `${value}年`
			}
			if (type === 'month') {
				return `${value}月`
			}
			if (type === 'day') {
				return `${value}日`
			}
			return value
		},
		goAudit(){
			if(!this.personId) return
			this.showAuditWrapper=true
			
		},
		backSubmit(){
			if(!this.personId) return
			this.$ut.api('mang/person/submitCancel', {
				communityId:this.community.id,
				id:this.personId,
			}).then(()=>{
				this.$tools.back('downCallback()')
				
				setTimeout(()=>{
					uni.showToast({
						title:'回退成功'
					})
				},100)
				
			})
		},
		backSubmitHandle(){
			uni.showModal({
				title: '操作提示',
				content: '确认退回到提交状态吗',
				success: res=> {
					if (res.confirm) {
						this.backSubmit()
					}
				}
			})
		},
		handlePass(){
			this.timeType='govPass'
			this.showTime = true
		},
		handleNoPass(){
			this.timeType='govNoPass'
			this.showTime=true
		},
		handleAudit(){
			uni.showModal({
				title: '操作提示',
				content: '确认审核吗？',
				success: res=> {
					if (res.confirm) {
						this.$ut.api('mang/person/audit', {
							communityId:this.community.id,
							...this.form,
							id:this.personId,
						}).then(()=>{
							this.showAuditWrapper=false
							this.$tools.back('downCallback()')
							
							setTimeout(()=>{
								uni.showToast({
									title:'审核成功'
								})
							},100)
							
						})
					}
				}
			})
		},
	},
}
</script>

<style lang="scss">
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}
.audit-box{
	display: flex;
}

:deep(.u-radio-group) {
	width:100%;
	.u-radio{
		padding: 60rpx;
	}
	
	.u-radio__icon-wrap--circle{
		width:50rpx !important;
		height:50rpx !important;
	}
	
	.u-radio__text{
		font-size: 36rpx !important;
	}
}
</style>

<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="申请人信息" navbarType="2"></f-navbar>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<person-info :detail="person" :show-all="true" :colors="colors" :file-data="fileData" />
		</view>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<view>提交时间：{{person.submitTime}}</view>
			<view>公司审核时间：{{person.auditTime}}</view>
			<view class="padding-top" style="width:200rpx">
				<u-button type="primary" shape="circle" :color="colors" text="退回到审核状态" :plain="true" size="small"  @click="backAuditHandle"></u-button>
			</view>
		</view>
		
		<view v-if="person.isPass" class="cu-card margin-sm radius shadow s-gray bg-white">
			<ut-grid :colors="colors" :list="menus" :count="menus.length>4?4:menus.length" :cell-color="true" :jump-param="jumpParam"></ut-grid>
		</view>
		
<!-- 		<u-popup :show="showAuditWrapper" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="998" :overlay-style="{zIndex:998}" @close="showAuditWrapper=false">
			<view class="pop-title">审核情况</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<view style="max-height: 80vh;">
					ssss
				</view>
			</scroll-view>
		</u-popup> -->
		
		<view class="padding-tb-xl margin-bottom-lg"></view>
		
<!-- 		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :color="colors" text="医保过审并生成客户数据" size="normal"  @click="govAudit"></u-button>
			</view>
		</ut-fixed> -->
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import PersonInfo from '@/pagesA/components/person-info.vue'

export default {
	mixins: [], 
	components: {
		PersonInfo,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			
			menus:[
				{
					'name': '医保资料',
					'url': '/pagesA/person/audit/file-data',
					'image': 'https://oss.afjy.net/api/file/preview?file=e0K3xVj',
					'code':'myPerson',
					'num': 0,
				},
			],
			
			personId:'',
			person:{},			
			fileData:[],
			showAuditWrapper:false,
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
		jumpParam(){
			return {personId:this.personId}
		}

	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.personId) {
			this.personId = options.personId
		}
		
		this.getPersonInfo()
		this.getPersonFileData()
	},
	methods: {
		async getPersonInfo(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/info', {
				communityId:this.community.id,
				id:this.personId,
			})
			this.person=data
		},
		async getPersonFileData(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/fileData/allList', {
				communityId:this.community.id,
				personId:this.personId,
			})
			this.fileData=data
		},
		govAudit(){
			if(!this.personId) return
			this.showAuditWrapper=true
			// uni.showModal({
			// 	title: '操作提示',
			// 	content: '确认医保已经通过审核？',
			// 	success: res=> {
			// 		if (res.confirm) {
			// 			this.$ut.api('mang/person/gov/audit', {
			// 				communityId:this.community.id,
			// 				id:this.personId,
			// 			}).then(()=>{
			// 				this.$tools.back('downCallback()')
							
			// 				setTimeout(()=>{
			// 					uni.showToast({
			// 						title:'审核成功'
			// 					})
			// 				},100)
							
			// 			})
			// 		}
			// 	}
			// })
		},
		backAudit(){
			if(!this.personId) return
			this.$ut.api('mang/person/auditCancel', {
				communityId:this.community.id,
				id:this.personId,
			}).then(()=>{
				this.$tools.back('downCallback()')
				
				setTimeout(()=>{
					uni.showToast({
						title:'回退成功'
					})
				},100)
				
			})
		},
		backAuditHandle(){
			uni.showModal({
				title: '操作提示',
				content: '确认退回到审核状态吗',
				success: res=> {
					if (res.confirm) {
						this.backAudit()
					}
				}
			})
		},
		
	},
}
</script>

<style>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}
</style>
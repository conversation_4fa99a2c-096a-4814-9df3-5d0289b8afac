<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="指派护理员" navbarType="2"></f-navbar>
		
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white">
			<customer-info :detail="customer" :colors="colors" :file-data="fileData" />
		</view>
		<view class="cu-card margin-sm radius padding-sm shadow s-gray bg-white"  @click="show=true">
			<text v-if="!selAttendant.id">点击这里选择护理分组</text>
			<view v-else class="flex justify-between">护理分组：
				<view>
					<text class="text-lg text-bold">{{selAttendant.name}}</text>
				</view>
				<view>
					<text class="cuIcon-right" />
				</view>
			</view>
			
		</view>
		
		<u-popup :show="show" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" height="400" close-icon-pos="top-left" @close="show=false">
			<view class="pop-title">选择护理分组</view>
			<view class="clear" @click="selectAttendant({})">清除</view>
			<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
				<template v-for="(item,index) in attendants">
					<attendant-item :key="index" :colors="colors" :detail="item" @select="selectAttendant" />
				</template>
			</scroll-view>
		</u-popup>
		
		<view class="padding-tb-xl margin-bottom-lg"></view>
		
		<ut-fixed safe-area-inset position="bottom" background="#fff">
			<view class="padding-tb-sm padding-lr-lg">
				<u-button type="primary" shape="circle" :disabled="!selAttendant.id" :color="colors" :text="!selAttendant.id?'请选择护理员':'确定指派'" size="normal" @click="showConfirmHandle"></u-button>
			</view>
		</ut-fixed>
		
		
		<u-modal :show="showConfirm" :showConfirmButton="true" :showCancelButton="true" :buttonReverse="true" @confirm="$shaken(save)" @cancel="showConfirm=false">
			确认把该顾客指派到 {{selAttendant.name}} 护理分组吗？
		</u-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import CustomerInfo from '@/pagesA/components/customer-info.vue'
import AttendantItem from '@/pagesA/components/attendant-item.vue'

export default {
	mixins: [], 
	components: {
		CustomerInfo,
		AttendantItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick:true,
			showConfirm:false,
			
			customer:{},
			show:false,
			attendants:[],
			selAttendant:{},
			
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			
			personId:'',
			fileData:[],
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),

	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		if (options.personId) {
			this.personId = options.personId
		}
		
		this.getCustomerInfo()
		this.getGroupAttendant()
		this.getPersonFileData()
	},
	methods: {
		async getGroupAttendant(){
			const {data} = await this.$ut.api('mang/person/gov/groupListpg', {
				communityId:this.community.id,
				module:'long',
				...this.pageReq,
			})
			this.attendants=data.info||[]
		},
		selectAttendant(item){
			this.selAttendant=item
			this.show=false
		},
		async getCustomerInfo(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/gov/customerInfo', {
				communityId:this.community.id,
				id:this.personId,
			})
			this.customer=data || {}
		},
		async getPersonFileData(){
			if(!this.personId) return
			const {data} = await this.$ut.api('mang/person/fileData/allList', {
				communityId:this.community.id,
				personId:this.personId,
			})
			this.fileData=data
		},
		showConfirmHandle(){
			this.noClick=true
			this.showConfirm = true
		},
		save(){
			this.showConfirm = false
			if(!this.personId || !this.selAttendant.id) return
			this.$ut.api('mang/person/gov/distributionGroup', {
				communityId:this.community.id,
				personId:this.personId,
				groupId:this.selAttendant.id,
			}).then(()=>{
				
				this.$tools.back('downCallback()')
			})
			// this.$tools.routerTo('/pagesA/nurse/set-attendant/index')
		}
		

	},
}
</script>

<style>
.card-box {
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx 20rpx 30rpx;
	background-size: 100% 100%;
	border-radius: 10rpx;
	background-color: #fff;
	overflow: hidden;
	bottom: 15rpx;
}

.scroll-box {
	padding-top: 60rpx;
	padding-bottom: 10rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	min-height: 30%;
	max-height: 80%;
}


.pop-title {
	position: absolute;
	left: 0;
	right: 0;
	padding: 15rpx;
	margin: auto;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}

.clear{
	position: absolute;
	right: 0;
	padding: 15rpx 30rpx 15rpx 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	text-align: center;
}
</style>
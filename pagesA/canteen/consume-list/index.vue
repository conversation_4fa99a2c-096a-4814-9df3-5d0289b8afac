<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="消费记录" navbarType="1" />
		</ut-top>
		<mescroll-body
			ref="mescrollRef"
			:top="topWrapHeight+'px'"
			:top-margin="-topWrapHeight+'px'"
			bottom="140"
			:up="upOption"
			:safearea="true"
			@init="mescrollInit"
			@down="downCallback"
			@up="upCallback"
			@emptyclick="emptyClick"
		>

			<template v-for="(item,index) in dataList">	
				<view :key="index" class="margin-bottom-sm">
					<view class="bg-white padding-lr-sm flex padding-tb-sm text-content">
						<view class="img-head margin-right-xs">
							<u-lazy-load  v-if="item && item.imgHead" :image="$tools.showImg(item.imgHead,200)" height="130" width="130" border-radius="75"/>
							<u-lazy-load  v-else :src="require('@/pages/images/logo.png')"></u-lazy-load>

						</view>
						<view class="flex-sub">
							<view class="flex justify-between">
								<view>{{item.name}}</view>
								<view class="text-sm text-gray">{{item.canteenName}}</view>
							</view>		
												
							<view class="flex justify-between ">
								<view class="text-sm margin-right-xs">
									{{item.productName}}
									<text class="margin-left-lg text-lg" :style="{color:colors}">¥{{item.trueMoney}}</text>
								</view>
								<view class="text-sm margin-right-xs" v-if="item.isPack">打包{{item.packMoney}}</view>
								<view class="text-sm" v-if="item.isSend">外卖{{item.sendMoney}}</view>
							</view>	
							<view class="flex justify-between text-gray">
								<view class="text-sm">
									<text class="margin-right-xs">电话</text>
									{{item.phone}}
								</view>
								<view class="text-sm">{{item.createTime}}</view>
							</view>
							<view class="flex text-gray justify-between">
								<view class="flex align-center">
									<view class="text-sm margin-right-xs">卡号</view>
									<view class="text-sm">{{item.cardNo}}</view>
								</view>
								<view>
									{{item.windowName}}
								</view>
							</view>	
							
							<view class="flex text-gray justify-between">
								<view class="flex align-center">
									<view class="text-sm margin-right-xs">出生</view>
									<view class="text-sm">{{item.birthday}}</view>
								</view>
								<view>
									<text class="text-lg" :style="{color:colors}">{{item.age}}</text>
									<text class="text-sm margin-left-xs">岁</text>
								</view>
							</view>	
						</view>
					</view>
				</view>
<!-- 				<customer-item :key="index" :detail="item" :colors="colors" :tip="item.isPass?'通过':''" @select="select">
					<template #other>
						<view v-if="item.auditTime">
							<text class="gray text-xs">审核时间:</text>
							<text class="text-xs">{{item.auditTime}}</text>
						</view>
						<view v-if="item.nextGovAuditTime">
							<text class="gray text-xs">下次提醒时间:</text>
							<text class="text-xs">{{item.nextGovAuditTime}}</text>
						</view>
					</template>
				</customer-item> -->
			</template>
		</mescroll-body>
		

		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import CustomerItem from '@/pagesA/components/customer-item.vue'

export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
		CustomerItem,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			dataList:[],

		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched

	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
		},
		async upCallback(page) {
			this.$ut.api('canteen/consumer/listpg', {
				communityId:this.community.id,
				...this.pageReq,
			}).then(({data}) => {		
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	
			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		emptyClick() {

		},
		select(item) {
			// this.$tools.routerTo('/pagesA/person/audit/info', { personId: item.id })
		},
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
	.img-head image {
		flex-shrink: 0;
		width: 100upx;
		height: 100upx;
		border: 5upx solid #fff;
		border-radius: 50%;
	}

</style>

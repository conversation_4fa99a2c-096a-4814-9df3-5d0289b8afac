<template>
	<view>
		<view class="flex justify-center align-center flex-direction">
			<view class="margin-bottom-xl">
				<view v-if="!cardKey || !nfc || !nfc.adapter" class="text-center padding-tb-xl">
					<image src="https://oss.afjy.net/api/file/preview?file=gA55xX2" style="width:300rpx;height:300rpx"></image>
					<view class="margin-tb text-bold">请打开NFC</view>
					
				</view>
				<view v-else>
					<image src="https://oss.afjy.net/api/file/preview?file=gA4RbTq" style="width:300rpx;height:300rpx"></image>
					<view class="margin-tb">请取出新卡给用户开卡</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import NFC from '@/pagesA/nfc/nfc.js'
import CryptoJS from '@/pagesA/nfc/crypto-js.min'
import slider from '../../../components/uview-ui/libs/config/props/slider'

export default {
	mixins: [], // 使用mixin
	components: {
	},
	props:{
		customer:{
			type:Object,
			default:()=>{}
		}
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			cardKey:'',
			nfc:null,
			err:'',
		}
	},
	mounted() {
		this.setData({ colors: app.globalData.newColor })
		
		this.getKey()
		
		if(app.globalData.nfc==null){
			app.globalData.nfc=new NFC()
		}
		app.globalData.nfc.on('connected',this.cardOp)		
		app.globalData.nfc.on('error',(err)=>{
			console.log(err)
		});
		this.nfc=app.globalData.nfc
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
	},
	destroyed() {
		if(app.globalData.nfc!=null){
			app.globalData.nfc.off('connected')
			app.globalData.nfc.off('error')
		}
	},
	methods: {
		async getKey(){
			const {data} = await this.$ut.api('canteen/info/key',{
				communityId:this.community.id
			})
			const pwd='FFFFFFFFFFFFFFFF'
			const pwdBytes = CryptoJS.enc.Utf8.parse(pwd)
			var decrypted = CryptoJS.AES.decrypt(data.key, pwdBytes,{
				mode: CryptoJS.mode.ECB,
				padding: CryptoJS.pad.Pkcs7
			});
			var dd=CryptoJS.enc.Utf8.stringify(decrypted)
			this.cardKey=dd
			
		},
		async cardOp(sid,type){
			if(!this.cardKey) return
			if(!this.customer || !this.customer.customerId) return
			
			const dir=await this.nfc.apdu('00A40000023F01')
			console.log('dir',dir)
			
			await this.$ut.api('canteen/account/cardOpen',{
				communityId:this.community.id,
				customerId:this.customer.customerId,
				sid:sid,
			}).then(async ()=>{
				let hex = Buffer.from(this.cardKey).toString('hex').padEnd(16,0)
					
			
				
				var res=await this.nfc.apdu('80E00000073F010095FAFFFF')
				console.log(res)
				
				res =await this.nfc.apdu('80D401000DF9F0F0AA99'+hex)
				console.log(res)
				
				res =await this.nfc.apdu('80E0000307280020F0FAFFFF')
				console.log(res)
				
				hex = Buffer.from(this.customer.customerId).toString('hex').padEnd(64,0)
				res =await this.nfc.apdu('00D6000020'+hex)
				
				uni.showToast({
					title:'开卡成功'
				})
				
				console.log(res)
				
				this.$emit('close')	
			})	
			
		},
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
	.btn-round {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		color: #fff;
		font-size: 28rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		align-content: center;
		justify-content: center;
		letter-spacing: 4rpx;
	}
</style>

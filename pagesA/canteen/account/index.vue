<template>
  <ut-page>
    <ut-top bg-color="#fff" class="top-warp" @topHeight="getHeight">
      <f-navbar :bgColor="colors" fontColor="#fff" navbarType="2" title="开户" />
      <view :style="{backgroundColor:'#fff'}" class="padding-sm">
        <u-input prefixIcon="search"
          placeholder="输入姓名、手机号搜索"
          v-model="pageReq.key"
          shape="circle"
          border="surround"
          clearable>
          <template slot="suffix">
            <u-button v-if='pageReq.key' text="搜索" type="success" size="mini" @click="downCallback"></u-button>
          </template>
        </u-input>
      </view>
    </ut-top>

    <mescroll-body
      ref="mescrollRef"
      :safearea="true"
      :top="topWrapHeight+'px'"
      :top-margin="-topWrapHeight+'px'"
      :up="upOption"
      bottom="140"
      @down="downCallback"
      @emptyclick="emptyClick"
      @init="mescrollInit"
      @up="upCallback"
    >
      <u-swipe-action ref="swipeUserList">
        <template v-for="(item,index) in dataList">
          <u-swipe-action-item
            :key="index"
            :disabled="!item.isReOpenCard"
            :name="item.id"
            :options="getActionOption(item)"
            @click="(data)=>{actionOp(data,item)}"
            @longpress="longpress(item)">

            <view class="margin-bottom-sm">
              <view class="bg-white padding-lr-lg flex padding-tb-lg text-content">
                <view class="img-head margin-right-xs">
                  <u-lazy-load v-if="item && item.imgHead"
                    :image="$tools.showImg(item.imgHead,200)"
                    border-radius="75"
                    height="130"
                    width="130" />
                  <u-lazy-load v-else
                    :src="require('@/pages/images/logo.png')"
                    class="img-head-empty"
                    height="130"
                    width="130"></u-lazy-load>
                </view>
                <view class="flex-sub">
                  <view class="text-bold text-lg">{{ item.name }}</view>
                  <view class="flex justify-center align-center">
                    <view class="flex-sub text-df">
                      <view class="text-gray margin-right">{{ item.phone }}</view>
                      <view class="text-gray margin-right-xs">{{ item.idcard }}</view>
                      <view class="text-gray margin-right-xs">{{ item.canteenName }}</view>
                      <view class="text-gray margin-right-xs text-sm">
                        <text>操作人：</text>
                        <text>{{ item.opUserName }}</text>
                      </view>
                    </view>
                    <view class="margin-top-xs flex justify-end text-sm">
                      <view v-if="!item.isOpenCard" class="btn" style="width:100rpx" @click="openCard(item)">开卡</view>
                      <view v-else>已开卡</view>
                    </view>
                  </view>
                </view>

              </view>
            </view>

          </u-swipe-action-item>
        </template>
      </u-swipe-action>
    </mescroll-body>

    <ut-fixed position="bottom" safe-area-inset>
      <view class="margin">
        <u-button :color="colors"
          shape="circle"
          size="normal"
          text="新增用户"
          type="primary"
          @click="goEdit"></u-button>
      </view>
    </ut-fixed>

    <u-popup v-if="showOpenCard"
      :closeable="true"
      :mask-close-able="true"
      :overlay-style="{zIndex:998}"
      :safe-area-inset-bottom="true"
      :show="showOpenCard"
      :z-index="998"
      close-icon-pos="top-left"
      mode="bottom"
      round="10"
      @close="showOpenCard=false">
      <view class="pop-title">开卡</view>
      <card :customer="selectRow" @close="openCardClose" />
    </u-popup>

    <ut-login-modal :colors="colors"></ut-login-modal>
  </ut-page>
</template>

<script>
const app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import Card from './card'

export default {
  mixins: [MescrollMixin], // 使用mixin
  components: {
    MescrollBody,
    Card,
  },
  options: {
    styleIsolation: 'shared',
  },
  data() {
    return {
      colors: '',
      topWrapHeight: 0,
      upOption: {
        noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
        empty: {
          icon: require('@/pagesA/components/image/nodata.png'),
          tip: '~ 没有数据 ~', // 提示
        },
      },
      pageReq: {
        pagesize: 20,
        pageindex: 1,
        key: '',
      },
      firstLoad: true,
      dataList: [],
      selectRow: {},
      showOpenCard: false,

    }
  },
  onShow() {
    this.setData({ colors: app.globalData.newColor })
  },
  computed: {
    ...mapState({
      commKey: state => state.init.template.commKey,
      token: state => state.user.token,
      userInfo: state => state.user.info,
      community: state => state.init.community,
    }),
  },
  onLoad: async function (options) {
    await this.$onLaunched

  },

  watch: {
    'pageReq.key': {
      handler(v) {
        if (!v) this.downCallback()
      },
    },
  },
  methods: {
    getHeight(h) {
      this.topWrapHeight = h
    },
    /*下拉刷新的回调 */
    downCallback() {
      this.pageReq.pageindex = 1
      this.mescroll.resetUpScroll()
    },
    async upCallback(page) {
      this.$ut.api('canteen/account/listpg', {
        communityId: this.community.id,
        ...this.pageReq,
      }).then(({ data }) => {

        setTimeout(() => {
          this.mescroll.endBySize(data.info.length, data.record)
        }, this.firstLoad ? 0 : 500)
        this.firstLoad = false

        if (this.pageReq.pageindex === 1) this.dataList = [] //如果是第一页需手动制空列表
        this.pageReq.pageindex++
        this.dataList = this.dataList.concat(data.info)
      }).catch(e => {
        this.pageReq.pageindex--
        this.mescroll.endErr()
      })
    },

    emptyClick() {

    },

    getActionOption(item) {
      const btnReOpenCard = {
        text: '重新开卡',
        code: 'reOpenCard',
        style: {
          backgroundColor: '#f56c6c',
        },
      }

      let data = []

      data.push(btnReOpenCard)

      return data
    },
    longpress(item) {
      console.log(item)
    },

    goEdit() {
      this.$tools.routerTo('/pagesA/canteen/account/edit', { customerId: '' })
    },
    openCard(row) {
      this.selectRow = row
      this.showOpenCard = true
    },
    openCardClose() {
      this.showOpenCard = false
      this.downCallback()
    },
    actionOp(data, item) {
      this.selectRow = item
      console.log(data, item)
      let content = ''
      if (data.code === 'reOpenCard') content = '确认对该客户重新开卡'
      uni.showModal({
        title: '操作提示',
        content: content,
        success: res => {
          if (res.confirm) {
            if (data.code === 'reOpenCard') {
              this.showOpenCard = true
            }
          } else if (res.cancel) {
          }
          this.$refs['swipeUserList'].closeAll()
        },
      })
    },
  },
}
</script>
<style>


</style>
<style lang="scss" scoped>
.img-head image {
  flex-shrink: 0;
  width: 130upx;
  height: 130upx;
  border: 5upx solid #fff;
  border-radius: 50%;
}

.btn {
  overflow: hidden;
  min-width: 140rpx;

  padding: 8rpx 10rpx;
  border-radius: 8upx;
  text-align: center;
  box-sizing: border-box;
  border: none;
  color: #fff;
  background: var(--colors);
}

.pop-title {
  padding-top: 20rpx;
  text-align: center;
}

.img-head-empty {
  width: 130rpx;
  height: 130rpx;
  overflow: hidden;
  border-radius: 50% !important;
}
</style>

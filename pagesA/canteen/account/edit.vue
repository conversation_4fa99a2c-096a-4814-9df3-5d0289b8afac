<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="新增顾客" navbarType="2" />
		<view class="padding-lr bg-white">
			<u--form ref="form" labelPostion="left" :model="form" :rules="rules" labelWidth="auto" :labelStyle="{color:'#a0a0a0'}">
				<u-form-item label="姓名" prop="name" required borderBottom>
					<u--input v-model="form.name" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="性别" prop="sex" borderBottom @click="showSex=true">
					<view class="full-width" @click="showSex=true">
						<u--input v-model="sexName" border="none" disabled disabledColor="#ffffff" placeholder="请选择性别" inputAlign='center' style="pointer-events:none"/>
					</view>
					<u-icon v-if="!form.isAudit" slot="right" name="arrow-right"  @click="showSex=true"/>
				</u-form-item>
				<u-form-item label="电话" prop="phone" borderBottom>
					<u--input v-model="form.phone" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="证件号" prop="idcard" borderBottom>
					<u--input v-model="form.idcard" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="出生日期" prop="birthday" required borderBottom  @click="$refs.birthdayPicker.show()">
					<view class="full-width" @click="$refs.birthdayPicker.show()">
						<u--input v-model="form.birthday" border="none" disabled disabledColor="#ffffff" placeholder="请选择出生日期" inputAlign='center' style="pointer-events:none"/>
					</view>
					<u-icon v-if="!form.isAudit" slot="right" name="arrow-right"  @click="$refs.birthdayPicker.show()"/>
				</u-form-item>
				<u-form-item label="民族" prop="nation" borderBottom>
					<u--input v-model="form.nation" border="none" inputAlign='center'/>
				</u-form-item>
				<u-form-item label="地址" prop="address" borderBottom>
					<!-- #ifdef MP -->
					<u-input v-model="form.address" border="none" inputAlign='left' >
						<template #suffix>
							<view @click="chooseLocation"><u-icon name="map" :color="colors" size="38rpx" /></view>
						</template>
					</u-input>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<u--input v-model="form.address" border="none" inputAlign='left' >
						<template #suffix>
							<view @click="chooseLocation"><u-icon name="map" :color="colors" size="38rpx" /></view>
						</template>
					</u--input>
					<!-- #endif -->
				</u-form-item>
				<u-form-item label="头像" prop="address" required borderBottom>
					<diy-person-upload
						v-model="myPhotos"
						mediaType="image"
						:disabled="false"
						:colors="colors"
						:max="1"
						:width="400"
						:height="500"
						:preview-image-width="1200"
						:border-radius="8"
						:headers="headers"
						:action="uploadInfo.server+uploadInfo.single"
						@uploadSuccess="uploadFaceSuccess"
						@imgDelete="imgDelete"
					 />
				</u-form-item>
				<u-form-item label="备注" prop="remark" borderBottom>
					<u--textarea v-model="form.remark" confirm-type="done"></u--textarea>
				</u-form-item>
			</u--form>
		</view>
		<view class="padding-lr padding-tb bg-white">
			<u-button type="primary" shape="circle" :color="colors" text="保存顾客信息" :plain="false" size="normal" @click="save"/>
		</view>
		<view class="bg-white" style="height: var(--safe-area-inset-bottom)"></view>
		<u-action-sheet :show="showSex" :actions="actions" title="请选择性别" @close="showSex = false" @select="sexSelect"></u-action-sheet>
		<pick-date ref="birthdayPicker" :start-year="1901" :end-year="new Date().getFullYear()" 
			:time-init="0"
			:time-hide="[true, true, true, false, false, false]" 
			:time-label="['年', '月', '日', '时', '分', '秒']"
			@submit="birthdaySelect" />

	</ut-page>
	

</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import PickDate from '@/pagesA/components/pickDate.vue'
import DiyPersonUpload from '@/pagesA/components/ut-person-upload/up-person-upload.vue'

export default {
	components: {
		PickDate,
		DiyPersonUpload,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick: true,
			topWrapHeight: 0,
			myPhotos: '',
			customerId:'',
			form:{
				birthday:'',
			},
			rules:{
				
			},
			showSex:false,
			sexName:'',
			actions: [{	id:1,name: '男',},{	id:2,	name: '女',	},{	id:'',	name: '保密'}],
			
		}
	},

	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	onReady() {
			//onReady 为uni-app支持的生命周期之一
	    	this.$refs.form.setRules(this.rules)
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
			uploadInfo: state => state.init.oss,
		}),
		headers() {
			return {
				Token: this.uploadInfo.token,
			}
		},

	},
	onLoad: async function (options) {
		await this.$onLaunched

		if (options.customerId) {
			this.customerId = options.customerId
			this.getPersonInfo()
		}
		
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		endYear(){
			return new Date().getFullYear()
		},

		sexSelect(e) {
			this.form.sex = e.id
			this.sexName = e.name
			this.$refs.form.validateField('sex')
		},

		birthdaySelect(e){
			this.form.birthday=`${e.year}-${e.month}-${e.day}`
		},
		uploadFaceSuccess(res) {
			const item=res.data
			this.$set(this.form, 'imgHead', this.uploadInfo.preview + '?file=' + item.name + item.ext)
		},
		imgDelete(e) {
			this.form.imgHead = ''
		},
		chooseLocation(){
			let that=this
			uni.chooseLocation({
				success: (res) => {
					this.$set(this.form,'address',res.address)
					this.$set(this.form,'latitude',res.latitude)
					this.$set(this.form,'longitude',res.longitude)
				}
			})
		},
		async getPersonInfo(){
			if(!this.customerId) return
			const {data} = await this.$ut.api('canteen/account/info', {
				communityId:this.community.id,
				id:this.customerId,
			})
			this.form=data
			if(this.form.sex){
				const obj=this.actions.find(u=>u.id==this.form.sex)
				if(obj)this.sexName=obj.name
			}

		},
		save(){
			this.$ut.api('canteen/account/open', {
				communityId:this.community.id,
				...this.form,
			}).then(()=>{
				this.$tools.back('downCallback()')				
				
				setTimeout(()=>{
					uni.showToast({
						title:'保存成功'
					})
				},100)
				
			})
		}
	},
}
</script>


<style lang="scss" scoped>


</style>

<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="充值" navbarType="2" />
		<view>
			<view v-if="!cardKey || !nfc || !nfc.adapter || !cardInfo || !cardInfo.cardNo" class="cu-card">
				<view class="cu-item text-center padding-tb-xl text-bold">
					请打开NFC，刷卡后方可充值
				</view>
			</view>
			<view v-if="cardInfo.cardNo" class="cu-card">
				<view class="cu-item padding-lr padding-tb text-content">
					<view>
						<text class="margin-right-sm text-grey">卡号</text>
						<text class="text-lg">{{cardNo}}</text>
					</view>
					<view>
						<text class="margin-right-sm text-grey">姓名</text>
						<text class="text-lg text-bold">{{cardInfo.customer?cardInfo.customer.name:''}}</text>
					</view>
					<view>
						<text class="margin-right-sm text-grey">电话</text>
						<text class="text-lg">{{cardInfo.customer?cardInfo.customer.phone:''}}</text>
					</view>
					<view>
						<text class="margin-right-sm text-grey">出生</text>
						<text>{{cardInfo.customer?cardInfo.customer.birthday:''}}</text>
					</view>
					<view>
						<text class="margin-right-sm text-grey">余额</text>
						<text class="margin-right-xs text-grey">¥</text>
						<text class="text-lg text-bold">{{cardInfo.balance}}</text>
						<text v-if="!cardInfo.accountValid" class="margin-left-xl text-sm">账户禁用</text>
					</view>
					<view>
						<text class="margin-right-sm text-grey">头像</text>
						<image v-if="cardInfo.customer && cardInfo.customer.imgHead" class="image-box"  mode="aspectFit" :src="cardInfo.customer?cardInfo.customer.imgHead:''"></image>
						<image v-else class="image-box" src="../../../static/theme/user.png"></image>
					</view>
				</view>
			</view>
			<view v-if="cardInfo.cardNo" class="cu-card">
				<view class="cu-item padding-lr padding-tb text-content">
					<view>
						<u--input v-model="money" type="number" placeholder="请输入充值金额" style="font-size: 40rpx; text-align: center;" />
					</view>
					<view class="margin-tb-lg">
						<view class="grid col-3 text-content text-black">
							<view class="money-item"><view class="detail-label" @click="money=100">100</view></view>
							<view class="money-item"><view class="detail-label" @click="money=200">200</view></view>
							<view class="money-item"><view class="detail-label" @click="money=300">300</view></view>
							<view class="money-item"><view class="detail-label" @click="money=500">500</view></view>
							<view class="money-item"><view class="detail-label" @click="money=800">800</view></view>
							<view class="money-item"><view class="detail-label" @click="money=1000">1000</view></view>
						</view>
					</view>
					<view class="margin-top-lg">
						<u-button type="primary" :disabled="!money" :color="colors" @click="$shaken(deposit)">确定充值</u-button>
					</view>
				</view>
			</view>
			<view v-else class="cu-card">
				<view class="cu-item text-content padding-lr-sm padding-tb-sm">
					<view class="text-lg">最近充值记录</view>
					<template v-for="(item, index) in depositList.info">
						<view :key="index" class="flex margin-tb-xs padding-tb-xs">
							<view class="flex-sub">
								<text class="text-grey text-sm margin-right">{{item.createTime}}</text>
								<text>{{item.name}}</text>
							</view>
							<view>
								<text class="text-grey text-sm margin-right-xs">¥</text>
								<text :style="{color:colors}">{{item.opMoney}}</text>
							</view>
						</view>
					</template>

	

				</view>
			</view>
		</view>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import NFC from '@/pagesA/nfc/nfc.js'
import CryptoJS from '@/pagesA/nfc/crypto-js.min'

export default {
	mixins: [], // 使用mixin
	components: {
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			noClick:true,
			cardKey:'',
			nfc:null,
			err:'',
			cardNo:'',
			cardInfo:{},
			money:'',
			depositList:{},
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
		
		this.getKey()
		this.getDepositData()
		
		if(app.globalData.nfc==null){
			app.globalData.nfc=new NFC()
		}
		if(app.globalData.nfc!=null){
			app.globalData.nfc.on('connected',this.cardOp)
			app.globalData.nfc.on('error',(err)=>{
				console.log(err)
			});
			this.nfc=app.globalData.nfc
		}
		
		
		//test
		//this.cardNo='1870454592093949952'
		//this.getCardInfo()
		
	},
	onUnload() {
		if(app.globalData.nfc!=null){
			app.globalData.nfc.off('connected')
			app.globalData.nfc.off('error')
		}
	},
	methods: {
		async getKey(){
			const {data} = await this.$ut.api('canteen/info/key',{
				communityId:this.community.id
			})
			const pwd='FFFFFFFFFFFFFFFF'
			const pwdBytes = CryptoJS.enc.Utf8.parse(pwd)
			var decrypted = CryptoJS.AES.decrypt(data.key, pwdBytes,{
				mode: CryptoJS.mode.ECB,
				padding: CryptoJS.pad.Pkcs7
			});
			var dd=CryptoJS.enc.Utf8.stringify(decrypted)
			this.cardKey=dd
		},
		async cardOp(uid,type){
			if(!this.cardKey) return
			
			const dir=await this.nfc.apdu('00A40000023F01')
			console.log('dir',dir)

			//2获取8位随机码
			const random=await this.nfc.apdu('0084000004')
			console.log('random',random)
			this.showReadError(random)
			
			//3得到外部认证指令
			let hex = Buffer.from(this.cardKey).toString('hex').padEnd(16,0)
			const sendRandomKey=this.nfc.randomKey(random,hex);
			console.log('sendKey',sendRandomKey)
			
			//4建立外部认证指令
			const randomKey=await this.nfc.apdu(sendRandomKey)
			console.log('randomKey',randomKey)
			this.showReadError(randomKey)
			
			//读卡内容
			var res=await this.nfc.apdu('00A40000020003')
			console.log('read1',res)
			this.showReadError(res)
			
			res=await this.nfc.apdu('00B000000000')
			console.log('read1',res)
			this.showReadError(res)
			
			this.showReadError(res)
			if(res.length!=34){
				uni.showToast({
					title:'读卡错误',
					icon:'none',
				})
				return
			}
			
			let arr=[]
			for(let i=0;i<32;i++){
				arr.push(res[i])
			}
			
			this.cardNo=String.fromCharCode.apply(null,arr)
			this.getCardInfo()
		},
		parseHex(hex) {
		  let arr = [];
		  for (let i = 0; i < hex.length; i = i + 2) {
			let curCharCode = parseInt(hex.substring(i, i + 2), 16);
			arr.push(curCharCode);
		  }
	
		  return new TextDecoder().decode(new Uint8Array(arr));
		},
		showReadError(res){
			if(res.length<2){
				uni.showToast({
					title:'读卡错误',
					icon:'none',
				})
				return
			} 
			let len=res.length
			
			if(res[len-2]!=0x90 && res[len-1]!=0x00) {
				uni.showToast({
					title:'读卡错误',
					icon:'none',
				})
				return
			}
		},
		async getCardInfo(){
			const {data}=await this.$ut.api('canteen/deposit/cardInfo',{
				cardNo:this.cardNo
			});
			this.cardInfo=data ||{}
		},
		deposit(){
			if(!this.cardNo) return;
			if(this.money=='' || this.money<=0 || this.money>=100000 ) return;
			this.$ut.api('canteen/deposit/deposit',{
				cardNo:this.cardNo,
				money:this.money,
			}).then(()=>{
				uni.showToast({
					title:'充值成功'
				})
				this.cardInfo={}
				this.cardNo=''
				this.getDepositData()
			})
		},
		async getDepositData(){
			const {data} = await this.$ut.api('canteen/deposit/listpg',{
				communityId:this.community.id,
			})
			this.depositList=data;
		}
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>
.image-box{
	width:300rpx;
	height: 300rpx;
	border-radius: 10rpx;
	overflow: hidden;
}

.money-item{
	
	text-align: center;
	padding: 10rpx;
	.detail-label{
		width:100%;
		height: 100%;
		border: 1rpx solid #e0e0e0;
		border-radius: 10rpx;
		line-height: 2.5;
		font-size: 30rpx;
	}
}

:deep(.u-input__content__field-wrapper__field) {
	font-size: 40rpx !important;
	
	.uni-input-input{
		text-align: center;
		font-weight: bold;
		color: var(--colors);
	}
}
</style>

import Vue from 'vue';
import App from './App';
import i18n from '@/common/i18n'
import http from '@/common/request'
import store from '@/common/store'
import platform from '@/common/platform'
import {
	router,
	RouterMount
} from '@/common/router'
import tools from '@/common/utils/tools.js'
//微信sdk
import wxsdk from '@/common/wechat/sdk';
import shaken from '@/common/shaken'

//uview
import uView from '@/components/uview-ui'
Vue.use(uView)

//消息推送
import appPush from '@/common/push'
Vue.use(appPush)

import audio from '@/common/utils/audio.js'

// #ifdef MP
// 引入uView对小程序分享的mixin封装
const mpShare = require('@/components/uview-ui/libs/mixin/mpShare.js')
Vue.mixin(mpShare)
// #endif

Vue.prototype.$ut = http
Vue.prototype.$store = store
Vue.prototype.$tools = tools
Vue.prototype.$shaken = shaken
Vue.prototype.$wxsdk = wxsdk
Vue.prototype.$platform = platform
Vue.prototype.$audio = audio

Vue.prototype.$onLaunched = new Promise(resolve => {
	Vue.prototype.$isResolve = resolve
})

// import nodata from "@/components/public/nodata"
// Vue.component("nodata", nodata);

Vue.config.productionTip = false;
Vue.mixin({
	onShow() {
		// #ifdef H5

		// #endif
	},
	methods: {
		setData: function(obj, callback) {
			let that = this;
			const handleData = (tepData, tepKey, afterKey) => {
				tepKey = tepKey.split('.');
				tepKey.forEach(item => {
					if (tepData[item] === null || tepData[item] === undefined) {
						let reg = /^[0-9]+$/;
						tepData[item] = reg.test(afterKey) ? [] : {};
						tepData = tepData[item];
					} else {
						tepData = tepData[item];
					}
				});
				return tepData;
			};
			const isFn = function(value) {
				return typeof value == 'function' || false;
			};
			Object.keys(obj).forEach(function(key) {
				let val = obj[key];
				key = key.replace(/\]/g, '').replace(/\[/g, '.');
				let front, after;
				let index_after = key.lastIndexOf('.');
				if (index_after != -1) {
					after = key.slice(index_after + 1);
					front = handleData(that, key.slice(0, index_after), after);
				} else {
					after = key;
					front = that;
				}
				if (front.$data && front.$data[after] === undefined) {
					Object.defineProperty(front, after, {
						get() {
							return front.$data[after];
						},
						set(newValue) {
							front.$data[after] = newValue;
							// that.$forceUpdate();
						},
						enumerable: true,
						configurable: true
					});
					front[after] = val;
				} else {
					that.$set(front, after, val);
				}
			});
			isFn(callback) && this.$nextTick(callback);
		}
	}
});
App.mpType = 'app';
const app = new Vue({
	i18n,
	store,
	...App
});

// #ifdef H5
RouterMount(app, router, '#app')
//app.$mount(); 
// #endif

// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif

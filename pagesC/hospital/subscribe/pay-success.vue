<template>
	<view class="special-service">
		<view class="select-agent">
			<view style="font-weight: 600;letter-spacing: 2rpx;">选择陪诊</view>
			<view class="own-random">
				<template v-for="(item,index) in topList">
					<view :key="index" :class="item.active?'own':'random'" @click="changeItem(item)">{{ item.title }}</view>
				</template>
			</view>
		</view>
		<view class="tabs-service">
			<template v-for="(item,index) in tabs">
				<view :key="index" :class="item.active?'male-active':'male'" @click="selectTabs(item,index)">{{ item.title }}</view>
			</template>
		</view>
		<view v-if="tabIndex == 1" class="service-card">
			<template v-for="(item,index) in cardList">
				<view :key="index" class="card-item" :style="{border: item.active?'1px solid #82DAC6':''}" @click="selectAgentItem(item)">
					<view class="imgs">
						<image :src="$tools.showImg(item.img,750)" mode="aspectFill"></image>
						<view class="pass-value">满意值 {{ item.passValue }}</view>
					</view>
					<view class="card-right">
						<view class="title-sex">
							<view class="service-name">{{item.name}}</view>
							<view class="icon-sex" :style="{color: item.gender==0?'#2AAEDB':'#FD42D3'}">
								<u-icon v-if="item.gender==0" name="man" size="12" color="#2AAEDB"></u-icon>
								<text v-if="item.gender==0">男士</text>
								<u-icon v-if="item.gender==1" name="woman" size="12" color="#FD42D3"></u-icon>
								<text v-if="item.gender==1">女士</text>
							</view>
						</view>
						<view class="star-service">
							<u-rate
								readonly 
								size="15"
								:allowHalf="true"
								:value="item.rate" 
								active-color="#FF9918" 
								inactive-color="#FF9918" 
								gutter="1">
							</u-rate>
							<view style="font-size: 24rpx;color: #999;margin-left: 10rpx;">服务：<text style="color: #000;margin-right: 6rpx;">{{ item.serviceNum }}</text>次</view>
						</view>
						<view style="font-size: 24rpx;color: #999;margin: 6rpx 0;">好评：<text style="color: #000;margin-right: 6rpx;">{{ item.goodRemark }}</text>次</view>
						<view class="often-address">常驻：{{ item.oftenAddress }}</view>
					</view>
					<view :class="item.active?'select-him-active':'select-him'">{{ item.active ? '已选陪诊' : '选他陪诊'}}</view>
				</view>
			</template>
		</view>
		<view v-if="tabIndex == 2" class="service-card">
			<template v-for="(item,index) in cardList2">
				<view :key="index" class="card-item" :style="{border: item.active?'1px solid #82DAC6':''}" @click="selectAgentItem(item)">
					<view class="imgs">
						<image :src="$tools.showImg(item.img,750)" mode="aspectFill"></image>
						<view class="pass-value">满意值 {{ item.passValue }}</view>
					</view>
					<view class="card-right">
						<view class="title-sex">
							<view class="service-name">{{item.name}}</view>
							<view class="icon-sex" :style="{color: item.gender==0?'#2AAEDB':'#FD42D3'}">
								<u-icon v-if="item.gender==0" name="man" size="12" color="#2AAEDB"></u-icon>
								<text v-if="item.gender==0">男士</text>
								<u-icon v-if="item.gender==1" name="woman" size="12" color="#FD42D3"></u-icon>
								<text v-if="item.gender==1">女士</text>
							</view>
						</view>
						<view class="star-service">
							<u-rate
								readonly 
								size="15"
								:allowHalf="true"
								:value="item.rate" 
								active-color="#FF9918" 
								inactive-color="#FF9918" 
								gutter="1">
							</u-rate>
							<view style="font-size: 24rpx;color: #999;margin-left: 10rpx;">服务：<text style="color: #000;margin-right: 6rpx;">{{ item.serviceNum }}</text>次</view>
						</view>
						<view style="font-size: 24rpx;color: #999;margin: 6rpx 0;">好评：<text style="color: #000;margin-right: 6rpx;">{{ item.goodRemark }}</text>次</view>
						<view class="often-address">常驻：{{ item.oftenAddress }}</view>
					</view>
					<view :class="item.active?'select-him-active':'select-him'">{{ item.active ? '已选陪诊' : '选他陪诊'}}</view>
				</view>
			</template>
		</view>
		<view class="special-service-sbmit">
			<view class="service-submit">
				<u-button color="#0bb584" @click="submit">确认陪诊</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	
	export default {
		data(){
			return {
				tabIndex: 1,
				cardList: [
					{
						id: '01',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱一光',
						gender: 1,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 1.5,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					},
					{
						id: '02',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱一光',
						gender: 0,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 2,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					},
					{
						id: '03',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱一光',
						gender: 1,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 3.5,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					},
					{
						id: '04',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱一光',
						gender: 0,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 4,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					}
				],
				cardList2: [
					{
						id: '01',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱二光',
						gender: 0,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 1.5,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					},
					{
						id: '02',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱二光',
						gender: 1,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 2.5,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					},
					{
						id: '03',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱二光',
						gender: 0,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 3,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					},
					{
						id: '04',
						img: 'https://oss.afjy.net/api/file/preview?file=7BTMsrN',
						name: '朱二光',
						gender: 1,
						score: '',
						serviceNum: '111',
						goodRemark: '23',
						rate: 4,
						oftenAddress: '云南省第一人民医院，云南省第二人民医院',
						passValue: '99.6'
					}
				],
				topList: [
					{
						id: '01',
						title: '自选陪诊',
						active: true
					},
					{
						id: '02',
						title: '随机匹配',
						active: false
					}
				],
				tabs: [
					{
						id: '01',
						title: '男士陪诊',
						active: true
					},
					{
						id: '02',
						title: '女士陪诊',
						active: false
					}
				]
			}
		},
		onLoad: async function(options) {
			await this.$onLaunched;
		},
		onShow: function () {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		created() {

		},
		methods: {
			changeItem(item){
				let obj = this.topList.find(v => v.active)
				if(obj){
					obj.active = false
					item.active = true
				}
			},
			selectTabs(item,index){
				this.tabIndex = index + 1
				let obj = this.tabs.find(v => v.active)
				if(obj){
					obj.active = false
					item.active = true
				}
			},
			selectAgentItem(item){
				if(item.active){
					this.$set(item,'active',false)
				}else{
					this.$set(item,'active',false)
					let temp = {}
					let obj = this.cardList.find(v => v.active)
					if(obj != undefined){
						temp = obj
					}
					temp.active = false
					item.active = true
				}
			},
			selectAgentItem2(item){
				if(item.active){
					this.$set(item,'active',false)
				}else{
					this.$set(item,'active',false)
					let temp = {}
					let obj = this.cardList2.find(v => v.active)
					if(obj != undefined){
						temp = obj
					}
					temp.active = false
					item.active = true
				}
			},
			submit() {
				const index = this.tabIndex
				let serviceList = []
				if (index === 1) serviceList = this.cardList
				else if (index === 2) serviceList = this.cardList2
				let active = serviceList.find(u => u.active)
				if (active) this.$emit('next')
				else uni.showToast({
					title: '请先选择陪诊',
					icon: 'none'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.special-service {
		background: #fff;
		border-radius: 10rpx;
		margin: 0 0 200rpx ;
		padding: 20rpx;
	}
	.select-agent {
		display: flex;
		align-items: center;
		justify-content: space-between;
		.own-random {
			display: flex;
			align-items: center;
			background: #F7F7F7;
			// padding: 10rpx 30rpx;
			border-radius: 50rpx;
			.own {
				height: 54rpx;
				line-height: 54rpx;
				letter-spacing: 2rpx;
				color: #fff;
				padding: 0 20rpx;
				border-radius: 50rpx;
				background-image: linear-gradient(#0AB685,#0CA8AD);
			}
			.random {
				height: 54rpx;
				line-height: 54rpx;
				letter-spacing: 2rpx;
				color: #999;
				padding: 0 14rpx;
			}
		}
	}
	.tabs-service {
		display: flex;
		align-items: center;
		justify-content: space-around;
		border-bottom: 4rpx solid rgba(0, 0, 0, 0.1);
		margin-top: 10rpx;
		height: 45px;
		line-height: 45px;
		.male-active {
			color: #06B48F;
			position: relative;
			letter-spacing: 2rpx;
		}
		.male {
			color: #666;
			letter-spacing: 2rpx;
		}
	}
	.male-active::after {
		position: absolute;
		content: '';
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		height: 6rpx;
		width: 60%;
		background: #06B58E;
		border-radius: 10rpx;
	}
	.service-card {
		margin-top: 30rpx;
		// border: 1px solid red;
		.card-item {
			border: 1px solid transparent;
			position: relative;
			padding: 20rpx 10rpx;
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			border-radius: 10rpx;
			.imgs {
				position: relative;
				flex-shrink: 0;
				width: 120rpx;
				height: 120rpx;
				image {
					width: 100%;
					height: 100%;
					border-radius: 100rpx;
				}
				.pass-value {
					white-space: nowrap;
					background: #82DAC6;
					font-size: 18rpx;
					position: absolute;
					bottom: -14rpx;
					left: 50%;
					transform: translateX(-50%);
					color: #fff;
					padding: 2rpx 10rpx;
					border-radius: 20rpx;
					opacity: 0.8;
				}
			}
			.card-right {
				margin-left: 20rpx;
				.title-sex {
					display: flex;
					align-items: center;
					.service-name {
						font-size: 30rpx;
						font-weight: 600;
						letter-spacing: 2rpx;
						margin-bottom: 6rpx;
					}
					.icon-sex {
						display: flex;
						align-items: center;
						font-size: 18rpx;
						background: #EEEEEE;
						border-radius: 8rpx;
						margin-left: 10rpx;
						padding: 2rpx 4rpx;
						letter-spacing: 2rpx;
					}
				}
				.star-service {
					display: flex;
					align-items: center;
					margin-left: -2rpx;
				}
				.often-address {
					font-weight: 600;
					font-size: 24rpx;
					max-width: 500rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
			.select-him {
				height: 40rpx;
				line-height: 40rpx;
				position: absolute;
				top: 30rpx;
				right: 20rpx;
				background: #F7F7F7;
				font-size: 24rpx;
				letter-spacing: 4rpx;
				border-radius: 20rpx;
				padding: 0 20rpx;
				color: #666;
			}
			.select-him-active {
				height: 40rpx;
				line-height: 40rpx;
				position: absolute;
				top: 30rpx;
				right: 20rpx;
				background: #06B58E;
				font-size: 24rpx;
				letter-spacing: 4rpx;
				border-radius: 20rpx;
				padding: 0 20rpx;
				color: #fff;
			}
		}
	}
	
	.special-service-sbmit {
		position: fixed;
		bottom: 0;
		width:100%;
		margin-left: -40rpx;
		background-color: #fff;
		padding: 52rpx 20rpx;
		margin-top: 20rpx;
		z-index: 2;
	}
</style>
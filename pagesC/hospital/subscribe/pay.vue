<template>
	<ut-page class="hospital-service-order">
		<f-navbar title="订单支付" fontColor="#000" bgColor="#fff" navbarType="6"@click="OrderPay" />
		<view class="hospital-service-order-bg"></view>
		<view class="service-order-body">
			<view class="order-progress">
				<u-line-progress :percentage="percentage" :showText="false" inactiveColor="#fff"></u-line-progress>
			</view>
			<view class="order-subsection">
				<tabs ref="tabs" :tabsData="list" :disable="true" bjColor="transparent" lineColor="transparent"
					fontColor="#ddd" activeColor="#fff" :bold="true" @change="subsectionChang" :init-index="1" />
			</view>

			<view class="order order-project" v-if="!isLoading">
				<!-- <view class="hospital-service-item">
					<view class="service-item-icon">
						<image :src="item.imgHead" class="icon" mode=""></image>
					</view>
					<view class="service-item-content">
						<view class="item-name">{{ item.name }}</view>
						<view class="item-dec text-overflow-ellipsis">{{ item.describe }}</view>
						<view class="item-price">
							<text class="number">{{ item.price }}</text>
							元/次
						</view>
					</view>
					<view class="service-item-subscribe">
						<u-button color="#0bb584" @click="jumpSubscribe(item)">预约</u-button>
					</view>
				</view> -->
				<view>
					<view class="hospital">
						<image v-if="orderInfo.shopImgHead" :showLoading="true" :src="$tools.showImg(orderInfo.shopImgHead,200)" class="hospital-logo" mode="aspectFill"></uimage>
						<image v-else class="hospital-logo" mode=""></image>
						<view class="hospital-name text-overflow-ellipsis">
							<view class="name">{{ orderInfo.shopName }}</view>
							<view v-for="(item,index) in orderInfo.project" :key="index">
								<view class="project">
									<text>{{item.name}}</text>
									<text>{{item.price}}元</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="order order-detail" v-if="!isLoading">
				<view>订单编号：{{orderInfo.code}}</view>
				<view>下单时间：{{orderInfo.createTime}}</view>
				<view>预约时间：{{orderInfo.agreeTime}}</view>
				<view>预约时段：
					<template v-for="(item,index) in orderInfo.times">
						<text :key="index">{{item}}</text><br>
					</template>
				</view>
				<view>预约人：{{orderInfo.agreePerName}} ({{orderInfo.agreeContactTel}})</view>
				
				<view>备注：{{orderInfo.remark}}</view>
			</view>
			<view class="order order-detail" v-if="!isLoading">
				<view>支付金额：{{orderInfo.payMoney}}</view>
				<view>支付方式：微信支付</view>
			</view>
			<!-- <view @click="OrderPay">支付</view> -->
		</view>
		<view class="footer" v-if="!isLoading && !isQuick">
			<view class="btn btn-save" v-if="!orderInfo.payOver && !orderInfo.state.isExpress" @click="OrderPay">立即支付{{orderInfo.payMoney}}元</view>
			<view class="btn btn-over" v-else >
				<text v-if="orderInfo.state.isExpress">支付超时已关闭订单</text>
				<text v-else-if="orderInfo.payOver">已经支付</text>
			</view>
		</view>
	</ut-page>

</template>

<script>
	let app = getApp();
	import {
		mapState
	} from 'vuex'
	import Tabs from "../components/order-tabs.vue"
	export default {
		components: {
			Tabs
		},
		data() {
			return {
				colors: '',
				showContent: true,
				isLoading:true,
				isQuick:false,
				list: [{
					name: "填写订单"
				}, {
					name: "在线支付"
				}, {
					name: "专人服务"
				}, {
					name: "服务完成"
				}],
				current: 1,
				orderInfoComplete: false,
				orderId: '',
				orderInfo:{},
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				userInfo: state => state.user.info,
			}),
			percentage() {
				let progress = (this.current + 1) * 25
				return progress - 1
			}
		},
		options: {
			styleIsolation: 'shared'
		},
		onShow: function() {
			let colors = app.globalData.newColor;
			this.setData({
				colors: colors
			});
			
			//this.getOrderInfo()
		},
		onLoad: function(options) {
			if (options.id) this.orderId = options.id
			if (!this.orderId) return
			
			if(options.q) this.isQuick=true
			
			this.getOrderInfo()
		},
		methods: {
			getOrderInfo(){
				if(!this.orderId) return
				uni.showLoading({
					title:'请稍等...'
				})
				this.$ut.api('hospital/order/info',{
					commKey:this.commKey,
					orderId:this.orderId,
				}).then(res=>{
					this.orderInfo=res.data
					uni.hideLoading()
					this.isLoading=false
					
					if(this.isQuick) this.OrderPay()
				})
			},
			OrderPay(){
				if(!this.orderId) return
				uni.showLoading({
					title:'请稍等...'
				})
				this.$ut.api('hospital/order/pay',{
					commKey:this.commKey,
					orderId:this.orderId,
					wxOpenId:this.userInfo.wxOpenId,
					appType:2, //小程序支付
					type:1,
				}).then(res=>{
					uni.hideLoading()
					const wechatPayData=res.data.wechatPayData
					wx.requestPayment({
						timeStamp: wechatPayData.timeStamp, 
						nonceStr: wechatPayData.nonceStr,   
						package: wechatPayData.package,     
						signType: wechatPayData.signType, 
						paySign: wechatPayData.paySign,        
					    success(res) {
							//查询订单
							this.orderQuery()
						},
					    fail(e) {
							console.log(e)
						}
					})
				}).catch(e=>{
					uni.hideLoading()
				})
			},
			orderQuery(){
				uni.showToast({
					title:'查询订单'
				})
				this.$ut.api('hospital/order/payQuery',{
					commKey:this.commKey,
					id:this.orderId,
					appType:2, //小程序支付
				}).then(res=>{
					uni.hideLoading()
					uni.redirectTo({
						url:'/pages/order/order'
					})
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hospital{
		display: flex;
	}
	.hospital-logo {
		width: 150rpx;
		height: 150rpx;
		overflow: hidden;
		border-radius: 8rpx;
	}
	
	.hospital-name {
		flex:1;
		padding-left: 10rpx;
		font-size: 34rpx;
		font-weight: 700;
		letter-spacing: 2rpx;
		.name{
			line-height: 1.5;
		}
		.project{
			padding-left: 30rpx;
			padding-right: 40rpx;
			font-size: 28rpx;
			line-height: 2;
			font-weight: normal;
			display: flex;
			justify-content: space-between;
		}
	}
	
	.footer{
		position: fixed;
		bottom: 0;
		padding: 12rpx 80rpx;
		text-align: center;
		background-color: #fff;
		width: 100%;
		.btn{
			background-color: var(--colors);
			padding: 16rpx;
			color: #fff;
			letter-spacing: 4rpx;
			border-radius: 40rpx;
		}
		.btn-over{
			background-color: #9c9c9c;
		}
	}
	
	.hospital-service-order {
		position: relative;
		
		

		.hospital-service-order-bg::after {
			content: ' ';
			width: 750rpx;
			height: 260rpx;
			position: absolute;
			background: linear-gradient(to right, #00bc69, #00a3a8);
		}

		.service-order-body {
			padding: 40rpx 20rpx 20rpx;
			position: relative;
			z-index: 1;

			.order-progress {
				padding: 18rpx;

				:deep(.u-line-progress) {

					.u-line-progress__background {
						height: 32rpx !important;
					}

					.u-line-progress__line {
						background-image: linear-gradient(to right, #00bc69, #00a3a8);
						top: 4rpx;
						left: 4rpx;
						height: 12rpx;
					}
				}
			}


		}
	}

	.order {
		padding: 20rpx;
		background-color: #fff;
		border-radius: 8rpx
	}
	
	.order-detail{
		margin-top: 30rpx;
		line-height: 2;
	}

	.hospital-service-item {
		display: flex;
		margin: 60rpx auto;
		align-items: center;

		.service-item-icon {
			width: 120rpx;
			height: 120rpx;
			margin-right: 20rpx;
			text-align: center;

			.icon {
				width: 100%;
				height: 100%;
			}
		}

		.service-item-content {
			flex: 1;
			overflow: hidden;
			padding: auto 20rpx;
			box-sizing: border-box;

			.item-name {
				font-weight: bold;
				font-size: 30rpx;
				line-height: 1.5;
			}

			.item-dec {
				color: #9c9c9c;
				font-size: 24rpx;
				line-height: 1.5;
			}

			.item-price {
				color: #0bb584;
				font-size: 22rpx;
				letter-spacing: 2rpx;

				.number {
					font-size: 30rpx;
					font-weight: bold;
				}
			}
		}

		.service-item-subscribe {
			width: 120rpx;
			height: 68rpx;
			line-height: 68rpx;
			text-align: center;
			color: #fff;
			background-color: #0bb584;
			border-radius: 10rpx;
			margin-left: 20rpx;
		}
	}
</style>

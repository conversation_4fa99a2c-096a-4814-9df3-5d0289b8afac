<template>
	<!-- 这是订单页面 -->
	<view class="order-success">
		<escort-info />
		<view class="order-title">
			订单信息
		</view>
		<view class="order-body">
			<template v-for="(item,index) in orderList">
				<view :key="index" class="order-item">
					<view class="item-left">
						{{ item.type }}
					</view>
					<view class="item-right">
						<text>{{ item.value }}</text>
						<text v-if="item.remark == '0'" class="iconfont color1 icon-sex-woman"><text
								style="margin-left: 8rpx;">女士</text></text>
						<text v-if="item.remark == '1'" class="iconfont color2 icon-sex-man"><text
								style="margin-left: 8rpx;">男士</text></text>
					</view>
				</view>
			</template>
		</view>
		<order-info :order-info="orderInfo" />
		<order-demand demand="订单需求" money="1288.00"  />
		<view class="buttom-btn-container">
			<view class="order-success-btn">
				<u-button color="#0bb584" icon="checkbox-mark" iconColor="#fff">服务完成</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import EscortInfo from "../components/escort-info.vue"
	import OrderInfo from "../components/order-info.vue"
	import OrderDemand from "../components/demand.vue"
	export default {

		name: 'Order',
		components: {
			EscortInfo,
			OrderInfo,
			OrderDemand
		},
		data() {
			return {
				orderList: [{
					type: '就诊医院',
					value: '昆明医科大学第一附属医院',
					remark: ''
				}, {
					type: '就诊日期',
					value: '2019-08-08  全天',
					remark: ''
				}, {
					type: '接送地址',
					value: '址址址址址址址址址址址址址址址',
					remark: ''
				}, {
					type: '陪诊人员',
					value: '朱一光',
					remark: '0'
				}, {
					type: '陪诊电话',
					value: '13459654569',
					remark: ''
				}, ],
				orderInfo: {}
			}
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.order-success {
		margin-bottom: 200rpx;
	}
	.order-title {
		color: rgb(141, 141, 141);
		margin: 20rpx 0 20rpx 10rpx;
		letter-spacing: 2rpx;
	}

	.order-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 20rpx;

		.order-item {
			padding: 30rpx 0;
			border-bottom: 1rpx solid rgb(240, 240, 240);
			display: flex;
			justify-content: space-between;
			align-items: center;

			&:last-child {
				border-bottom: none;
			}

			&:first-child .item-right {
				width: 45%;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				-o-text-overflow: ellipsis;
			}

			.item-left {
				color: gray;
				letter-spacing: 2rpx;
			}

			.item-right {


				.iconfont {
					font-weight: bolder;
					font-size: 18rpx;
					background-color: rgb(238, 238, 238);
					margin-left: 20rpx;
					padding: 4rpx;
				}

				.color1 {
					color: rgb(254, 58, 212);
				}

				.color2 {
					color: blue;
				}
			}


		}
	}
	
	.buttom-btn-container {
		position: fixed;
		bottom: 0;
		width: 100%;
		margin-left: -20rpx;
		background-color: #fff;
		padding: 52rpx 20rpx;
		margin-top: 20rpx;
		z-index: 2;
		display: flex;
		
		.order-success-btn {
			width: 100%;
		}
	}
</style>

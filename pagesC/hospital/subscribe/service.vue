<template>
	<view class="special-person-service">
		<escort-info />
		<order-info :order-info="orderInfo" />
		<order-demand demand="订单需求" money="1288.00"  />
		<view class="buttom-btn-container">
			<view class="service">
				<u-button color="#0bb584">待服务</u-button>
			</view>
			<view class="cancel">
				<u-button >取消订单</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import EscortInfo from "../components/escort-info.vue"
	import OrderInfo from "../components/order-info.vue"
	import OrderDemand from "../components/demand.vue"
	export default {
		components: {
			OrderInfo,
			OrderDemand,
			EscortInfo
		},
		data() {
			return {
				orderInfo: {
					
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.special-person-service {
		margin-bottom: 200rpx;
		.buttom-btn-container {
			position: fixed;
			bottom: 0;
			width: 100%;
			margin-left: -20rpx;
			background-color: #fff;
			padding: 52rpx 20rpx;
			margin-top: 20rpx;
			z-index: 2;
			display: flex;

			.service {
				flex: 1;
				margin-right: 10rpx;
			}
		}
	}
</style>

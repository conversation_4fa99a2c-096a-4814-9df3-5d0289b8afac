<template>
	<ut-page>
		<view  class="hospital-container">
		<view class="hospital-bg"></view>
		<ut-top class="top" :full="true" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" navbarType='5' />
		</ut-top>
		
		<view class="hospital-body">
			<info :detail="hospitalInfo"/>
			<view style="margin-top: 30rpx;">
				<service :list="projectList" @login="login"/>
			</view>
			
		</view>
		</view>
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
	const app = getApp();
	import {
		mapState,
	
	} from 'vuex'
	import Info from "./components/info.vue"
	import Service from "./components/service.vue"
	export default {
		components: {
			Info,
			Service
		},
		data() {
			return {
				colors: '',
				topWrapHeight:'',
				statusBarHeight:parseInt(app.globalData.statusHeight) + 44,
				id:'',
				hospitalInfo:{},
				projectList:[],
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
			})
		},
		onShow: function() {
			let colors = app.globalData.newColor;
			this.setData({
				colors: colors
			});
		},
		onLoad: async function(options) {
			await this.$onLaunched;
			// #ifdef APP-PLUS
			this.bottoms = '0' //在APP下 规格弹窗的位置发生变化
			// #endif
			
			if(options.id) this.id=options.id
			
			if(!this.id) return
			
			this.getInfo()
			this.getShopProject()
		},
		methods: {
			getHeight(h) {
				this.topWrapHeight = h
			},
			getInfo(){
				this.$ut.api("hospital/shop/info", {
					commKey: this.commKey,
					id:this.id
				}).then(res => {
					this.hospitalInfo = res.data
				})
			},
			getShopProject(){
				this.$ut.api("hospital/shop/project/list", {
					commKey: this.commKey,
					shopId:this.id
				}).then(res => {
					this.projectList = res.data
				})
			},
			login(){
				this.$store.commit('LOGIN_TIP', true)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.hospital-container {
		width: 100%;
		position: relative;
	}

	.hospital-bg {
		width: 100%;
		height: 520rpx;
		position: absolute;
		top: 0;
		left: 0;
		background-color: antiquewhite;
		z-index: 0;
		background: url('images/hospital_info_bg.png') no-repeat center top;
		background-size: 100% 100%;
	}

	.hospital-body {
		padding: 50rpx 30rpx 20rpx;
		background-color: #f4f4f4;
		color: black;
	}
</style>

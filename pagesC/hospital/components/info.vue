<template>
	<view class="hospital-info-container">
		<image v-if="detail.imgHead" @click.stop="$tools.previewImage([detail.imgHead?detail.imgHead:require('@/pages/images/logo.png')],0,800)" :showLoading="true" :src="$tools.showImg(detail.imgHead,200)" class="hospital-logo" mode="aspectFill"></uimage>
		<image v-else class="hospital-logo" mode=""></image>
		<view class="hospital-name">{{ detail.name }}</view>
		<view class="hospital-info-box">
			<view class="hospital-info">
				<view class="info">
					<view class="infotag" v-for="(item,index) in detail.tags">{{item}}</view>
				</view>
				<view class="hospital-info-icon">
					<button class="btn-share" open-type="share">分享</button>

					<text class="iconfont icon-right"></text>
				</view>
			</view>
			<view class="hospital-address text-overflow-ellipsis">
				<text class="iconfont icon-address"></text>
				<text class="text-overflow-ellipsis">{{ detail.address }}</text>

				<view class="hospital-address-icon" @click="mapClick()">
					导航
					<text class="iconfont icon-right"></text>
				</view>
				
			</view>
			<view class="describe">
				<view class="title">医院简介</view>
				<view class="content" ref="content" :style="{display:isMore?'block':'-webkit-box'}">
					
					{{detail.describe}}
				</view>
				
				<view class="more" @click="isMore=true" v-if="!isMore">更多...</view>
				<view class="more" @click="isMore=false" v-else >收起</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			marginTop: {
				type: String,
				default: () => ''
			},
			detail: {
				type: Object,
				default: () => {}
			}

		},
		data() {
			return {
				isMore:false,
				lineHeight:100,
			}
		},
		 mounted () {
		    // 计算展开更多内容超出显示
		    this.$nextTick(() => {
		      // 这里具体行数可依据需求自定义
			 // console.log(this.$refs['content'])
			
		    })
		},
		methods: {
			jumpSubscribe() {
				this.$tools.routerTo('/pagesC/hospital/subscribe/index')
			},
			mapClick() {
				// this.$wxsdk.openLocation({
				// 	latitude: 25.013281,
				// 	longitude: 102.681531,
				// 	name: 'ssss',
				// 	address: 'ddd'
				// });
				this.$wxsdk.openLocation({
					latitude: this.detail.latitude,
					longitude: this.detail.longitude,
					name: this.detail.name,
					address: this.detail.address,
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.btn-share {
		margin: 0;
		padding: 0;
		background: none;
		border: 0;
		line-height: 1;
		font-size: inherit;
		display: inline;
		color: #767676;
	}

	.btn-share::after {
		border: 0;
	}

	.hospital-info-container {
		position: relative;

		.hospital-logo {
			width: 150rpx;
			height: 150rpx;
			overflow: hidden;
			border-radius: 8rpx;
			position: absolute;
			left: 30rpx;
			top:20rpx;
		}

		.hospital-name {
			padding: 16rpx;
			padding-left: 200rpx;
			font-size: 34rpx;
			font-weight: 700;
			color: #fff;
			letter-spacing: 2rpx;
		}

		.hospital-info-box {
			background-color: #fff;
			border-radius: 10rpx;
			margin-top: 10rpx;


			.hospital-info,
			.hospital-address {
				padding: 30rpx;
				display: flex;
			}

			.hospital-info {
				border-bottom: 2rpx solid #eee;
				padding-left: 200rpx;


				.info {
					background-image: -webkit-linear-gradient(left, #00bc69, #00a3a8);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					font-weight: bold;
					display: flex;
				}

				.infotag{
					margin-right: 10rpx;
				}

				.hospital-info-icon {
					margin-left: auto;
					color: #767676;

					.icon-right {
						font-size: 20rpx;
						margin-left: 10rpx;
						font-weight: bold;
						color: #c7c6cb;
					}
				}
			}

			.hospital-address {
				color: #353535;

				.icon-address-1 {
					
				}

				.hospital-address-icon {
					margin-left: auto;
					color: #767676;

					.icon-right {
						font-size: 20rpx;
						margin-left: 10rpx;
						font-weight: bold;
						color: #c7c6cb;
					}
				}
			}


		}
	}

	.text-overflow-ellipsis {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
	
	.describe{
		padding: 0 30rpx 30rpx;
		
		.title{
			font-size: 28rpx;
			font-weight: bold;
			padding-bottom: 20rpx;
		}
		
		.content{
			display: -webkit-box; /*设置为弹性盒子*/
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;  /*最多显示x行*/
			text-overflow: ellipsis;  /*超出显示为省略号*/
			overflow: hidden;
		}
		
		.more{
			text-align: center;
			font-size: 20rpx;
			padding-top: 20rpx;
		}
	}
</style>
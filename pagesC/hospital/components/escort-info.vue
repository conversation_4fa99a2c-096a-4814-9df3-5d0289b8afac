<template>
	<view class="escort-info-container">
		<view class="avatar">
			<image class="img" src="../../../static/imgs/ut-logo.png"></image>
		</view>
		<view class="escort-info">
			<view class="person">
				{{ escortInfo.name }}
				<text class="phone">{{ escortInfo.phone }}</text>
			</view>
			<view class="info">
				<view class="icon">
					<image class="img" :src="escortInfo.icon" mode=""></image>
				</view>
				<view class="content">{{ escortInfo.content }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			escortInfo: {
				type: Object,
				default: () => {
					return {
						name: '李天龙',
						phone: '17658855548',
						icon: require('../../../static/imgs/ut-logo.png'),
						content: '尊享VIP陪诊'
					}
				}
			}
		},
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.escort-info-container {
		background-color: #fff;
		border-radius: 8rpx;
		padding: 20rpx;
		display: flex;
		align-items: center;

		.avatar {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			overflow: hidden;

			.img {
				width: 100%;
				height: 100%;
			}
		}

		.escort-info {
			padding-left: 20rpx;

			.person {
				.phone {
					margin-left: 10rpx;
					color: #666;
				}
			}

			.info {
				margin-top: 10rpx;
				display: flex;
				align-items: center;

				.icon {
					width: 50rpx;
					height: 50rpx;

					.img {
						width: 100%;
						height: 100%;
					}
				}
				
				.content {
					margin-left: 20rpx;
				}
			}
		}
	}
</style>

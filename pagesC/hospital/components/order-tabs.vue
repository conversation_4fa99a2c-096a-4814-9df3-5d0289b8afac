<template>
	<view class="ut-tabs-container"
		:style="{'background-color':bjColor, '--active-olor': activeColor, '--inactive-color':fontColor, '--bold': bold ? 'bold' :''}">
		<scroll-view scroll-x scroll-with-animation class="ut-tabs-scroll">
			<view class="ut-tabs-scroll-box" :style="{'height': tabHeight,'line-height': tabHeight}">
				<view class="ut-tabs-scroll-box-item" v-for="(item,index) in tabsData" :key="index"
					@click="onTabIndex(index,item)">
					<view class="name" :class="tabIndex==index?'active':''">{{item.name}}
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		props: {
			bjColor: {
				type: String,
				default: "#fff"
			},
			tabHeight: {
				type: String,
				default: '90rpx'
			},
			tabsData: {
				type: Array,
				default: function() {
					return []
				}
			},
			fontColor: {
				type: String,
				default: "#fff"
			},
			activeColor: {
				type: String,
				default: "#fff"
			},
			bold: {
				type: Boolean,
				default: () => false
			},
			disable: {
				type: Boolean,
				default: () => false
			},
			initIndex:{
				type:Number,
				default:0,
			}
		},
		data() {
			return {
				tabIndex: this.initIndex
			};
		},
		watch:{
			initIndex:{
				handler(v){
					this.tabIndex=v
				}
			}
		},
		methods: {
			onTabIndex(index, item) {
				if (this.disable) return
				this.tabIndex = index;
				this.$emit("change", index, item)
			}
		}
	}
</script>

<style lang="scss">
	$activeColor:var(--active-olor);
	$inactiveColor:var(--inactive-color);
	$bold:var(--bold);

	.ut-tabs-container {
		width: 100%;
	}

	.ut-tabs-scroll-box {
		white-space: nowrap !important;
		display: flex;
		justify-content: space-around;

		&-item {
			min-width: 100rpx;
			position: relative;
			padding: 0rpx 10rpx;
			font-size: 25rpx;
			letter-spacing: 4rpx;
			display: inline-block;
			text-align: center;

			.name {
				width: 100%;
				color: $inactiveColor;
			}

			.line {
				position: absolute;
				left: 0;
				bottom: 10rpx;
				width: 100%;
				height: 5rpx;
			}
		}
	}

	.active {
		color: $activeColor !important;
		font-weight: $bold;
	}
</style>

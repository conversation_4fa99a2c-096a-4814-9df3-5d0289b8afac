<template>
	<view class="page" :style="{'--colors':colors,'--colors2':colors+'60','--colors3':colors+'90'}">
		<view class="card-body">
			<template v-for="(item,index) in carList">
				<view :key="index" class="item">
					<view class="item-left">
						{{ item.name }}
					</view>
					<view class="item-right">
						<input v-if="item.type == 'input'" placeholder-class="ipt" type="text"
							:placeholder="item.remark" />
						<u-upload v-if="item.type == 'upload'" accept="image" capture="camera" :previewImage="true"
							name="1" multiple :maxCount="2" uploadText="添加图片" width="60" height="60"
							:fileList="fileList1" @afterRead="afterRead" @delete="deletePic"></u-upload>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>
<script>
	export default {
		name: 'IdentityCard',
		data() {
			return {
				colors: '',
				bgColor: '#FFF',
				carList: [{
					name: '身份证名称',
					type: 'input',
					remark: '请填写身份证上的姓名'
				}, {
					name: '身份证号',
					type: 'input',
					remark: '请填写身份证号'
				}, {
					name: '资料图片（正反两面）',
					type: 'upload',
					remark: ''
				}, ],
				fileList1: [],
			}
		},
		methods: {
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
		},
	}
</script>

<style lang="scss" scoped>
	.card-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 20rpx;
		margin-top: 20rpx;

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;
			border-bottom: 1rpx solid rgb(240, 240, 240);

			&:last-child {
				border-bottom: none;
			}

			.item-left {
				color: gray;
				letter-spacing: 2rpx;
			}

			.item-right {

				// text-align: center;
				.ipt {
					letter-spacing: 2rpx;
					color: rgba(#000, 0.4);
					text-align: right;
				}
			}
		}
	}
</style>

<template>
	<view class="hospital-service-container" :style="{marginTop: marginTop}">
		<view class="hospital-service-tips">在线预约您需要的服务</view>
		<template v-for="(item, index) in list">
			<view class="hospital-service-item">
				<view class="service-item-icon">
					<image :src="item.imgHead" class="icon" mode=""></image>
				</view>
				<view class="service-item-content">
					<view class="item-name">{{ item.name }}</view>
					<view class="item-dec text-overflow-ellipsis">{{ item.describe }}</view>
					<view class="item-price">
						<text class="number">{{ item.price }}</text>
						元/次
					</view>
				</view>
				<view class="service-item-subscribe">
					<u-button color="#0bb584" @click="jumpSubscribe(item)">预约</u-button>
				</view>
			</view>
		</template>

	</view>
</template>

<script>
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex';
	export default {
		props: {
			marginTop: {
				type: String,
				default: () => ''
			},
			 list: {
			 	type: Array,
			 	default: () => [],
		    }

		},
		data() {
			return {
				hospitalInfo: {
				}
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				loginTip: state => state.user.loginTip,
				token: state => state.user.token,
			}),
		},
		methods: {
			jumpSubscribe(item) {
				if(!this.token){
					this.$emit("login",true)
				}else{
					this.getNoticeTemplate(item)
					
				}
			},
			getNoticeTemplate(item){
				let that=this
				// #ifdef H5
				 that.$tools.routerTo('/pagesC/hospital/subscribe/index',{id:item.id})
				 return
				// #endif
				this.$ut.api('hospital/order/noticeTemplate',{
					commKey:this.commKey,
					code:'orderpay',
				}).then(res=>{
					const tmplIds = res.data.map((item) => item.tmpl_id)
					wx.requestSubscribeMessage({
					  tmplIds: tmplIds,
					  success (res) {
						 
					  },
					  complete(res){
						   that.$tools.routerTo('/pagesC/hospital/subscribe/index',{id:item.id})
					  }
					})
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hospital-service-container {
		position: relative;
		background-color: #fff;
		border-radius: 10rpx;
		padding: 40rpx 30rpx;


		.hospital-service-tips {
			font-size: 32rpx;
			font-weight: bold;
			letter-spacing: 2rpx;
		}

		.hospital-service-item {
			display: flex;
			margin: 60rpx auto;
			align-items: center;

			.service-item-icon {
				width: 120rpx;
				height: 120rpx;
				margin-right: 20rpx;
				text-align: center;

				.icon {
					width: 100%;
					height: 100%;
				}
			}

			.service-item-content {
				flex: 1;
				overflow: hidden;
				padding: auto 20rpx;
				box-sizing: border-box;

				.item-name {
					font-weight: bold;
					font-size: 30rpx;
					line-height: 1.5;
				}

				.item-dec {
					color: #9c9c9c;
					font-size: 24rpx;
					line-height: 1.5;
				}

				.item-price {
					color: #0bb584;
					font-size: 22rpx;
					letter-spacing: 2rpx;

					.number {
						font-size: 30rpx;
						font-weight: bold;
					}
				}
			}

			.service-item-subscribe {
				width: 120rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				color: #fff;
				background-color: #0bb584;
				border-radius: 10rpx;
				margin-left: 20rpx;
			}
		}
	}

	.text-overflow-ellipsis {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
</style>

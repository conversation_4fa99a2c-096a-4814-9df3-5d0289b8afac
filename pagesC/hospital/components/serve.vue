<template>
	<view class="">
		<view class="server-title">
			服务需求
		</view>
		<view class="server-body">
			<view class="server-content">
				需求需求需求需求需求需求需求需求需求需求需求需求需求需求
			</view>
		</view>
		<view class="pay-body">
			<view class="pay-money">
				<view class="money-left">
					支付金额
				</view>
				<view class="money-right">
					￥ {{ money }}
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	export default {
		name: 'Serve',
		props: {
			money: {
				type: String,
				default: "",
			},
		},
		data() {
			return {

			}
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.server-title {
		color: rgb(141, 141, 141);
		margin: 20rpx 0 20rpx 10rpx;
		letter-spacing: 2rpx;
	}

	.server-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 20rpx;

		.server-content {
			padding: 30rpx 0;
		}


	}

	.pay-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 20rpx;
		margin-top: 20rpx;

		.pay-money {
			padding: 30rpx 0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.money-left {
				color: rgb(141, 141, 141);
			}

			.money-right {
				color: rgb(255, 127, 0);
			}
		}
	}
</style>

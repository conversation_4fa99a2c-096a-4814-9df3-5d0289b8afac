<template>
	<view class="search-box-wrap">
		<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
			<template v-for="(item,index) in list">
				<view class="community-item" :key="index">
					<view class="item-image">
						<u-lazy-load height="100" width="100" border-radius="4" img-mode="aspectFill"
							@click="$tools.previewImage([item.imgHead?item.imgHead:'/static/imgs/ut-logo.png'],0,800)"
							:image="$tools.showImg(item.imgHead?item.imgHead:'/static/imgs/ut-logo.png',200)"></u-lazy-load>
					</view>
					<view class="item-content">
						<view class="name">
							{{item.name}}
							<!-- <text class="city">({{item.city}})</text> -->
						</view>
						<view class="addr">{{item.address}}</view>
					</view>
					<view class="item-op" @click="communityClick(item)">
						<view class="btn" :style="{color:colors,background:colors+'30'}">选择</view>
					</view>
				</view>
			</template>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex'
	export default {
		options: { //小程序样式穿透
			styleIsolation: 'shared'
		},
		props: {
			colors: {
				type: String
			},

			list: {
				type: Array,
				default: () => []
			},

		},
		data() {
			return {
				isLoading: false,
				keyword: '',
				isSearch: false,
				// cityTemp: {},
				focus: false,
				showCity: false,
				provinceIndex: 0,

			}
		},
		computed: {
			...mapState({
				city: state => state.init.city,
				locationAddress: state => state.init.locationAddress,
			}),

		},
		watch: {
			
		},
		methods: {
			communityClick(item){
				this.$emit('select',item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.scroll-box{
		height: 900rpx;
	}
	.search-box-wrap {
		padding: 0 30rpx 10rpx 30rpx;
	
		.community-item {
			width: 100%;
			padding: 8rpx 10rpx;
			display: flex;
	
			.name {
				font-size: 30rpx;
				font-weight: bold;
			}
	
			.item-image {
				padding: 8rpx;
				border-radius: 8rpx;
				overflow: hidden;
			}
	
			.item-op {
				width: 100rpx;
				padding: 20rpx 0;
	
				.btn {
					padding: 4rpx;
					border-width: 1rpx;
					border-radius: 80rpx;
					border-style: solid;
					text-align: center;
					font-size: 20rpx;
					opacity: 0.6;
				}
			}
	
			.item-content {
	
				padding-left: 20rpx;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				font-size: 26rpx;
	
				.addr {
					font-size: 18rpx;
					color: #aaa;
				}
	
				.city {
					font-size: 16rpx;
					color: #ccc;
				}
			}
		}
	}
</style>
<template>
	<view class="page">
		<view class="card-body">
			<view class="item">
				<view class="item-left">陪诊城市：</view>
				<view class="item-right" @click="goSelect()">{{info.cityId?info.cityName:'选择城市'}}</view>
			</view>
			<view class="item" v-if="info.cityId">
				<view class="item-left">陪诊区域：</view>
				<view class="item-right">
						<u-checkbox-group v-model="info.areas"  @change="checkboxChange" placement="row">
							<u-checkbox :customStyle="{marginLeft: '16rpx',marginTop: '20rpx'}"
								v-for="(item, index) in areaList" :key="index" :checked="myArea(item)" :label="item.name"
								:name="item.id" :activeColor="colors">
							</u-checkbox>
						</u-checkbox-group>
				</view>
				
				<!-- 				<view v-if="info.areas.length<=0" class="item-right">
					<view>请选择区域</view>
				</view>
				<view v-else class="item-right">
					<view v-for="(itm,idx) in info.areas">{{itm.name}}</view>
				</view> -->
			</view>
			<view class="item">
				<view class="item-left">身份证号：</view>
				<view class="item-right">
					<input v-model="info.identityCard" placeholder-class="ipt" type="idcard" placeholder="请填写身份证号" />
				</view>
			</view>
			<view class="item">
				<view class="item-left">手机号码：</view>
				<view class="item-right">
					<input v-model="info.phone" placeholder-class="ipt" type="number" placeholder="请填写手机号码" />
				</view>
			</view>
			<view class="item">
				<view class="item-left">性别：</view>
				<view class="item-right">
					<view>
						<text class="sex" :class="{'sex-true':info.sex == 1}" @click="getSex(1)">男</text>
						<text class="sex" :class="{'sex-true':info.sex == 2}" @click="getSex(2)">女</text>
					</view>
				</view>
			</view>
		</view>
		<view class="card-body">
			<view class="item">
				<view class="item-left">展示称呼：</view>
				<view class="item-right">
					<input v-model="info.stageName" placeholder-class="ipt" type="text" placeholder="请填写展示昵称" />
				</view>
			</view>
<!-- 			<view class="item">
				<view class="item-left">形象图片：</view>
				<view class="item-right" style="flex: 1;">
					<ut-image-upload
						name="file" 
						v-model="myPhotos" 
						mediaType="image"
						:colors="colors"
						:max="1"
						:headers="headers" 
						:action="uploadInfo.server+uploadInfo.single" 
						:preview-image-width="1200"
						:width="150"
						:height="150"
						:border-radius="8"
						@uploadSuccess="uploadPhoneSuccess" 
						@imgDelete="imgDelete">
					</ut-image-upload>
					<u-upload multiple :maxCount="3" accept="image" capture="camera" :previewImage="true" name="file"
						uploadText="添加照片" width="60" height="60" :fileList="myPhotos" @afterRead="myPhotoAfterRead"
						@delete="deletePic" @clickPreview="myPhotoPreview"></u-upload>
				</view>
			</view> -->
			<view class="item">
				<view class="item-left">资料图片：</view>
				<view class="item-right idcard-image">
					<ut-image-upload
						name="file"
						v-model="idcardImg1" 
						mediaType="image"
						:colors="colors"
						:max="1"
						:headers="headers" 
						:action="uploadInfo.server+uploadInfo.single" 
						:preview-image-width="1200"
						:width="160"
						:height="120"
						:border-radius="8"
						@uploadSuccess="uploadIdCard1Success" 
						@imgDelete="imgDelete">
						<template v-slot:default>
							<view class="camera">
								<u-icon name="camera-fill" color="#ccc" size="28"></u-icon>
								<text class="text">正面</text>
							</view>
						</template>
					</ut-image-upload>
					<ut-image-upload
						name="file"
						v-model="idcardImg2" 
						mediaType="image"
						:colors="colors"
						:max="1"
						:headers="headers" 
						:action="uploadInfo.server+uploadInfo.single" 
						:preview-image-width="1200"
						:width="160"
						:height="120"
						:border-radius="8"
						@uploadSuccess="uploadIdCard2Success" 
						@imgDelete="imgDelete">
						<template v-slot:default>
							<view class="camera">
								<u-icon name="camera-fill" color="#ccc" size="28"></u-icon>
								<text class="text">反面</text>
							</view>
						</template>
					</ut-image-upload>
				</view>
			</view>

		</view>
		<!-- @confirm="confirm" -->
		<u-picker ref="cityPicker" immediateChange closeOnClickOverlay :confirmColor="colors" v-model="info.cityId" :show.sync="showCity"
			:columns="[city]" keyName="name" @close="showCity = false" @cancel="showCity = false" @confirm="confirm" />
		<!-- <u-picker :show="showCity" ref="uPicker" :columns="columns" @cancel="confirm" @change="changeHandler"></u-picker> -->
	</view>
</template>
<script>
	import {
		mapState,
	} from 'vuex'
	export default {
		name: 'Accmsg',
		options: { styleIsolation: 'shared' },   //自定义组件小程序css穿透
		props: {
			colors: {
				type: String,
				default: '',
			},
			detail: {
				type: Object,
				default: () => {},
			},
			city: {
				type: Array,
				default: () => [],
			},
		},
		data() {
			return {
				info:{},
				showCity: false,
				loading: false,
				areaList: [],
				myPhotos: [],
				fileList1: [],
				idcardImg1:[],
				idcardImg2:[],
				bgColor: '#FFF',
				sex: '',
				cityId:'',
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				uploadInfo: state => state.init.oss,
			}),
			headers(){
				return {
					Token:this.uploadInfo.token
				}
			},
		},
		watch: {
			detail:{
				deep:true,
				handler(v) {
					this.info=v
				}
			},
			'info.cityId': {
				handler(v) {
					if (v) this.getAreaList(v)
				}
			},
			'info.imgHead':{
				handler(v) {
					this.myPhotos=[v]
				}
			},
			'info.identityCardImg1':{
				handler(v) {
					this.idcardImg1=[v]
				}
			},
			'info.identityCardImg2':{
				handler(v) {
					this.idcardImg2=[v]
				}
			},
			info:{
				deep:true,
				handler(v) {
					this.$emit('update:detail',v)
				}
			},
			
		},
		methods: {
			getSex(flag) {
				if(this.info.sex==flag){
					this.info.sex=''
				}else this.$set(this.info,'sex',flag)
			},
			goSelect() {
				this.showCity = true
			},
			getAreaList(cityId) {
				this.$ut.api('hospital/city/area/list', {
					commKey: this.commKey,
					cityId: cityId,
				}).then(res => {
					this.areaList = res.data
				})
			},
			myArea(item) {
				if (!this.info.areas) return false
				const obj = this.info.areas.find(u => u == item.id)
				if (obj) return true
				return false
			},
			changeHandler(e) {
				const {
					columnIndex,
					value,
					values, // values为当前变化列的数组内容
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e
				// 当第一列值发生变化时，变化第二列(后一列)对应的选项
				if (columnIndex === 0) {
					// picker为选择器this实例，变化第二列对应的选项
					picker.setColumnValues(1, this.columnData[index])
				}
			},
			// 回调参数为包含columnIndex、value、values
			confirm(e) {
				this.showCity = false
				const selCity = e.value[0]
				this.$set(this.info,'cityId',selCity.id)
				this.$set(this.info,'cityName',selCity.name)
				this.getAreaList(selCity.id)
				// console.log('e', e);
			},
			uploadPhoneSuccess(uploadFileRes){
				uploadFileRes.forEach(r=>{
					let item=r.data
					this.$set(this.info,'imgHead',this.uploadInfo.preview  + '?file='+item.name+item.ext)
				})
				
			},
			uploadIdCard1Success(uploadFileRes){
				uploadFileRes.forEach(r=>{
					let item=r.data
					this.$set(this.info,'identityCardImg1',this.uploadInfo.preview  + '?file='+item.name+item.ext)
				})
				
			},
			uploadIdCard2Success(uploadFileRes){
				uploadFileRes.forEach(r=>{
					let item=r.data
					this.$set(this.info,'identityCardImg2',this.uploadInfo.preview  + '?file='+item.name+item.ext)
				
				})
				
			},
			imgDelete(e){
				console.log(e)
			},
			async myPhotoAfterRead(event) {
				let lists = [].concat(event.file)
				let fileListLen = this.myPhotos.length
				lists.map((item) => {
					this.myPhotos.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadPhotoFilePromise(lists[i].url)
					let item = this.myPhotos[fileListLen]
					this.myPhotos.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			uploadPhotoFilePromise(url) {
				return new Promise((resolve, reject) => {
					this.$tools.uploadImage(
						this.uploadInfo.url, {
							Token: this.uploadInfo.token
						},
						url
					).then(res => {
						if(res.length>0)
						resolve(this.uploadInfo.preview +'/'+ res[0].name+res[0].ext)
						// resolve(this.uploadInfo.preview +'?file='+ res[0].name+res[0].ext)

					})
				})
			},
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {
					this.myPhotos.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			myPhotoPreview(e){
				console.log(e)
			},
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			checkboxChange(e){
				this.$set(this.info,'areas',e)
			}
		},
	}
</script>

<style lang="scss" scoped>

	.idcard-image {
		display: flex;
	}
	
	.camera{
		text-align: center;
		display: flex;
		flex-direction: column;
		align-content: center;
		justify-content: center;
		align-items: center;
		.text{
			font-size: 20rpx;
			line-height: 1.5;
		}
	}
	.sex-true {
		padding: 20rpx 30rpx;
		background-color: var(--colors);
		color: #fff;
		//background-color: orange;
		border: 1rpx solid orange;
	}

	.card-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 20rpx;
		margin-top:20rpx;

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;
			border-bottom: 1rpx solid rgb(240, 240, 240);

			&:last-child {
				border-bottom: none;
			}

			.item-left {
				color: gray;
				letter-spacing: 2rpx;
				min-width: 160rpx;
			}

			.item-right {
				:deep(.ut-image-upload-list) {
					justify-content: flex-end;
				}
				
				
				
				
				.sex {
					border: 1rpx solid rgba(0, 0, 0, 0.4);
					padding: 20rpx 30rpx;
				}

				

				.ipt {
					letter-spacing: 2rpx;
					color: rgba(0, 0, 0, 0.4);
					text-align: right;
				}
			}
		}
		
		
	}
	
	.card-body:first-child{
		margin-top: 0;
	}
	
	:deep(.u-checkbox-group) {
		flex-wrap: wrap;
	}
</style>

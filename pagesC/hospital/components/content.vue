<template>
	<u-popup class="" :show="show" mode="bottom" border-radius="34" :closeable="true" :safe-area-inset-bottom="true"
		:mask-close-able="true" height="500" close-icon-pos="top-left" @close="close">
		<view class="hospital-service-content">
			<view class="title">尊享VIP陪诊服务内容</view>
			<view class="content-body">
				<view class="body-content">{{projectInfo.remark}}</view>
				<view class="body-dec" v-html="projectInfo.detailContent"></view>
				<!-- <view class="service-list">
					<template v-for="(service, serviceIndex) in serviceList">
						<view :key="serviceIndex" class="service-item">
							<view class="service-icon">
								<text class="iconfont" :class="service.icon"></text>
							</view>
							<view class="service-item-content">
								<template v-for="(item, index) in service.service">
									<view :key="index" class="item-content-name">{{ item }}</view>
								</template>
							</view>
						</view>
					</template>

				</view>
				<view class="service-object-list">
					<view class="service-object-title">服务对象</view>
					<template v-for="(item, index) in serviceObject">
						<view class="service-object-item">
							<view class="object-item-icon">
								<text class="iconfont" :class="item.icon"></text>
							</view>
							<view class="object-item-content">{{ item.name }}</view>
						</view>
					</template>
				</view> -->
			</view>
			<u-button color="#0bb584" @click="close">我知道了，开始预约</u-button>
		</view>

	</u-popup>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: () => false
			},
			projectInfo:{
				type:Object,
				default:()=>{},
			},
		},
		data() {
			return {
				serviceList: [{
					icon: 'icon-vip',
					service: ["排队缴费", "市内接送", "送取结果"]
				}, {
					icon: 'icon-vip',
					service: ["了解病史", "诊前提示", "转达医诉"]
				}, {
					icon: 'icon-vip',
					service: ["规划流程", "陪同检查", "就医协助", "传达医嘱"]
				}, {
					icon: 'icon-vip',
					service: ["异地就医", "预定食宿", "定期回访", "诊后关爱"]
				}],
				serviceObject: [{
						icon: "icon-vip",
						name: "没时间陪亲人看病"
					},
					{
						icon: "icon-vip",
						name: "不想浪费时间等待"
					}, {
						icon: "icon-vip",
						name: "病情不明担心存在误诊"
					}, {
						icon: "icon-vip",
						name: "流程繁杂外来语言不通"
					}
				]
			}
		},
		methods: {
			close() {
				this.$emit('update:show', !this.show)
			}
		}
	}
</script>

<style lang="less" scoped>
	.hospital-service-content {
		width: 100%;
		padding: 30rpx;


		.title {
			text-align: center;
			padding: 0 0 30rpx 0;
			font-weight: bold;
		}

		.content-body {
			max-height: 800rpx;
			overflow-y: scroll;
			margin-bottom: 30rpx;
			margin-top: 20rpx;
		}
	}

	.content-body {
		.body-content {
			background-color: #fef2e4;
			border: 2rpx solid #fecb93;
			padding: 30rpx;
			color: #fe7300;
			letter-spacing: 2rpx;
			line-height: 1.7;
			font-size: 30rpx;
			border-radius: 8rpx;
		}

		.body-dec {
			padding-top: 10rpx;
			font-size: 30rpx;
			letter-spacing: 2rpx;
			line-height: 1.7;
				
			img{
				width: 100%;
			}
			
		}

		.service-list {
			.service-item {
				padding: 42rpx 14rpx;
				display: flex;
				align-items: center;
				border-bottom: 2rpx solid #eee;

				.service-icon {
					width: 100rpx;
					height: 100rpx;
					line-height: 100rpx;
					text-align: center;
					background-color: #efeff9;
					border-radius: 50%;
					overflow: hidden;
					margin-right: 10rpx;

					.iconfont {
						font-size: 60rpx;
						color: #5f8efd;
					}
				}

				.service-item-content {
					display: flex;
					font-size: 30rpx;
					justify-content: space-between;
					flex-wrap: wrap;
					flex: 1;

					.item-content-name {
						margin: 10rpx 20rpx;
						color: #147ed9;
					}
				}
			}
		}

		.service-object-list {
			.service-object-title {
				font-size: 32rpx;
				font-weight: bold;
				padding: 26rpx 0;
				letter-spacing: 2rpx;
			}

			.service-object-item {
				height: 50px;
				background-color: #f3f6ff;
				color: #656c76;
				letter-spacing: 4rpx;
				font-size: 30rpx;
				margin-top: 22rpx;
				border-radius: 8rpx;
				position: relative;
				display: flex;
				align-items: center;


				.object-item-icon {
					position: absolute;
					left: 70rpx;

					.iconfont {
						font-size: 50rpx;
						color: #6392fc;
					}
				}

				.object-item-content {
					width: 100%;
					text-align: center;
				}
			}
		}
	}
</style>

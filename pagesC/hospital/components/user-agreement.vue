<template>
	<u-popup class="" 
		:show="show" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="true"
		:mask-close-able="true" height="500" close-icon-pos="top-left" @close="close">
		<view class="hospital-service-content">
			<view class="title">陪诊服务协议</view>
			<view class="content-body">
				<view class="body-content">
				<span v-html="content.detailContent"></span>
				</view>
			</view>
			<u-button :color="colors" @click="close">确认</u-button>
		</view>

	</u-popup>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: () => false
			},
			content:{
				type:Object,
				default:()=>{},
			},
			colors:{
				type:String,
				default:'#0bb584'
			}
		},
		data() {
			return {
				serviceList: [{
					icon: 'icon-vip',
					service: ["排队缴费", "市内接送", "送取结果"]
				}, {
					icon: 'icon-vip',
					service: ["了解病史", "诊前提示", "转达医诉"]
				}, {
					icon: 'icon-vip',
					service: ["规划流程", "陪同检查", "就医协助", "传达医嘱"]
				}, {
					icon: 'icon-vip',
					service: ["异地就医", "预定食宿", "定期回访", "诊后关爱"]
				}],
				serviceObject: [{
						icon: "icon-vip",
						name: "没时间陪亲人看病"
					},
					{
						icon: "icon-vip",
						name: "不想浪费时间等待"
					}, {
						icon: "icon-vip",
						name: "病情不明担心存在误诊"
					}, {
						icon: "icon-vip",
						name: "流程繁杂外来语言不通"
					}
				]
			}
		},
		methods: {
			close() {
				this.$emit('update:show', !this.show)
			}
		}
	}
</script>

<style lang="less" scoped>
	.hospital-service-content {
		width: 100%;
		padding: 30rpx;


		.title {
			text-align: center;
			padding: 0 0 30rpx 0;
			font-weight: bold;
		}

		.content-body {
			max-height: 800rpx;
			overflow-y: scroll;
			margin-bottom: 30rpx;
			margin-top: 20rpx;
		}
	}

	.content-body {
		.body-content {
			padding: 10rpx 20rpx;
			letter-spacing: 4rpx;
			font-size: 28rpx;
			border-radius: 8rpx;
		}

		
	}
</style>

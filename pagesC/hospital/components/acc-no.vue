<template>
	<view class="page">
		<view class="card-body">
			<view class="item">
				<view class="item-left">审核不通过</view>
				<view class="item-right">
					<view>可以原因如下：</view>
					<view>1.展示昵称违反国家相关政策</view>
					<view>2.形象图片清晰</view>
					<view>3.资料号码错误</view>
					<view>4.资料图片不够清晰</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		options: { styleIsolation: 'shared' },   //自定义组件小程序css穿透
		props: {
			colors: {
				type: String,
				default: '',
			},
			
		},
		data() {
			return {
				
			}
		},
		computed: {
			// ...mapState({
			// 	commKey: state => state.init.template.commKey,
			// }),
		},
		watch: {
		},
		methods: {
			
		},
	}
</script>

<style lang="scss" scoped>

	.card-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 20rpx;
		margin-top: 20rpx;

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 0;
			border-bottom: 1rpx solid rgb(240, 240, 240);

			&:last-child {
				border-bottom: none;
			}

			.item-left {
				color: gray;
				letter-spacing: 2rpx;
				min-width: 160rpx;
			}

			.item-right {
				:deep(.ut-image-upload-list) {
					justify-content: flex-end;
				}	
			}
		}
	
	}
	

</style>

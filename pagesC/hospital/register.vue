<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" :title="title" navbarType="6">
		</f-navbar>
		<view class="acc-body">
			<acc-msg :detail.sync="employeeInfo" :city="cityList" :colors="colors"></acc-msg>
			<acc-no v-if="pageType==2" :colors="colors"></acc-no>
		</view>
		<view class="acc-bottom">
			<view class="protocol" v-if="pageType==0">
				<view class="service-clause">
					<u-checkbox-group v-model="checkboxValue">
						<u-checkbox name="agree" shape="circle" :activeColor="colors" label="我已阅读并同意"></u-checkbox>
					</u-checkbox-group>
					<text class="link" @click="showUserAgreement=true">《陪诊服务协议》</text>
				</view>

			</view>
			<u-button v-if="disvalue && pageType==0" type="success" text="注册成为陪诊师" @click="reg"></u-button>
			<u-button v-else-if="!disvalue && pageType==0" color="#c2c2c2" :disabled="true" text="注册成为陪诊师"></u-button>
			<u-button v-if="pageType==1 || pageType==2" :color="colors" text="修改资料" @click="reReg()"></u-button>
		</view>
		<user-agreement :show.sync="showUserAgreement" :content="agreementInfo" :colors="colors"></user-agreement>
	</ut-page>
</template>

<script>
	var app = getApp();
	import {
		mapState,
	} from 'vuex'

	import userAgreement from './components/user-agreement.vue'
	import AccMsg from "./components/acc-msg.vue"
	import AccNo from "./components/acc-no.vue"
	import IdentityCard from "./components/identity-card.vue"
	export default {
		name: 'AccompanyRegister',
		components: {
			userAgreement,
			AccMsg,
			IdentityCard,
			AccNo,
		},
		data() {
			return {
				colors: '',
				showUserAgreement: false,
				agreementInfo: {},
				checkboxValue: [],
				pageType:'',
				employeeInfo: {
					id: '',
					cityId: '',
					cityName: '',
					areas: [],
					identityCard: '',
					phone: '',
					sex: '',
					stageName: '',
					imgHead: '',
					identityCardImg1: '',
					identityCardImg2: '',
				},
				cityList: [],
			}
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				userInfo: state => state.user.info,
			}),
			disvalue() {
				return this.checkboxValue.length > 0 ? true : false
			},
			title(){
				if(this.pageType==undefined || this.pageType=='') return '注册陪诊师'
				if(this.pageType==1) return '注册陪诊师（待审核)'
				if(this.pageType==2) return '注册陪诊师（审核未通过)'
			}
		},
		onLoad: async function(options) {
			if (options.type) this.pageType = options.type
			
			this.getAgreement()
			this.getMyInfo()
			this.getCityList()
		},
		methods: {
			getAgreement() {
				this.$ut.api('hospital/shop/agreement/pz', {
					commKey: this.commKey,
					code: 'pz',
				}).then(res => {
					this.agreementInfo = res.data
				})
			},
			getMyInfo() {
				this.$ut.api('hospital/employee/myInfo', {
					commKey: this.commKey,
				}).then(res => {
					if (res.data) {
						this.employeeInfo = res.data
						this.$set(this.employeeInfo,'areas',this.employeeInfo.areaIds)
					}
				})
			},
			getCityList() {
				this.$ut.api('hospital/city/list', {
					commKey: this.commKey,
				}).then(res => {
					this.cityList = res.data
				})
			},
			reg(){
				this.$ut.api('hospital/employee/register', {
					commKey: this.commKey,
					imgHead:this.employeeInfo.imgHead,
					stageName:this.employeeInfo.stageName,
					sex:this.employeeInfo.sex,
					phone:this.employeeInfo.phone,
					identityCard:this.employeeInfo.identityCard,
					identityCardImg1:this.employeeInfo.identityCardImg1,
					identityCardImg2:this.employeeInfo.identityCardImg2,
					cityId:this.employeeInfo.cityId,
					areaIds:this.employeeInfo.areas,
					wxOpenId:this.userInfo.wxOpenId,
				}).then(res => {
					if(this.pageType=='')uni.showToast({title:'注册成功！'})
					if(this.pageType==1)uni.showToast({title:'修改成功！'})
					setTimeout(()=>{
						uni.switchTab({
							url:'/pages/user/index'
						})
					},1000)
				})
			},
			reReg(){
				this.reg()
			}
		}
	}
</script>
<style lang="scss" scoped>
	.acc-body {
		padding: 20rpx 20rpx 200rpx 20rpx;
		background-color: rgb(240, 240, 240);
		// margin-bottom: 200rpx;
	}

	.acc-bottom {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		z-index: 20;

		.protocol {
			.service-clause {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #323232;
				padding: 25rpx 0;
				background-color: #fff;

				.link {
					color: #5ab397;
				}
			}

			.service-submit {
				padding-right: 40rpx;

				.submit-disable {
					height: 78rpx;
					line-height: 78rpx;
					text-align: center;
					background-color: #f7f7f7;
					color: #adadad;
					padding: 0 24rpx;
					font-size: 28rpx
				}
			}
		}
	
		:deep(.u-button) {
			height: 100rpx;
		}
	}
</style>

<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="我的陪诊工作订单" navbarType="6"></f-navbar>
			<view class="search-warp">
				<u-search height="30" :showAction="true"  placeholder="输入关键字搜索" shape="round" />
			</view>
		</ut-top>
	
		<!-- :top="topWrapHeight+'px'" -->
		<!-- <view class="content-warp"> -->
			<mescroll-body 
				@init="mescrollInit" 
				:top="topWrapHeight+'px'"
				:top-margin="-topWrapHeight+'px'"
				bottom="20" 
				:up="upOption"  
				@down="downCallback" 
				@up="upCallback"
				@emptyclick="emptyClick"
			>
			
				<template v-for="(item,index) in pageList.info">
					<view :key="index" class="list-item-wrap">
						<view class="item-title code">订单编号：{{item.code}}</view>
						<view class="item-title">
							<view class="left-box">医院名称：</view>
							<view class="right-box">{{item.shopName}}</view>
						</view>
						<view class="item-title">
							<view class="left-box">预约项目：</view>
							<view class="right-box">
								<template v-for="(itemProject,indexProject) in item.project">
									<view :key="indexProject">
										<view class="image-text-box">
											<view class="image-content">
												<u-lazy-load class="image" height="60" width="60" border-radius="4"
													:image="$tools.showImg(itemProject.imgHead,200)"></u-lazy-load>
											</view>
											<view class="text-content">
												<view class="project-title">
													<text>{{itemProject.name}}</text>
													<text>{{itemProject.price}}</text>
												</view>
											</view>
										</view>
									</view>
								</template>
							</view>
						</view>
						
						<view class="item-title">
							<view class="left-box">预约人信息：</view>
							<view class="right-box">
								<view>{{item.agreePerName}}(
								<text v-if="item.agreeGender==1">男士</text>
								<text v-else-if="item.agreeGender==2">女士</text>
								<text v-else>没有预留性别</text>
								) </view>
								<view>电话：{{item.agreeContactTel}}</view>
								<view>身份证：{{item.agreeIdCard}}</view>
							</view>
						</view>
						
						<view class="item-title">
							<view class="left-box">陪诊时间：</view>
							<view class="right-box">
								<view>
									{{item.agreeTime}}
								</view>
								<view>
									<text class="time" v-for="(timeItem,timeIndex) in item.times" :key="timeIndex">
										{{timeItem}}
									</text>
								</view>
							</view>
						</view>
						
						<view class="item-title">
							<view class="left-box">订单信息：</view>
							<view class="right-box">
								<view>{{item.createTime}}(下单)</view>
								<view>{{item.expressTime}}(派发)</view>
							</view>
						</view>
						
						<view class="item-title state">
							<view class="left-box work">
								<text v-if="!item.state.service">未开始</text>
								<text v-else-if="item.state.over">已完成</text>
								<text v-else-if="item.state.service">服务中</text>
							</view>
							<view class="right-box job">
								<text @click="$shaken(orderAgree,item)" v-if="item.setEmployee.state==0 && !item.state.over">接受此单任务</text>
								<text @click="$shaken(orderOver,item)" v-if="item.setEmployee.state==1  && !item.state.over">完成订单</text>
							</view>
						</view>
					</view>
				</template>
			</mescroll-body>
			<!-- <view class="nodata" v-if="pageList.info.length >= 3">—— 到底啦 ——</view> -->
		<!-- </view> -->
		<ut-login-modal :colors="colors"></ut-login-modal>
		<!-- <loading :show="isShow"></loading> -->
	</ut-page>
</template>

<script>
	var app = getApp();
	import {mapState} from 'vuex'
	import MescrollBody from "@/components/mescroll-uni/mescroll-body/mescroll-body.vue";
	import MescrollMixin from "@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			MescrollBody,
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				colors: '',
				isShow:false,
				noClick: true,
				topWrapHeight: 0,
				upOption:{
					noMoreSize:5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty:{
						icon: "https://www.mescroll.com/img/mescroll-empty.png",
						tip: '~ 无订单数据 ~', // 提示
						// btnText: '去看看'
					}
				},
				pageReq: {
					type:0,
					pagesize: 20,
					pageindex: 1,
					key:'',
				},
				pageList: {
					info: [],
				},
				isEnd:false,

			}
		},
		onShow() {
			this.setData({colors: app.globalData.newColor});
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				userInfo: state => state.user.info,
			}),

		},
		onLoad: async function(options) {
			
		},
		methods: {
			getHeight(h){
				this.topWrapHeight=h
			},
			/*下拉刷新的回调 */
			downCallback() {
				this.pageReq.pageindex = 1
				this.pageList.info = []
				//加载列表数据
				this.mescroll.resetUpScroll()
			},
			upCallback(page) {
				this.isShow = false
				let parm = {
					commKey: this.commKey,
					type: this.pageReq.type,
					pageindex: this.pageReq.pageindex,
					pagesize: this.pageReq.pagesize,
					key: this.pageReq.key,
				}

				this.$ut.api("hospital/employee/order/list", parm).then(res => {
					// 需自行维护页码
					this.pageReq.pageindex++;
					// 先隐藏下拉刷新的状态
					this.mescroll.endSuccess(res.data.info.length)
					// 不满一页,说明已经无更多消息 (建议根据您实际接口返回的总页码数,总消息量,是否有消息的字段来判断)
					if(res.data.length < this.pageSize){
						this.isEnd = true; // 标记已无更多消息
						this.mescroll.lockDownScroll(true); // 锁定下拉
					}
					
	
					this.pageList.record = res.data.record;
					this.pageList.page = res.data.page;
					if (this.pageReq.pageindex == 1) this.pageList.info = []; //如果是第一页需手动制空列表
					this.pageList.info = this.pageList.info.concat(res.data.info);
					
			
				}).catch(e => {
					this.pageReq.pageindex--;
					this.mescroll.endErr();
				})
			},
			emptyClick(){
				
			},
			orderAgree(item){
				let that=this
				uni.showModal({
					title: '提示',
					content: '确定接受此单任务？',
					success: (res) => {
						if (res.confirm) {
							let param = {
								commKey: that.commKey,
								orderId: item.id,
							}
							that.$ut.api("hospital/employee/order/agree", param).then(res => {
								item.state.service=true
								item.setEmployee.state=1
										
							})
						} else if (res.cancel) {that.noClick=true}
					}
				});
			},
			orderOver(item){
				let that=this
				uni.showModal({
					title: '提示',
					content: '确定结束此订单任务？',
					success: (res) => {
						if (res.confirm) {
							let param = {
								commKey: that.commKey,
								orderId: item.id,
							}
							that.$ut.api("hospital/employee/order/over", param).then(res => {
								item.state.service=true
								item.setEmployee.state=2
										
							})
						} else if (res.cancel) {that.noClick=true}
					}
				});
			}
		}
	}
</script>
<style>
	.page{
		background-color: #fff;
	}
	
</style>
<style lang="scss" scoped>
	
	.search-warp{
		padding: 10rpx 20rpx;
		box-shadow: 0 1rpx 2rpx rgba(0,0,0,0.05);
		border-bottom: 1rpx solid rgba(0,0,0,0.05);;
	}
	

	.content-warp{
		padding: 12rpx;
	}
	.list-item-wrap{
		background-color: #fff;
		padding: 20rpx 30rpx;
		margin: 20rpx;
		font-size: 24rpx;
		border-radius: 8rpx;
		border:1rpx solid rgba(0,0,0,0.1);
		box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.15);
		transition: box-shadow 0.3s ease-in-out;
	}
	.list-item-wrap:last-child{
		margin-bottom: 0rpx;
	}
	
	
	.image-text-box{
		display: flex;
		width: 100%;
		align-items: center;
		.image-content{
			
		}
		.text-content{
			flex: 1;
			padding: 0 20rpx;
		}
	}
	
	.project-title{
		display: flex;
		justify-content: space-between;
	}
	
	.item-title{
		line-height: 2;
		display: flex;
		.left-box{
			width: 180rpx;
		}
		.right-box{
			flex: 1;
		}
	}
	
	.code{
		border-bottom: 1rpx dashed #ccc;
	}
	
	.state{
		border-top: 1rpx dashed #ccc;
		padding-top: 20rpx;
		.work{
			padding: 2rpx 20rpx;
			background-color: #ccc;
			border-radius: 40rpx;
			color: #fff;
			text-align: center;
		}
	}
	
	.job{
		text-align: center;
		
		text{
			
			color: #fff;
			border: 2rpx solid var(--colors);
			background-color: var(--colors);
			padding: 10rpx 40rpx;
			font-size: 28rpx;
			border-radius: 30rpx;
		}
	}
	
	.time{
		padding: 0 20rpx;
		border: 1rpx solid var(--colors2);
		border-radius: 30rpx;
		margin-right: 30rpx;
		&:last-child{
			margin-right: 0;
		}
	}
</style>
<template>
	<ut-page>
		<f-navbar :title="baseProjectInfo.name"></f-navbar>
		<view class="content-body ">
			<view class="title">
				<view class="image-box">
					<image style="width: 60rpx; height: 60rpx; background-color: #eeeeee;"
						:src="baseProjectInfo.imgHead"></image>
				</view>
				<view class="content">{{baseProjectInfo.name}}</view>
				
			</view>
			<view class="caption">
				<view>服务内容：</view>
				<view class="phone" :style="{backgroundColor:colors+'30'}" @click="callPhone">
					我要电话咨询<text class="cuIcon-phone"></text>
					</view>
			</view>
		</view>
		<view>
			<view class="detail">
				
				<view class="content">
					<template v-for="(item,index) in baseProjectDetail">
						<view class="imgs-box" v-if="item.background" :key="index">
							<u-lazy-load class="background" :image="$tools.showImg(item.background)"
								img-mode='widthFix'></u-lazy-load>
							<u-lazy-load class="image" :image="$tools.showImg(item.img,750)"></u-lazy-load>
						</view>
						<u-lazy-load class="image" v-if="!item.background" :image="$tools.showImg(item.img,750)"
							img-mode='widthFix'></u-lazy-load>
						<!-- <image class="image" :src="item.img" mode="widthFix"></image> -->
					</template>
				</view>
			</view>
		</view>
		<view class="page-footer">
			<view class="btn-box other">
				<view class="btn">
					<view class="tel" @click="callPhone">
						<view class="cuIcon-phone"></view>
						<view>电话咨询</view>
					</view>
					<button class="contact" open-type="contact" bindcontact="test" session-from="sessionFrom">
						<view class="cuIcon-peoplefill"></view>
						<view>在线客服</view>
					</button>
				</view>

			</view>
			<view class="btn-box refund" @click="refund">
				<view class="btn">
					<text class="cuIcon-refund"></text>
					立即下单
				</view>
			</view>
		</view>
	</ut-page>

</template>
<script>
	let app = getApp()
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex'

	export default {

		components: {},
		options: { //小程序样式穿透
			styleIsolation: 'shared'
		},
		data() {
			return {
				colors: '',
				phone:'',
				baseProjectId:'',
				baseProjectInfo:{},
				baseProjectDetail:[],
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
			}),
		},
		onLoad: async function(options) {
			await this.$onLaunched
			if(options.projectId) this.baseProjectId=options.projectId
			this.init()
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor
			})
		},
		methods: {
			init() {
				this.getBaseProjectInfo()
				this.getBaseProjectDetailList()
				this.getKfPhone()
			},
			getKfPhone(){
				this.$ut.api("hospital/setting/phone", {
					commKey:this.commKey,
				}).then(res => {
					this.phone=res.data.tel
				})
			},
			getBaseProjectInfo(){
				this.$ut.api("hospital/project/base/info", {
					commKey:this.commKey,
					id:this.baseProjectId,
				}).then(res => {
					this.baseProjectInfo=res.data
				})
			},
			getBaseProjectDetailList(){
				this.$ut.api("hospital/project/base/detail", {
					commKey:this.commKey,
					baseProjectId:this.baseProjectId,
				}).then(res => {
					this.baseProjectDetail=res.data
				})
			},
			callPhone(){
				if(!this.phone) return 
				uni.makePhoneCall({
					phoneNumber: this.phone //仅为示例
				});
			},
			refund(){
				this.$tools.routerTo('/pagesC/project/order-refund',{baseProjectId:this.baseProjectId})
			}
		},
	}
</script>
<style>
	.page{
		padding-bottom: 90rpx;
	}
</style>
<style lang="scss" scoped>
	.content-body {
		margin-top: 20rpx;
		background-color: #fff;
		border-radius: 10rpx;
	}

	.title {
		display: flex;
		align-items: center;

		.image-box {
			padding: 20rpx;
			padding-right: 50rpx;
		}

		.content {
			font-size: 32rpx;
			font-weight: bold;
		}
	}

	.caption {
		margin: 5rpx 20rpx;
		font-weight: bold;
		padding-bottom: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		.phone{
			font-weight: normal;
			font-size: 20rpx;
			border: 1rpx solid var(--colors);
			border-radius: 40rpx;
			padding: 4rpx 20rpx;
			color: var(--colors);
		}
	}

	.page-footer {
		width: 100%;
		height: 88rpx;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		box-shadow: 1rpx 2rpx 3rpx 4rpx rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 0 20rpx;
		justify-content: space-between;

		.btn-box {
			text-align: center;
		}

		.btn {
			background-color: var(--colors);
			border-radius: 80rpx;
			padding: 14rpx 20rpx;
			color: #fff;

		}

		.cuIcon-refund {
			margin-right: 10rpx;
		}

		// .tel,
		// .contact {
		// 	width: 140rpx;
		// 	font-size: 20rpx;
		// }
		.other{
			width:260rpx;
			font-size: 20rpx;
			
			.btn{
				position: relative;
				
				display: flex;
				flex-direction: row;
				justify-content: space-around;
				padding: 6rpx 20rpx;
				
				.tel::after{
					content: ' ';
					position: absolute;
					right: 0;
					left: 50%;
					top: 10rpx;
					height: 44rpx;
					border-left: 2rpx dashed #ccc;
				}
			}
			
			
		}

		.contact {
			background: none;
			padding: 0;
			margin: 0;
			line-height: inherit;
			font-size: inherit;
			font-weight: inherit;
			color: inherit;
			
			&::after{
				border: inherit;
			}
		}

		.refund {
			margin-left: 20rpx;
			flex: 1;
		}
	}
</style>
<template>
	<view class="top-list" :style="{top: navBarH + 'px'}">
		<view class="btn-item-first" @click="tabItem(topList[0].url)">
			<text class="iconfont" :class="topList[0].icon"></text>
		</view>
		<view class="flex">
			<template v-for="(item,index) in list">
				<view :key="index" class="btn-item" @click="tabItem(item.url)">
					<text class="iconfont" :class="item.icon"></text>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	
	export default {
		data(){
			return {
				navBarH: app.globalData.statusHeight+app.globalData.toBar,
				colors: '',
				topList: [
					{	id: '01',
						icon: 'icon-home',
						url: '/pages/index/index',
					},
					{	id: '02',
						icon: 'icon-code',
						url: '',
					},
					{	id: '03',
						icon: 'icon-gouwuche',
						url: '',
					},
					{	id: '04',
						icon: 'icon-home',
						url: '',
					}
				],
				list: []
			}
		},
		onLoad: async function(options) {
			await this.$onLaunched;
		},
		onShow: function () {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		created() {
			if(this.topList){
				this.list = this.topList.slice(1,this.topList.length)
			}
		},
		methods: {
			tabItem(url){
				// console.log(url);
				if(url == ''){
					return
				}
				this.$tools.routerTo(url)
			}
		}
	}
</script>

<style scoped lang="scss">
	.top-list {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		width: 100%;
		position: absolute;
		.btn-item {
			background: #534E4B;
			height: 50rpx;
			width: 50rpx;
			text-align: center;
			line-height: 50rpx;
			border-radius: 50%;
			margin-left: 20rpx;
			.iconfont {
				font-size: 26rpx;
				color: #fff;
			}
			&-first {
				background: #534E4B;
				opacity: 0.7;
				height: 50rpx;
				width: 50rpx;
				text-align: center;
				line-height: 50rpx;
				border-radius: 50%;
				.iconfont {
					font-size: 26rpx;
					color: #fff;
				}
			}
		}
	}
</style>
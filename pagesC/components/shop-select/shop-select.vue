<template>
	<view class="shop-list">
		<view class="img-price">
			<image :src="contentObj.img" mode="aspectFill"></image>
			<view>
				<view class="price">
					<text style="font-size: 24rpx;">￥</text>123<text style="font-size: 28rpx;">.00</text>
				</view>
				<view class="select-hos-time">选择：医院 时间</view>
			</view>
		</view>
		<view>
			<view style="margin: 30rpx 0;">医院</view>
			<view class="hos-flex">
				<template v-for="(item,index) in contentObj.hospitalList">
					<view :key="index" :class="item.active?'hospital-title-active':'hospital-title'" @click="selectHospitalItem(item)">{{ item.title }}</view>
				</template>
			</view>
		</view>
		<view>
			<view style="margin: 10rpx 0 30rpx;">时间</view>
			<view class="hos-flex" style="padding-bottom: 40rpx;">
				<template v-for="(item,index) in contentObj.times">
					<view :key="index" :class="item.active?'hospital-title-active':'hospital-title'" @click="selectTimeItem(item)">{{ item.time }}</view>
				</template>
			</view>
		</view>
		<view class="flex-between" style="padding-bottom: 30rpx;">
			<view>购买数量</view>
			<view>
				<!-- <stepper size="small" :min="1" :max="allowMax" :defaultValue="selectNum"
					:display="canBuy" @change="changeNum"></stepper> -->
				<stepper size="small" @change="changeNum" :defaultValue="selectNum"></stepper>
			</view>
		</view>
		<view class="flex-between">
			<view>预约日期</view>
			<view style="color: #999;font-size: 24rpx;" @click="selectTime">{{ dateValue || '请选择日期'}}</view>
		</view>
		
		<view class="fixed-btn">
			<view class="sure" @click="btnSure">确定</view>
		</view>
		
		<!-- 日期选择 -->
		 <u-datetime-picker
			:show="showTime"
			v-model="timeValue"
			:closeOnClickOverlay="true"
			ref="datetimePicker"
			mode="date"
			:formatter="formatter"
			@close="closeDatePicker"
			@confirm="dateSure"
			@cancel="closeDatePicker"
		></u-datetime-picker>
	</view>
</template>

<script>
	let app = getApp();
	import stepper from './stepper.vue';
	
	export default {
		components: {
			stepper
		},
		data(){
			return {
				colors: '',
				showTime: false,
				selectNum: 1, //选中数量
				timeValue: '',
				dateValue: '',//日期
				// itemObj: {
				// 	hospitalName: '',
				// 	time: '',
				// 	date: '',
				// 	num: ''
				// },
				contentObj: {
					img: 'https://oss.afjy.net/api/file/preview?file=7BERovb',
					price: '188',
					hospitalList: [
						{
							id: '01',
							title: '云南省第一人民医院(昆华医院)'
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '01',
							title: '云南省第一人民医院(昆华医院)',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '01',
							title: '云南省第一人民医院(昆华医院)',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '01',
							title: '云南省第一人民医院(昆华医院)',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '01',
							title: '云南省第一人民医院(昆华医院)',
						},
						{
							id: '02',
							title: '昆明医学院',
						},
						{
							id: '02',
							title: '昆明医学院',
						}
					],
					times: [
						{
							id: '01',
							time: '上午 8:00~12:00'
						},
						{
							id: '02',
							time: '下午 13:00~18:00'
						}
					]
				}
			}
		},
		onLoad: async function(options) {
			await this.$onLaunched;
		},
		onReady() {
			// 微信小程序需要用此写法
			this.$refs.datetimePicker.setFormatter(this.formatter)
		},
		onShow: function () {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		created() {
		},
		methods: {
			selectHospitalItem(item){
				if(item.active){
					this.$set(item,'active',false)
				}else{
					this.$set(item,'active',false)
					let temp = {}
					let obj = this.contentObj.hospitalList.find(v => v.active)
					if(obj != undefined){
						temp = obj
					}
					temp.active = false
					item.active = true
				}
				// this.itemObj.hospitalName = item.title
			},
			selectTimeItem(item){
				if(item.active){
					this.$set(item,'active',false)
				}else{
					this.$set(item,'active',false)
					let temp = {}
					let obj = this.contentObj.times.find(v => v.active)
					if(obj != undefined){
						temp = obj
					}
					temp.active = false
					item.active = true
				}
				// this.itemObj.time = item.time
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			selectTime(){
				this.showTime = true
			},
			closeDatePicker(){
				this.showTime = false
			},
			dateSure(e){
				const date = new Date(e.value)
				const year = date.getFullYear()
				const month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
				const day = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())
				this.dateValue = `${year}年${month}月${day}日`
				// this.itemObj.date = this.dateValue
				this.showTime = false
			},
			btnSure(){
				this.$emit('submit')
				// console.log(this.itemObj);
			},
			changeNum(val) {
				this.selectNum = parseInt(val);
				// this.itemObj.num = this.selectNum
			},
		}
	}
</script>

<style scoped lang="scss">
	.shop-list {
		height: 100%;
		width: 100%;
		padding: 40rpx 40rpx 100rpx;
	}
	.img-price {
		display: flex;
		align-items: center;
		image {
			height: 100px;
			width: 100px;
			border-radius: 10rpx;
			margin-right: 30rpx;
		}
		.price {
			color: red;
			font-weight: 600;
			font-size: 40rpx;
			margin-bottom: 4rpx;
		}
		.select-hos-time {
			font-size: 24rpx;
			color: #999;
		}
	}
	
	.hos-flex {
		display: flex;
		flex-wrap: wrap;
		.hospital-title {
			display: inline-block;
			background: #F2F2F2;
			font-size: 24rpx;
			border-radius: 50rpx;
			padding: 10rpx 30rpx;
			margin-bottom: 20rpx;
			margin-right: 20rpx;
			border: 1px solid transparent;
		}
		.hospital-title-active {
			display: inline-block;
			background: #F2F2F2;
			font-size: 24rpx;
			border-radius: 50rpx;
			padding: 10rpx 30rpx;
			margin-bottom: 20rpx;
			margin-right: 20rpx;
			border: 1px solid #EB040E;
			color: #EB040E;
		}
	}
	
	.flex-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}
	.fixed-btn {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 10;
		padding: 10rpx 24rpx;
		background: #fff;
		.sure {
			background: #EB040E;
			color: #fff;
			border-radius: 50rpx;
			text-align: center;
			padding: 20rpx 0;
			letter-spacing: 2rpx;
		}
	}
</style>
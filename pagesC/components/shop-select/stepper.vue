<template>
	<view class="stepper-wrapper" :class="size === 'small' ? 'small' : ''">
		<view class="reduce" @tap="reduce" :class="{ noclick: minusDisabled }"></view>
		<view class="num-wrapper"><input class="num-content" type="number" v-model="currentValue" @blur="onBlur" @input="onInput" :disabled="display" /></view>
		<view class="add" :class="{ noclick: plusDisabled }" @tap="add"></view>
	</view>
</template>

<script>
export default {
	props: {
		size: {
			type: String
		},
		min: {
			type: [Number, String],
			default: 1
		},
		max: {
			type: [Number, String],
			default: Infinity
		},
		defaultValue: {
			type: [Number, String],
			default: 1
		},
		display: {
			type: [Boolean, String],
			default: false
		}
	},
	data() {
		const value = this.defaultValue;
		return {
			currentValue: value
		};
	},
	watch: {
		defaultValue(newValue) {
			this.currentValue = newValue;
		},
		currentValue(newValue) {
			this.$emit('change', this.currentValue);
		}
	},
	computed: {
		minusDisabled() {
			return this.display || this.currentValue <= this.min;
		},

		plusDisabled() {
			return this.display || this.currentValue >= this.max;
		}
	},
	methods: {
		format(value) {
			value = String(value).replace(/[^0-9.-]/g, '');
			return value === '' ? 1 : Math.floor(value);
		},
		onBlur(event){
			let value = event.detail.value;
			this.currentValue = this.format(value) > this.max ? parseInt(this.max) : this.format(value);
		},
		onInput(event) {
			// let value = event.detail.value;
			// setTimeout(() => {
			// 	this.currentValue = this.format(value) > this.max ? parseInt(this.max) : this.format(value);
			// }, 0);
		},
		reduce() {
			if (!this.minusDisabled) {
				this.currentValue--;
			}
		},
		add() {
			if (!this.plusDisabled) {
				this.currentValue++;
			}
		}
	}
};
</script>

<style lang="less">
.stepper-wrapper {
	display: inline-flex;
	flex-direction: row;
	flex-flow: wrap;
	// border: 1rpx solid #ccc;
	// border-radius: 40rpx;

	&.small {
		.num-wrapper {
			width: 80rpx;
			height: 44rpx;
			margin: 0 10rpx;
		}
		.add {
			width: 44rpx;
			height: 44rpx;
			background: #EB040E;
			border-radius: 50%;
			color: #fff;
		}
		
		.reduce {
			width: 44rpx;
			height: 44rpx;
			border: 2px solid rgba(0, 0, 0, 0.1);
			border-radius: 50%;
			color: #fff;
		}

		.reduce {
			&::after {
				width: 18rpx;
			}
		}

		.add {
			&::before,
			&::after {
				width: 18rpx;
			}
		}
		.num-wrapper {
			//border-radius: 10rpx;
			overflow: hidden;

			.num-content {
				width: 100%;
				height: 100%;
				background-color: #f4f4f4;
				min-height: 44rpx; //修改input默认样式
				line-height: 44rpx;
				font-size: 28rpx;
				text-align: center;
			}
		}
	}

	.reduce,
	.num-wrapper,
	.add {
		width: 100rpx;
		height: 56rpx;
		position: relative;
	}

	.reduce {
		&::after {
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			width: 28rpx;
			height: 4rpx;
			border-radius: 2rpx;
			background-color: #333333;
			margin: auto;
		}
		&.noclick {
			&::after {
				background-color: #cccccc;
			}
		}
	}

	.num-wrapper {
		//border-radius: 10rpx;
		overflow: hidden;

		.num-content {
			width: 100%;
			height: 100%;
			background-color: #f4f4f4;
			min-height: 56rpx; //修改input默认样式
			line-height: 56rpx;
			font-size: 30rpx;
			text-align: center;
		}
	}

	.add {
		&::before,
		&::after {
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			width: 28rpx;
			height: 4rpx;
			border-radius: 2rpx;
			background-color: #fff;
			margin: auto;
		}

		&::after {
			transform: rotate(90deg);
		}

		&.noclick {
			&::before,
			&::after {
				background-color: #cccccc;
			}
		}
	}
}
</style>

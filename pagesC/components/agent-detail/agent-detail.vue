<template>
	<view class="agent-list">
		<view class="detail-top">
			<view class="tit-left"></view>
			<view class="agent-title">详情</view>
			<view class="tit-right"></view>
		</view>
		<view>
			<template v-if="details.length > 0" v-for="(item,index) in details">
				<u-lazy-load class="image" :image="$tools.showImg(item.img,750)" img-mode="aspectFill"></u-lazy-load>
			</template>
		</view>
	</view>
</template>

<script>
	let app = getApp();
	
	export default {
		data(){
			return {
				colors: '',
				details: [
					{
						img: 'https://img0.baidu.com/it/u=3449744615,1420716012&fm=253&fmt=auto&app=138&f=JPEG?w=431&h=300'
					}
				]
			}
		},
		onLoad: async function(options) {
			await this.$onLaunched;
		},
		onShow: function () {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		created() {

		},
		methods: {

		}
	}
</script>

<style scoped lang="scss">
	.agent-list {
		background: #fff;
		padding: 0 24rpx 20rpx;
		/* #ifndef H5 */
		margin-bottom: 66px;
		/* #endif */
	}
	.detail-top {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx 100rpx;
		.tit-left {
			position: relative;
			height: 4rpx;
			width: 100%;
			background: #E6E6E6;
		}
		.agent-title {
			font-size: 30rpx;
			margin: 0 30rpx;
			white-space: nowrap;
			// color: #000;
			// font-weight: 600;
		}
		.tit-right {
			position: relative;
			height: 4rpx;
			width: 100%;
			background: #E6E6E6;
		}
	}
	.tit-left::after {
		position: absolute;
		content: '';
		right: 0;
		top: -2rpx;
		width: 10rpx;
		height: 10rpx;
		background: #E6E6E6;
		transform: rotate(45deg);
	}
	.tit-right::before {
		position: absolute;
		content: '';
		left: 0;
		top: -2rpx;
		width: 10rpx;
		height: 10rpx;
		background: #E6E6E6;
		transform: rotate(45deg);
	}
	// .image{
	// 	width: 100%;
	// 	min-height: 200rpx;
	// }
</style>
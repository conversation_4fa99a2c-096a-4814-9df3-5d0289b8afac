<template>
	<view class="back" :style="[backstyle()]">
		<ut-banner :detail="datail" @getIndex="getIndex" :showDots="showDots"></ut-banner>
	</view>
</template>

<script>
	import utBanner from './ut-banner.vue'
	export default {
		name: "utBannerDer",
		components:{
			utBanner
		},
		data() {
			return {
				imgCurrIndex: 0,
				showDots:false // 是否显示指示点
			};
		},
		props: {
			datail: {
				type: Array,
				default: function() {
					return []
				}
			}
		},
		methods: {
			getIndex(index) {
				this.imgCurrIndex = index
			},
			backstyle() {
				return {
					backgroundImage: this.getImageUrl()
				}
			},
			getImageUrl() {
				if(this.datail.length == 0){
					return "";
				}
				const url = this.datail[this.imgCurrIndex].backimage || ''
				return `url(${url})`
			}
		}
	}
</script>

<style lang="less">
	.back {
		padding: 20rpx 0;
		box-sizing: border-box;
		transition: all 0.6s ease-in-out 0s;
		background-size: 100% 100%;
		overflow-x: hidden;
	}
</style>

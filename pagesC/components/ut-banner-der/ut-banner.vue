<template>
	<!-- 轮播 -->
	<view class="banner-swiper-box" v-if="detail">
		<canvas canvas-id="colorThief" class="hide-canvas"></canvas>
		<swiper class="banner-carousel shopro-selector-rect" circular @change="swiperChange" :autoplay="true">
			<swiper-item v-for="(item, index) in detail" :key="index" class="carousel-item " @tap="routerTo(item.path)">
				<image class="swiper-image " :src="$tools.showImg(item.image,750)" mode="aspectFit" lazy-load></image>
			</swiper-item>
		</swiper>
		<view class="banner-swiper-dots">
			<text :class="swiperCurrent === index ? 'banner-dot-active' : 'banner-dot'" v-for="(dot, index) in detail.length" :key="index"></text>
		</view>
	</view>
</template>

<script>
import colorThief from 'miniapp-color-thief';
export default {
	components: {},
	data() {
		return {
			swiperCurrent: 0, //轮播下标
			webviewId: 0,
		};
	},
	props: {
		detail: {
			type: Array,
			default: null
		},
		showDots:{
			type:Boolean,
			default:true
		}
	},
	computed: {},
	created() {
		this.getIndex(0)
	},
	methods: {
		// 轮播切换
		swiperChange(e) {
			this.swiperCurrent = e.detail.current;
			this.getIndex(this.swiperCurrent)
		},
		getIndex(index){
			this.$emit('getIndex', index);
		},
		routerTo(path){
			if(path == ''){
				return
			}
			this.$tools.routerTo(path)
		}
	}
};
</script>

<style lang="scss">
.hide-canvas {
	position: fixed !important;
	top: -99999upx;
	left: -99999upx;
	z-index: 1;
}

// 轮播
.banner-swiper-box {
	// background: #fff;
}

.banner-swiper-box,
.banner-carousel {
	width: 750upx;
	height: 650upx;
	position: relative;

	.carousel-item {
		width: 100%;
		height: 100%;
		// padding: 0 28upx;
		overflow: hidden;
	}

	.swiper-image {
		width: 100%;
		height: 100%;
		// border-radius: 10upx;
		// background: #ccc;
	}
}

.banner-swiper-dots {
	display: flex;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	bottom: 0rpx;
	z-index: 66;

	.banner-dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.2);
		margin: 0 10rpx;
	}

	.banner-dot-active {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		background: #000;
		margin: 0 10rpx;
	}
}
</style>

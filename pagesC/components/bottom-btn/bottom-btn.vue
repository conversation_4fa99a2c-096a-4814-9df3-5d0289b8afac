<template>
	<view>
		<view class="bottom-box">
			<view class="bottom-box-left">
				<view class="bottom-box-left-item">
					<text class="iconfont icon-shopping shake animated" @click="shop()"></text>
					<text class="text" size="40">店铺</text>
				</view>
				<view class="bottom-box-left-item" @click="myCollect()">
					<text class="iconfont icon-pentagram"></text>
					<text class="text">收藏</text>
				</view>
				<view class="bottom-box-left-item" @click="jumpCar()">
					<text class="iconfont icon-gouwuche"></text>
					<text class="text">购物车</text>
				</view>
			</view>
			<view class="bottom-box-right">
				<view class="bottom-box-right-item" style="background: #05CFFF;border-radius: 30rpx 0 0 30rpx;" @click="joinCar()">加入购物车</view>
				<view class="bottom-box-right-item" style="background: #008EFC;border-radius: 0 30rpx 30rpx 0;margin-right: 30rpx;" @click="onBuy()">立即购买</view>
	<!-- 			<view class="bottom-box-right-item"
					style="background: #008EFC;border-radius: 30rpx 30rpx 30rpx 30rpx;width: 380rpx; margin-right: 30rpx;" @click="onBuy()">
					立即购买</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			colors: {
				type: String
			},
			isCollect:{
				type:Boolean,
				default:false
			},
			type:{
				type:Number,
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			onBuy() {
				this.$emit('onBuy')
			},
			joinCar() {
				this.$emit('joinCar')
			},
			jumpCar() {
				this.$emit('jumpCar')
			},
			myCollect() {
				this.$emit('myCollect')
			},
			shop(){
				this.$emit('myShop')
			}
		}
	}
</script>

<style scoped lang="scss">
	.bottom-box {
		width: 100%;
		height: 100rpx;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;

		&-left {
			flex: 1;
			display: flex;
			align-content: center;
			justify-content: space-around;

			&-item {
				flex: 1;
				text-align: center;
				color: #333;

				.iconfont {
					font-size: 40rpx;
					// font-weight: bold;
				}

				.text {
					display: inline-block;
					width: 100%;
					font-size: 20rpx;
					text-align: center;
					padding-top: 5rpx;
				}
			}

		}

		&-right {
			flex: 1.5;
			display: flex;
			justify-content: flex-end;
			height: 100%;
			padding: 10rpx 0;

			&-item {
				color: #fff;
				height: inherit;
				font-size: 30rpx;
				letter-spacing: 4rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				white-space: nowrap;
				padding: 0 20rpx;
			}
		}
	}

	.shake {
		-webkit-animation-name: shake;
		animation-name: shake
	}

	@-webkit-keyframes shake {

		0%,
		100% {
			-webkit-transform: translate3d(0, 0, 0);
			transform: translate3d(0, 0, 0)
		}

		10%,
		30%,
		50%,
		70%,
		90% {
			-webkit-transform: translate3d(-10px, 0, 0);
		 transform: translate3d(-10px, 0, 0)
		}

		20%,
		40%,
		60%,
		80% {
			-webkit-transform: translate3d(10px, 0, 0);
			transform: translate3d(10px, 0, 0)
		}
	}

	@keyframes shake {

		0%,
		100% {
			-webkit-transform: translate3d(0, 0, 0);
			transform: translate3d(0, 0, 0)
		}

		10%,
		30%,
		50%,
		70%,
		90% {
			-webkit-transform: translate3d(-10px, 0, 0);
			transform: translate3d(-10px, 0, 0)
		}

		20%,
		40%,
		60%,
		80% {
			-webkit-transform: translate3d(10px, 0, 0);
			transform: translate3d(10px, 0, 0)
		}
	}

	.animated {
		-webkit-animation-duration: 1s;
		animation-duration: 1s;
		-webkit-animation-fill-mode: both;
		animation-fill-mode: both
	}
</style>

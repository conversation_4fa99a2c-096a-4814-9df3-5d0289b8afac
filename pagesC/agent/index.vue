<template>
	<ut-page class="page" :style="{'--colors':colors,'--colors2':colors+'60','--colors3':colors+'90'}">
		<f-navbar title="代办问诊" fontColor="#000" bgColor="#fff" navbarType="2" @leftClick="leftClick"></f-navbar>
		<!-- 轮播图 -->
		<ut-banner-der :datail="swiperData"></ut-banner-der>
		
		<view class="price-share">
			<view class="price-share-item">
				<view class="price">
					<text style="font-size: 24rpx;">￥</text>123<text style="font-size: 28rpx;">.00</text>
				</view>
				<view class="share" @click="myShare">
					<u-icon name="share" size="20" color="#999"></u-icon>
					<text>分享</text>
				</view>
			</view>
			<view class="content">代办问诊</view>
		</view>
		<view class="shop-select" @click="showSku = true">
			<view>
				<text>选择</text>
				<text style="margin-left: 40rpx;color: #999;font-weight: normal;">请选择商品规格</text>
			</view>
			<view>
				<text class="iconfont icon-right"></text>
			</view>
		</view>
		<view class="shop-remark">
			商品评价<text>（0）</text>
		</view>
		
		<!-- 详情 -->
		<agent-detail></agent-detail>
		<!-- 顶部按钮 -->
		<top-btn></top-btn>
		<!-- 购买/加入购物车商品规格选择 -->
		<u-popup :customStyle="{maxHeight: '540px',overflow: 'scroll'}" :show="showSku" :closeable="true" @close="closePopup">
			<shop-select @submit="submit"></shop-select>
		</u-popup>
		
		<!-- <ut-login-modal :colors="colors"></ut-login-modal> -->
		
		<loading :show="isShow"></loading>
		
		<!-- <ut-position :isTop="scrollTop>=700?true:false" @goTop="goTop"></ut-position> -->
		
		<!-- 分享 -->
		<shopro-share v-model="showShare"></shopro-share>
		
		<!-- 底部按钮 -->
		<view class="fixed">
	<!-- 		<bottom-btn :type="goodsData.type" @joinCar="joinCar()" @onBuy="onBuy()" @myCollect="myCollect()"
				@jumpCar="jumpCar()" @myShare="myShare()" :isCollect="goodsData.isCollect" :colors="colors">
			</bottom-btn> -->
			<bottom-btn @joinCar="joinCar()" @onBuy="onBuy()" @myCollect="myCollect()"
				@jumpCar="jumpCar()" @myShop="myShop()" :colors="colors">
			</bottom-btn>
		</view>
	</ut-page>
</template>

<script>
	let app = getApp();
	import { mapState } from 'vuex'
	import topBtn from '../components/top-btn/top-btn.vue'
	import utBannerDer from '../components/ut-banner-der/ut-banner-der.vue'
	import agentDetail from '../components/agent-detail/agent-detail.vue'
	import bottomBtn from '../components/bottom-btn/bottom-btn.vue'
	import shopSelect from '../components/shop-select/shop-select.vue'
	import shoproShare from '../components/shopro-share/shopro-share'
	// import utPosition from '../components/ut-position/ut-position.vue'
	
	export default {
		components: {
			utBannerDer,
			bottomBtn,
			topBtn,
			agentDetail,
			shopSelect,
			shoproShare,
			// utPosition
		},
		data() {
			return {
				colors: '',
				showShare: false,
				isShow: false,
				showSku: false
			}
		},
		onLoad: async function(options) {
			await this.$onLaunched;
		},
		onShow: function () {
			this.setData({
				colors: app.globalData.newColor
			});
		
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				userInfo: state => state.user.info,
				swiperData: state => state.init.template.swiper,
			}),
		},
		methods: {
			leftClick(){
				uni.navigateBack();
			},
			joinCar() {
				// if (!this.userInfo) {
					// this.$store.commit('LOGIN_TIP', true)
				// } else {
					console.log('加入购物车');
					this.showSku = true
					// this.getGoodsSkuInfo()
					// this.skuType = 1
					// if (this.goodsData.classShowMode == 0) {
					// 	this.showSku = true
					// } else {
					// 	if (this.itemId) {
					// 		this.addCar(this.itemId)
					// 	} else {
					// 		// uni.showToast({
					// 		// 	title: '请选择商品',
					// 		// 	icon: 'none'
					// 		// })
					// 		this.getGoodsSkuInfo()
					// 		this.skuType = 1
					// 		this.showSku = true
					// 	}
					// }
				// }
			},
			//立即购买
			onBuy() {
				// if (this.inventory < 0) {
				// 	uni.showToast({
				// 		title: '已售馨',
				// 		icon: 'none'
				// 	})
				// 	return
				// }
				// if (!this.userInfo) {
					// this.$store.commit('LOGIN_TIP', true)
				// } else {
					console.log('立即购买');
					this.showSku = true
					// if (this.goodsData.classShowMode == 0) {
					// 	this.getGoodsSkuInfo()
					// 	this.skuType = 2
					// 	this.showSku = true
					// } else {
					// 	if (this.itemId) {
					// 		let _this = this
					// 		uni.navigateTo({
					// 			url: '/pagesG/pay/payGoods',
					// 			events: {
					// 				/// 添加一个监听器，名称为acceptDataFromOpenedPage，用于获取被打开页面传送到当前页面的数据
					// 				///     B页面test.vue，使用eventChannel.emit('acceptDataFromOpenedPage', { data: 'test' }); 触发此监听函数
					// 				acceptDataFromOpenedPage: function(data) {
					// 					console.log(data);
					// 				},
					// 				// 同上
					// 				someEvent: function(data) {
					// 					console.log(data);
					// 				}
					// 			},
					// 			// 接口调用成功的回调函数，即跳转到B页面 test.vue 后的回调函数
					// 			success: function(res) {
					// 				// 通过eventChannel向被打开页面传送数据
					// 				res.eventChannel.emit('acceptDataFromOpenerPage', [{
					// 					id: _this.itemId,
					// 					count: 1
					// 				}]);
					// 			}
					// 		})
					// 	} else {
					// 		// uni.showToast({
					// 		// 	title: '请选择商品',
					// 		// 	icon: 'none'
					// 		// })
					// 		this.getGoodsSkuInfo()
					// 		this.skuType = 2
					// 		this.showSku = true
					// 	}
					// }
					//this.$tools.routerTo('/pagesG/pay/payment', this.params)
				// }
			},
			// 收藏
			myCollect() {
				console.log('我的收藏');
				// let params = {
				// 	commKey: this.commKey,
				// 	goodsId: this.goodsId
				// }
				// this.$ut.api('mall.myGoods.addCollect', params).then(res => {
				// 	if (res.data) {
				// 		uni.showToast({
				// 			title: "收藏成功",
				// 			icon: 'none'
				// 		})
				// 	} else {
				// 		uni.showToast({
				// 			title: "取消成功",
				// 			icon: 'none'
				// 		})
				// 	}
				// 	this.goodsData.isCollect = !this.goodsData.isCollect
				// })
			},
			jumpCar() {
				// if (!this.userInfo) {
				// 	this.$store.commit('LOGIN_TIP', true)
				// } else {
					console.log('购物车');
					// this.$tools.routerTo("/pagesG/car/car");
				// }
			},
			//店铺
			myShop() {
				console.log('店铺');
			},
			myShare(){
				console.log('分享');
				this.showShare = true
			},
			closePopup(){
				this.showSku = false
			},
			submit(){
				console.log('确定');
				this.showSku = false
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		padding-bottom: 120rpx;
	}
	.fixed {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 89;
	}
	.price-share {
		background: #fff;
		margin-bottom: 20rpx;
		&-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0 50rpx; 
			.price {
				color: red;
				font-weight: 600;
				font-size: 40rpx;
				padding-left: 24rpx;
			}
			.share {
				display: flex;
				align-items: center;
				background: #F7F7F7;
				color: #999;
				padding: 8rpx 24rpx;
				border-radius: 50rpx 0 0 50rpx;
			}
		}
		.content {
			font-size: 30rpx;
			font-weight: 600;
			padding: 0 24rpx 80rpx;
		}
	}
	.shop-select {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #fff;
		margin-bottom: 20rpx;
		padding: 30rpx 24rpx;
		font-weight: 600;
		.iconfont {
			font-size: 20rpx;
			color: #999;
		}
	}
	.shop-remark {
		background: #fff;
		padding: 30rpx 24rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		font-size: 30rpx;
		font-weight: 600;
	}
</style>

/**
 *  全局配置文件
 */

// #ifdef H5
let protocol = window.location.protocol;
// #endif

const ENV_COMMKEY = {
	development: 'homecare', //开发环境
	production: 'homecare', //生产环境
}

const EVN_INICOLOR='#8dc63f'

const ENV_APP_TITLE='先施康养'

const EVN_QQ_MAP_KEY='ZGOBZ-RLT6K-YE3JF-AKVOM-YVEBJ-TOBO7'
const EVN_FUZZY_LOCATION=false

const ENV_BASE_URL = {
	development: 'http://192.168.0.109:8080', //开发环境
	production: 'http://test.afjy.net', //生产环境
}

const ENV_API_URL = {
	// development: `https://ut.afjy.net:1443`, //开发环境 47.108.193.128 183.224.74.90
	development:`https://ky.afjy.net:8092`,
	production: `https://ky.afjy.net:8092`, //生产环境
}

module.exports = {
	BASE_URL : ENV_BASE_URL[process.env.NODE_ENV || 'development'],
	API_URL : ENV_API_URL[process.env.NODE_ENV || 'development'],
	COMMKEY : ENV_COMMKEY[process.env.NODE_ENV || 'development'],
	INICOLOR: EVN_INICOLOR,
	APP_TITLE: ENV_APP_TITLE,
	QQ_MAP_KEY:EVN_QQ_MAP_KEY,
	FUZZY_LOCATION:EVN_FUZZY_LOCATION,
	HAS_LIVE : false,
	contentType: 'application/json;charset=UTF-8',
	// 最长请求时间
	requestTimeout: 10000,
	// 操作正常code，支持String、Array、int多种类型
	successCode: [200, 0, '200', '0'],
	// 数据状态的字段名称
	statusName: 'code',
	// 状态信息的字段名称
	messageName: 'message',
}

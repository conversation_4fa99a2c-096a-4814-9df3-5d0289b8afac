<script>
	import request from '@/common/request'
	import store from '@/common/store/index.js'
	import platform from '@/common/platform'
	import {
		mapMutations,
		mapActions,
		mapState
	} from 'vuex';
	import {
		setConfig,
		getConfig
	} from '@/common/store/modules/initInfo.js'
	import {COMMKEY,INICOLOR} from '@/config'
	// import myconfig from "./common/thems/index.js";
	// import pageAnimation from './components/page-animation'
	let colors = getConfig();
	let nowColor = ''
	if (colors) {
		nowColor = colors.color
	}

	export default {
		// mixins: [notify],
		
		globalData: {
			nfc:null,
			userInfo: null,
			header:64,
			statusHeight: 0, //状态栏高度
			toBar: 44, //标题栏高度
			newColor: nowColor || INICOLOR, //小程序主题颜色
			// config: myconfig.themeList //主题图标
		},
		onLaunch: async function() {
			
			let that = this
			// // #ifdef MP-WEIXIN
			// uni.getSetting({
			// 	success: res => {
			// 		if (res.authSetting['scope.userInfo']) {
			// 			// 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框
			// 			uni.getUserInfo({
			// 				success: res => {
			// 					// 可以将 res 发送给后台解码出 unionId
			// 					this.globalData.userInfo = res.userInfo; // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
			// 					// 所以此处加入 callback 以防止这种情况
			// 					if (this.userInfoReadyCallback) {
			// 						this.userInfoReadyCallback(res);
			// 					}
			// 				}
			// 			});
			// 		}
			// 	}
			// });
			// // #endif

			await uni.getSystemInfo({ // 获取手机状态栏高度
				complete: res => {
					console.log(res)
					that.globalData.statusHeight = res.statusBarHeight;
					if (res.osName === "ios") {
						store.commit('statusHeight', res.statusBarHeight);
					}else{
						store.commit('statusHeight', res.statusBarHeight+2);
					}
					that.globalData.toBar = 44;
					
					that.globalData.statusHeight=5
					// #ifndef MP
					var ua = window.navigator.userAgent.toLowerCase();

					if (ua.match(/micromessenger/i) == 'micromessenger') {
						if (res.osName === "ios") {
							that.globalData.toBar = 48;
						} else if (res.osName === "android") {
							that.globalData.toBar = 46;
						}
					}
					// #endif
					that.globalData.header =parseInt(that.globalData.statusHeight)+parseInt(that.globalData.toBar)
				}
			});

			// 设置默认主题颜色
			let config = getConfig();
			if (config && config.color !== '') {
				//如果存在设置的主题 使用设置的主题
				uni.setTabBarStyle({
					selectedColor: config.color
				});
				let tabList = config.tabList;
				if (config.tabList) {
					for (var i = 0; i < tabList.length; i++) {
						let img = tabList[i];
						uni.setTabBarItem({
							//设置tabBar 首页图标
							index: i,
							selectedIconPath: img
						});
					}
				}
			} else {
				//如果不存在主题 使用默认主题
				let fristColor = {};
				fristColor.color = INICOLOR;
				fristColor.name = "olive";
				setConfig(fristColor);
				uni.setTabBarStyle({
					selectedColor: INICOLOR
				});
			}

			//设置公共key(很重要！！！)
			this.setCommKey(COMMKEY)
			
			this.getAppInfo().then(r=>{
				//加载微信浏览器配置
				// #ifdef H5
				if(platform.get() === 'wxOfficialAccount') {
					
					//加载微信相关
					// #ifdef H5
					this.getWeChat()
					// #endif
					this.$wxsdk.initJssdk(this.commKey, this.realAuthUrl)
				}
				// #endif
			})
			
			
			//加载OSS地址
			this.getUpload()
			
			//加载我的信息
			if(this.token){
				await this.getMyInfo().catch(()=>{
					this.$isResolve()
				})
			}
			
			//ws通信准备
			this.socketInit('/ws/ChatHub')
			if (that.userInfo) {
				that.websocket.on('connected', function(res) {
					let param = {
						id: that.userInfo.id,
						nickname: that.userInfo.nickname,
						phone: that.userInfo.phone,
						headimg: that.userInfo.headimgurl,
						gender: that.userInfo.gender,
					}
					that.websocket.invoke('setMyInfo', param)
				})
			}
			//接收信息绑定
			this.websocket.on('message', function(res) {
				switch (res.type) {
					case "notify":
						let params = {
							inApp: false, // app内横幅提醒
							voice: true, // 声音提醒
							vibration: true, // 振动提醒
							messageType: res.context.type,
							messageTitle: res.context.title,
							messageContent: res.context.content,
							messageImage: res.context.image
						}
						console.log(that.$appPush)
						that.$appPush(params)
						break;
					case "notify_audio":
						that.$audio.play(res.context.url)
						break;
				}
			
				console.log(res)
			});
			this.websocket.on('logMessage', function(res) {
				switch (res.type) {
					case 'setMyInfo':
						that.websocket.invoke('joinRoom', 'room')
						// that.joinRoom()
						break;
				}
			})
			
			// await this.getTemplate().then().catch(res=>{
			// 	console.log(res)
			// })

			this.$isResolve()
			/**
			 * 模拟获取购物车的数量 getCart
			 */
			// let cart = getCart()
			// let length = ''
			// if (cart && getToken()) {
			// 	length = cart.length
			// 	uni.setTabBarBadge({
			// 		//给tabBar添加角标
			// 		index: 2,
			// 		text: String(length)
			// 	});
			// }
		},
		computed: {
			...mapState({
				userInfo: state => state.user.info,
				websocket: state => state.websocket.socketTask,
				commKey: state => state.init.template.commKey,
				realAuthUrl: state => state.init.realAuthUrl,
				token: state => state.user.token,
			}),
		},
		methods: {
			...mapActions(['getTemplate','getAppInfo', 'getMyInfo', 'setCommKey', 'getWeChat', 'socketInit', 'setOss','getUpload']),
			joinRoom() {
				let that = this
				that.websocket.invoke('joinRoom', 'room')
			},
		}
	};


	// #ifdef H5
	//设置公众号的字段不受微信影响
	(function() {
		if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
			handleFontSize();
		} else {
			if (document.addEventListener) {
				document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
			} else if (document.attachEvent) {
				document.attachEvent("WeixinJSBridgeReady", handleFontSize);
				document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
			}
		}

		function handleFontSize() {
			// 设置网页字体为默认大小
			WeixinJSBridge.invoke('setFontSizeCallback', {
				'fontSize': 0
			});
			// 重写设置网页字体大小的事件
			WeixinJSBridge.on('menu:setfont', function() {
				WeixinJSBridge.invoke('setFontSizeCallback', {
					'fontSize': 0
				});
			});
		}
	})();
	// #endif
</script>
<style lang="scss">
	//不要使用scope，整个程序尽可能使用公共样式
	@import "@/components/uview-ui/index.scss";
	@import "@/common/css/app.scss";
	@import "@/common/css/ut.scss";
	body {
		/* IOS禁止微信调整字体大小 */
		-webkit-text-size-adjust: 100% !important;
		text-size-adjust: 100% !important;
		-moz-text-size-adjust: 100% !important;
	}
</style>
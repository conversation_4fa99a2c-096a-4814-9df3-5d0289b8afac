// #ifdef H5
const jweixin = require('jweixin-module')
// #endif
// #ifdef MP-WEIXIN
const jweixin = wx
// #endif
import {QQ_MAP_KEY,FUZZY_LOCATION} from '@/config'
import $platform from "@/common/platform"
import request from '@/common/request'
import store from 'vuex';
import promote from "../store/modules/promote";


const isWechat = () => {
	var ua = window.navigator.userAgent.toLowerCase()
	return ua.match(/micromessenger/i) == 'micromessenger'
}

const beforeFunc = () => {
	return new Promise((resolve, reject) => {
		// #ifdef H5
		if (!isWechat()) {
			reject("请使用微信客户端打开")
		}else{
			jweixin.ready(() => {
				resolve()
			});
		}
		// #endif
		// #ifdef MP-WEIXIN
		resolve()
		// #endif
	})
}


// 申请订阅功能
const requestSubscribe=function (tmplIds, resolve, reject) {
    uni.requestSubscribeMessage({
        tmplIds,
        success: async res => {
            // 检查订阅数量
            let checkSubscribeBool = await checkSubscribeAll(tmplIds, res);
			uni.hideLoading()
            if (checkSubscribeBool) {
                // 用户完成订阅
                console.log("[用户完成订阅]")
                resolve(1);
            } else {
                // 跳去检查永久关闭订阅还是普通关闭订阅
                guidSubscribeMessageAuthAfter(tmplIds, resolve, reject);
            }
        },
        fail: res => {
            console.log(res, "订阅，失败");
			uni.hideLoading()
            if (res.errCode == 20004) {
                // console.log(res, 'fail:用户关闭了主开关，无法进行订阅,引导开启---');
                guideOpenSubscribeMessage(tmplIds, resolve, reject);
            }
            return
        }
    })
}



// 计算用户订阅消息的数量
const  checkSubscribeAll=function(tmplIds, res) {
    // 将accept的生成一个数组，判断申请的订阅消息是不是已经订阅消息的子集
    let arr = [];
    for (const key of Object.keys(res)) {
        if (res[key] === 'accept') {
            arr.push(key);
        }
    }
    if (arr.length == tmplIds.length) {
        console.log('订阅完毕')
        return true
    } else {
        console.log('没订阅或者少订阅')
        return false
    }

}

// 检查用户是否授权完毕（检查时永久关闭还是普通关闭）
const guidSubscribeMessageAuthAfter=function (tmplIds, resolve, reject) {
    uni.getSetting({
        withSubscriptions: true,
        success: async res => {
            let {
                authSetting = {},
                subscriptionsSetting: { mainSwitch = false, itemSettings = {} } = {}
            } = res;
            if (Object.keys(itemSettings).length == 0) {   // 这种情况是普通关闭
                uni.showModal({
                    title: "温馨提示",
                    content: "同意订阅才能及时通知您获取最新信息",
                    confirmText: "重新订阅",
                    cancelText: "我再看看",
                    success: res => {
                        if (res.confirm) {
                            // 重新调起授权订阅
                            requestSubscribe(tmplIds, resolve, reject);
                        } else if (res.cancel) {
                            //没成功订阅，返回reject
                            reject(2);
                        }
                    }
                });
            } else {   // 这种是订阅成功或永久关闭
                let checkSubscribeBool = await checkSubscribeAll(tmplIds, itemSettings);
                if (
                    authSetting["scope.subscribeMessage"] ||
                    (mainSwitch && checkSubscribeBool)
                ) {
                    //成功
                    console.log("用户手动开启同意了，订阅消息");
                    resolve(1);
                } else {
                    //失败，永久关闭
                    guideOpenSubscribeMessage(tmplIds, resolve, reject);
                }
            }
        }
    });
}



//引导用户重新授权（永久关闭的方法）
const guideOpenSubscribeMessage=function (tmplIds, resolve, reject) {
    // console.log(resolve, reject, 'rescovavasr1')
    uni.showModal({
        title: "温馨提示",
        content: "检测到您没有开启全部订阅消息的权限，是否去设置？",
        success: res => {

            if (res.confirm) {
                uni.openSetting({
                    success: res => {
                        // 在检查是否全部订阅完毕
                        guidSubscribeMessageAuthAfter(tmplIds, resolve, reject);
                    }
                });
            } else if (res.cancel) {
                // console.log(resolve, reject, 'rescovavasr2')
                uni.showModal({
                    title: "温馨提示",
                    content: "同意订阅才能及时通知您获取最新信息",
                    showCancel: false,
                    confirmText: "我知道了"
                });
                reject(2);
            }
        }
    });
}

// 比较版本号
const compareVersion=function (v2) {
    let { SDKVersion: v1 } = uni.getSystemInfoSync();
    v1 = v1.split(".");
    v2 = v2.split(".");
    const len = Math.max(v1.length, v2.length);

    while (v1.length < len) {
        v1.push("0");
    }
    while (v2.length < len) {
        v2.push("0");
    }

    for (let i = 0; i < len; i++) {
        const num1 = parseInt(v1[i]);
        const num2 = parseInt(v2[i]);

        if (num1 > num2) {
            return 1;
        } else if (num1 < num2) {
            return -1;
        }
    }

    return 0;
}

const wxsdk = {
	// 鉴权页面
	initJssdk(commKey,authUrl) {
		return new Promise((resolve, reject) => {
			if (!isWechat()) {reject("请打开微信")}
			let url = authUrl || store.state.init.realAuthUrl || $platform.entry()
			request.api("comm/wechat/jsapi/config", {
				commKey:commKey,
				url: url
			}).then(res => {
				jweixin.config({
					debug: res.data.debug,
					appId: res.data.appId,
					timestamp: res.data.timestamp,
					nonceStr: res.data.nonceStr,
					signature: res.data.signature,
					openTagList: res.data.openTagList,
					jsApiList: [ //这里是需要用到的接口名称  
						'checkJsApi', //判断当前客户端版本是否支持指定JS接口  
						'onMenuShareAppMessage', //分享接口  
						'getLocation', //获取位置  
						'openLocation', //打开位置  
						'scanQRCode', //扫一扫接口  
						'chooseWXPay', //微信支付  
						'chooseImage', //拍照或从手机相册中选图接口  
						'previewImage', //预览图片接口  
						'uploadImage', //上传图片  
						'updateAppMessageShareData',
						'updateTimelineShareData'
					]
					// jsApiList: [ // 可能需要用到的能力 需要啥就写啥。多写也没有坏处
					// 	'checkJsApi', 'previewImage', 'openLocation', 'getLocation',
					// 	'updateAppMessageShareData', 'updateTimelineShareData',
					// 	'onMenuShareAppMessage', 'onMenuShareTimeline',
					// 	'onMenuShareQQ', 'onMenuShareQZone', 'chooseImage'
					// ]
				});
				resolve(res.data)
			}).catch(err=>{
				reject(err)
			});
		})
		
	},

	//在需要定位页面调用
	getLocation() {
		return new Promise((resolve, reject) => {
			beforeFunc().then(()=>{
				jweixin.getLocation({
					type: "gcj02", // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
					success: function(res) {
						resolve(res);
					},
					fail:function(res) {
						reject(res)
					},
					cancel:function(res) {
						reject(res)
					},
				})
			}).catch((resFunc)=>{
				reject(resFunc)
			})
		})
	},
	
	getFuzzyLocation(){
		return new Promise((resolve, reject) => {
			beforeFunc().then(()=>{
				jweixin.getFuzzyLocation({
					type: "gcj02", // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
					success: function(res) {
						resolve(res);
					},
					fail:function(res) {
						reject(res)
					},
					cancel:function(res) {
						console.log(res)
						reject(res)
					},
				})
			}).catch((resFunc)=>{
				reject(resFunc)
			})
		})
	},

	//获取微信收货地址
	openAddress(callback) {
			beforeFunc().then(() => {
				jweixin.getLocation({
					type: "wgs84", // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
					success: function(res) {
						callback(res);
					},
					fail: function(res) {
						console.log("%c微信H5sdk,getLocation失败：",
							"color:green;background:yellow");
					},
				});
			});
		
	},

	// 微信扫码
	scanQRCode() {
		return new Promise((resolve, reject) => {
			// #ifdef H5
			beforeFunc().then(()=>{
				jweixin.scanQRCode({
					needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
					scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
					success: function(res) {
						resolve(res)
					},
					fail: function(res) {
						reject(res)
					},
				});
			}).catch((resFunc)=>{
				reject(resFunc)
			})
			// #endif
			// #ifdef MP
			jweixin.scanCode({
			  success (res) {
			   resolve(res)
			  },
			  fail: function(res) {
			  	reject(res)
			  },
			})
			// #endif
		});
	},

	// 微信分享
	share(data, callback) {
		beforeFunc().then(() => {
			var shareData = {
				title: data.title,
				desc: data.desc,
				link: data.path,
				imgUrl: data.image,
				success: function(res) {
					if(callback)callback(res);
					// 分享后的一些操作,比如分享统计等等
				},
				cancel: function(res) {}
			};

			if(jweixin.updateAppMessageShareData) {
				jweixin.updateAppMessageShareData(shareData)
			} else {
				jweixin.onMenuShareAppMessage(shareData)
			}
			// jweixin.updateAppMessageShareData(shareData); //新版接口
			//分享到朋友圈接口
			jweixin.updateTimelineShareData(shareData);
		});
	},


	// 打开坐标位置
	openLocation(data) { //打开位置
		beforeFunc().then(() => {
			jweixin.openLocation({ //根据传入的坐标打开地图
				latitude: data.latitude,
				longitude: data.longitude,
				name: data.name,
				address: data.address,
				scale: 14
			});
		});
	},
	// 选择图片
	chooseImage(callback) { //选择图片
		beforeFunc(() => {
			jweixin.chooseImage({
				count: 1,
				sizeType: ["compressed"],
				sourceType: ["album"],
				success: function(rs) {
					callback(rs);
				}
			});
		});
	},

	//微信支付
	wxpay(data, callback) {
		let that = this;
		beforeFunc(() => {
			jweixin.chooseWXPay({
				timestamp: data.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
				nonceStr: data.nonceStr, // 支付签名随机串，不长于 32 位
				package: data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
				signType: data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
				paySign: data.paySign, // 支付签名
				success: function(res) {
					callback(res);
				},
				fail: function(res) {
					console.log("%c微信H5sdk,chooseWXPay失败：",
						"color:green;background:yellow");
					callback(res);
				},
				cancel: function(res) {

				},
			});
		});
	},
	
	qqMapGeocoder(lat,lng){
		var QQMapWX = require('@/common/qqmap/qqmap-wx-jssdk.min.js');
		var qqmapsdk = new QQMapWX({
			key: QQ_MAP_KEY // 必填
		});
		
		return new Promise((resolve, reject) => {
			qqmapsdk.reverseGeocoder({
				location: {
					 latitude: lat,
					 longitude: lng,
				}, 
				success: function(res) {//成功后的回调
					// console.log(res)
					if(res.status==0){ //数据成功
						let data={
							nation:{
								code:res.result.ad_info.nation_code,
								name:res.result.ad_info.nation,
							},
							city:{
								code:res.result.ad_info.adcode.substring(0,4)+'00',
								name:res.result.ad_info.city,
							},
							district:{
								code:res.result.ad_info.adcode,
								name:res.result.ad_info.district,
							},
							street:{
								id:res.result.address_reference.street.id,
								title:res.result.address_reference.street.title,
							},
							street_number:{
								id:res.result.address_reference.street_number.id,
								title:res.result.address_reference.street_number.title,
							},
							address:res.result.address,
							phone_area_code:res.result.ad_info.phone_area_code,
							location:{
								lat:lat,
								lng:lng,
							}
						}
						resolve(data)
					}else{
						reject()
					}
				   //const mapdata=res.result.ad_info;
				  // that.city = mapdata.city;
				},fail: function(error) {
				   reject()
				},
				complete: function(res) {
				   //console.log(res);
				}
			});
		})
	},
	
	//得到位置并转换得到地址
	getLocationToAddress(){
		let that=this
		return new Promise((resolve, reject) => {
			if(FUZZY_LOCATION){
				this.getFuzzyLocation().then(res_location=>{
					// that.qqMapGeocoder(res_location.latitude,res_location.longitude).then(res=>{
					// 	resolve({location:res_location,info:res})
					// }).catch(err=>{
					// 	resolve({location:res_location})
					// })
					if(res_location.errMsg=='getLocation:ok'){
						resolve(res_location)
					}else{
						reject()
					}
				}).catch(()=>{
					reject()
				})
			}else{
				this.getLocation().then(res_location=>{
					// that.qqMapGeocoder(res_location.latitude,res_location.longitude).then(res=>{
					// 	resolve({location:res_location,info:res})
					// }).catch(err=>{
					// 	resolve({location:res_location})
					// })
					if(res_location.errMsg=='getLocation:ok'){
						resolve(res_location)
					}else{
						reject()
					}
				}).catch(()=>{
					reject()
				})
			}
		})
	},
	
	//得到位置并转换得到地址
	getFuzzyLocationToAddress(){
		let that=this
		return new Promise((resolve, reject) => {
			this.getFuzzyLocation().then(res_location=>{
				// that.qqMapGeocoder(res_location.latitude,res_location.longitude).then(res=>{
				// 	resolve({location:res_location,info:res})
				// }).catch(err=>{
				// 	resolve({location:res_location})
				// })
				if(res_location.errMsg=='getLocation:ok'){
					resolve(res_location)
				}else{
					reject()
				}
			}).catch(()=>{
				reject()
			})
		})
	},
	
	subscribe(tmplIds = []) {
		uni.showLoading({
			title:'请稍等...'
		})
	    return new Promise((resolve, reject) => {
	        // 判断是否为微信小程序，不是的不做订阅进行跳过
	        let isWx = false
	        // #ifdef MP-WEIXIN
	        isWx = true
	        // #endif
	        if (!isWx){ 
				uni.hideLoading()
				resolve(1)
			}
	        // 判断基本库是否在2.8.3，低于的暂时不做订阅进行跳过
	        const versionCan = compareVersion("2.8.3");
	        if (versionCan === -1){
				uni.hideLoading()
				resolve(1)
			}
			uni.hideLoading()
	        // 主流程
	        requestSubscribe(tmplIds, resolve, reject)
	    })
	
	}
	
};

export default wxsdk;



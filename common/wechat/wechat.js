/**
 * Wechat v1.1.0
 * wehcat第三方登录组件
 */
import api from "@/common/request/index";
import $platform from "@/common/platform";
import store from "@/common/store";
import {BASE_URL} from "@/config";

export default {
	eventMap(event) {
		let map = "";
		switch (event) {
			case "login":
				map = "登录中...";
				break;
			case "refresh":
				map = "更新中...";
				break;
			case "bind":
				map = "绑定中...";
				break;
		}
		return map;
	},

	async login() {
		let token = "";
		// #ifdef MP-WEIXIN
		token = await this.wxMiniProgramOauth("login");
		return token;
		// #endif
		// #ifdef H5
		this.wxOfficialAccountOauth("login");
		// #endif
		// #ifdef APP-PLUS
		token = await this.wxOpenPlatformOauth("login");
		return token;
		// #endif
	},
	async refresh() {
		let token = "";
		// #ifdef MP-WEIXIN
		token = await this.wxMiniProgramOauth("refresh");
		return token;
		// #endif
		// #ifdef H5
		this.wxOfficialAccountOauth("refresh");
		// #endif
		// #ifdef APP-PLUS
		token = await this.wxOpenPlatformOauth("refresh");
		return token;
		// #endif
	},
	async bind() {
		let token = "";
		// #ifdef MP-WEIXIN
		token = await this.wxMiniProgramOauth("bind");
		return token;
		// #endif
		// #ifdef H5
		this.wxOfficialAccountOauth("bind");
		// #endif
		// #ifdef APP-PLUS
		token = await this.wxOpenPlatformOauth("bind");
		return token;
		// #endif
	},

	// #ifdef H5
	// 微信公众号网页登录&刷新头像昵称&绑定
	wxOfficialAccountOauth(event = "login") {
		if ($platform.get() !== "wxOfficialAccount") {
			uni.showToast({
				title: "请在微信浏览器中打开",
				icon: "none"
			});
			throw false;
		}
		let host = $platform.host();
		let payloadObject = {
			host: host,
			event,
			token: (event !== "login" && store.state.user.token) ? uni.getStorageSync("token") : ""
		};
		let payload = encodeURIComponent(JSON.stringify(payloadObject));
		let redirect_uri = encodeURIComponent(`${BASE_URL}/pages/login/login?payload=${payload}&commKey=${store.state.init.template.commKey}`);
		let oauthUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + store.state.init.wechat.appid +
			`&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_userinfo&state=123#wechat_redirect`;
		uni.setStorageSync("lastPage", window.location.href);
		window.location = oauthUrl;
	},

	// 微信公众号网页静默登录:临时登录获取OpenId 不入库不绑定用户
	wxOfficialAccountBaseLogin() {
		let state = encodeURIComponent(window.location.href);
		window.location = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + store.state.init.wechat.appid +
			`&redirect_uri=${BASE_URL}user/wxOfficialAccountBaseLogin&response_type=code&scope=snsapi_base&state=${state}`;
		throw "stop";
	},
	// #endif

	// #ifdef APP-PLUS
	// 微信开放平台登录
	wxOpenPlatformOauth(event = "login") {
		let that = this;
		return new Promise((resolve, reject) => {
			uni.login({
				provider: "weixin",
				success: function(loginRes) {
					if (loginRes.errMsg === "login:ok") {
						let authResult = loginRes.authResult;
						api("user.wxOpenPlatformOauth", {
							authResult,
							event
						}, that.eventMap(event)).then(res => {
							if (res.code === 1) {
								resolve(res.data.token);
							} else {
								resolve(false);
							}
						});
					}
				},
				fail: function(res) {
					uni.showToast({
						title: "登录失败,请稍后再试"
					});
					resolve(false);
					api("common.debug", {
						info: res
					});
				},
				complete: function(res) {}
			});
		});
	},
	// #endif

	// #ifdef MP-WEIXIN
	// 微信小程序静默登录
	async getWxMiniProgramSessionKey(autoLogin = true) {
		let sessionStatus = false;
		let session_key = "";
		return new Promise((resolve, reject) => {
			uni.checkSession({
				success(res) {
					if (res.errMsg === "checkSession:ok") sessionStatus = true;
				},
				complete() {
					if (uni.getStorageSync("session_key") && sessionStatus && !autoLogin) {
						resolve(uni.getStorageSync("session_key"));
					} else {
						uni.login({
							success: function(info) {
								let code = info.code;
								api("user.getWxMiniProgramSessionKey", {
									code: code,
									autoLogin: autoLogin
								}).then(res => {
									if (res.code === 1) {
										uni.setStorageSync("session_key", res
											.data.session_key);
										if (autoLogin) {
											if (res.data.token) {
												resolve(res.data.token);
											} else {
												resolve(false);
											}
										}
										resolve(res.data.session_key);
									} else {
										reject(res.msg);
									}
								});
							}
						});
					}
				}
			});
		});
	},

	// 微信小程序获取用户信息登录
	wxMiniProgramOauth(event = "login") {
		let that = this;
		uni.showLoading({
			title: that.eventMap(event)
		});
		return new Promise((resolve, reject) => {
			uni.getUserProfile({ // 必须手动确认触发
				desc: "获取用户基本资料", // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
				success: user => {
					if (user.errMsg === "getUserProfile:ok") {
						uni.login({
							provider: 'weixin',
							success: function(code_res) {
								if (code_res.errMsg == "login:ok") {
									// console.log(code_res.code,user.userInfo)
									uni.hideLoading();
									resolve({code:code_res.code,userInfo:user.userInfo})
								}
							}
						});
					}else{
						uni.hideLoading();
						reject("授权失败，请重试!")
					}
				},
				fail: res=>{
					uni.hideLoading();
					reject("用户取消授权")
				},
				complete: res => {
					
				}
			});
		});

	},

	// 小程序更新
	checkMiniProgramUpdate() {
		if (uni.canIUse("getUpdateManager")) {
			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate(function(res) {
				// 请求完新版本信息的回调
				if (res.hasUpdate) {
					updateManager.onUpdateReady(function() {
						uni.showModal({
							title: "更新提示",
							content: "新版本已经准备好，是否重启应用？",
							success: function(res) {
								if (res.confirm) {
									// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
									updateManager.applyUpdate();
								}
							}
						});
					});
					updateManager.onUpdateFailed(function() {
						// 新的版本下载失败
						uni.showModal({
							title: "已经有新版本了哟~",
							content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~"
						});
					});
				}
			});
		}
	},
	// #endif


};

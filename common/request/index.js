import Request from './request.js'
import store from '@/common/store/index.js'
import * as Token from '@/common/store/modules/token.js'
import * as UserInfo from '@/common/store/modules/userInfo.js'
import apiList from '@/api/index.js'
import {API_URL} from '@/config'

let refreshToking = false

const http = new Request();

http.interceptor.request((config, cancel) => {
	/* 请求之前拦截器 */
	if (config.custom.auth == true) {
		let token = Token.getToken();
		if (token) {
			config.header['Authorization'] = 'Bearer ' + token
			
		} else {
			switch (config.url) {
				case "/consumer/shop/myMenu":
					break;
				default:
					store.commit('LOGIN_TIP', true);
			}
			throw ('暂未登录,已阻止此次API请求~');
		}
	}else{
		if(config.header){
			delete config.header.Authorization
		}
	}

	return config;
});

http.interceptor.response(async (response) => {
	/* 请求之后拦截器 */
	switch (response.statusCode) {
		case 200:
			if (response.data.code != 200) { // 服务端返回的状态码不等于200，则reject()
				uni.showToast({
					title: response.data.message || '请求出错,稍后重试',
					icon: 'none',
					duration: 3000,
					mask: true
				});
				return Promise.reject(response.data)
			}
			return Promise.resolve(response.data);
		case 400:
			uni.showToast({
				title: '错误的请求',
				icon: 'none',
				duration: 3000,
				mask: true
			});
			return Promise.reject(response.data);
			break;
		case 401:
			store.dispatch('loginExit');
			store.commit('LOGIN_TIP', true);
			
			// return Promise.reject(response.data);
			break;
		case 402:
			return await tryRefreshToken(config)
		case 403:
			uni.showToast({
				title: '权限不足',
				icon: 'none',
				duration: 2000,
				mask: true
			});
			return Promise.reject(response.data);
			break;
		case 405:
			uni.showToast({
				title: '当前操作不被允许',
				icon: 'none',
				duration: 2000,
				mask: true
			});
			return Promise.reject(response.data);
		case 404:
			// uni.showToast({
			// 	title: '找不到相关资源',
			// 	icon: 'none',
			// 	duration: 2000,
			// 	mask: true
			// });
			return Promise.reject(response.data);
		case 429:
		case 999:
			uni.showToast({
				title: '请求过多，请稍等一会',
				icon: 'none',
				duration: 2000,
				mask: true
			});
			return Promise.reject(response.data);
		case 500:
			return Promise.reject(response.data);
		default:

			return Promise.reject(response.data);
	}
}, (error) => {
	return Promise.reject(error);
});

/**
 * 刷新刷新令牌
 * @param config 过期请求配置
 * @returns {any} 返回结果
 */
let requests = []
const tryRefreshToken = async (config) => {
	if (!refreshToking) {
		refreshToking = true
		try {
			// const {
			// 	data: { token },
			// } = await this.api('comm/login/refresh')
			// if (token) {
			// 	store.dispatch('user/setToken', token).then(() => {})
			// 	// 已经刷新了token，将所有队列中的请求进行重试
			// 	requests.forEach((cb) => cb(token))
			// 	requests = []
			// 	return instance(requestConf(config))
			// }
			//开始刷新
			//查看是否需要刷新token
			const refreshToken = Token.getRefresh()
			const Expires_in=Token.getExpires_in()
			const now = Date.now()
			// console.log(now,Expires_in)
			if(now>=Expires_in && !refreshToking){
				refreshToking=true
				await api('comm/login/refresh', {
					refresh_token: refreshToken
				}).then(res2 => {
					refreshToking=false
					// uni.hideLoading()
					if(!res2.data.access_token){
						store.dispatch('loginExit');
						store.commit('LOGIN_TIP', true);
					}else{
						store.dispatch('setToken', res2.data); // 保存token
						config.header['Authorization'] = 'Bearer ' + Token.getToken()
					}
					
				})
			}
		} catch (error) {
			console.error('refreshToken error =>', error)
			// router.push({ path: '/login', replace: true }).then(() => {})
		} finally {
			refreshToking = false
		}
	} else {
		return new Promise((resolve) => {
			// 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
			requests.push(() => {
				resolve(instance(http.config))
			})
		})
	}
}

function getApiObj(url) {
	let apiArray = []
	if (url.indexOf('.') > 0) {
		apiArray = url.split('.')
	} else {
		apiArray = url.split('/')
	}
	let apiData = apiList
	apiArray.forEach((v) => {
		apiData = apiData[v]
	})
	return apiData
}

function api(url, data = {}) {
	const apiData = getApiObj(url);
	return http.request({
		url: apiData.url,
		data,
		method: apiData.method,
		custom: { // 自定义数据，不会请求到服务器
			auth: apiData.auth || false
		},
	})
}

export default {
	api: api
}

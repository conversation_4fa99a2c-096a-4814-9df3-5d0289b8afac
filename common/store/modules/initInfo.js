const CityKey = 'city'
const TemplateKey = 'template'
const ConfigKey='config'
const OssKey = 'oss'
const WeChatKey = 'wechat'
const CommunityKey = 'community'

export function getCity() {
	return uni.getStorageSync(CityKey)
}
export function setCity(city) {
	return uni.setStorageSync(CityKey, city)
}
export function removeCity() {
	return uni.removeStorageSync(CityKey)
}

//模板信息
export function getTemplate() {
	return uni.getStorageSync(TemplateKey)
}
export function setTemplate(template) {
	return uni.setStorageSync(TemplateKey, template)
}
export function removeTemplate() {
	return uni.removeStorageSync(TemplateKey)
}

//微信
export function getWeChat() {
	return uni.getStorageSync(WeChatKey)
}
export function setWeChat(wechat) {
	return uni.setStorageSync(WeChatKey, wechat)
}
export function removeWeChat() {
	return uni.removeStorageSync(WeChatKey)
}

//社区
export function getCommunity() {
	return uni.getStorageSync(CommunityKey)
}
export function setCommunity(community) {
	return uni.setStorageSync(CommunityKey, community)
}
export function removeCommunity() {
	return uni.removeStorageSync(CommunityKey)
}


//上传地址
export function getUpload() {
	return uni.getStorageSync(OssKey)
}
export function setUpload(oss) {
	return uni.setStorageSync(OssKey, oss)
}
export function removeUpload() {
	return uni.removeStorageSync(OssKey)
}

//主题设置相关
export function setConfig(value) {
	try {
		let config = JSON.stringify(value);
		uni.setStorageSync(ConfigKey, config);
		// console.log('存储主题成功');
	} catch (e) {
		return;
	}
}
export function getConfig() {
	let config = uni.getStorageSync(ConfigKey);
	if (config) {
		return JSON.parse(config);
	}
}

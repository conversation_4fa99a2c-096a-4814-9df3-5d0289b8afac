const TokenKey = 'token'
const RefreshKey = 'refresh_token'
const Expires_in = 'expires_in'

export function getToken(){
	return uni.getStorageSync(TokenKey)
}
export function setToken(token){
	return uni.setStorageSync(TokenKey,token)
}
export function removeToken(){
	return uni.removeStorageSync(TokenKey)
}
export function getRefresh(){
	return uni.getStorageSync(RefreshKey)
}
export function setRefresh(data){
	return uni.setStorageSync(RefreshKey,data)
}
export function removeRefresh(){
	return uni.removeStorageSync(RefreshKey)
}
export function getExpires_in(){
	return uni.getStorageSync(Expires_in)
}
export function setExpires_in(data){
	return uni.setStorageSync(Expires_in,data)
}
export function removeExpires_in(){
	return uni.removeStorageSync(Expires_in)
}
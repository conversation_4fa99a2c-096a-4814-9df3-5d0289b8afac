import * as promoteInfo from "./promoteInfo.js";
const state = {
	name: promoteInfo.getName(),
	id: promoteInfo.getId()
}
const mutations = {
	setPromoteName(state, name) {
		state.name = name;
		promoteInfo.setName(name);
	},
	setPromoteId(state, id) {
		state.id = id;
		promoteInfo.setId(id);
	}
}

const getters = {
	// 获取name和Id
	getPromoteInfo(state){
		return {
			name: state.name,
			id: state.id
		}
	},
	// 获取是否存在已经选中的项
	getPromoteFlag(state){
		return !!state.name && !!state.id;
	}
}
export default {
	state,
	mutations,
	getters
}

import request from '@/common/request'
import * as userInfo from './userInfo.js'
import * as tokenInfo from './token.js'
import {
	LOGIN_TIP,
	PHONE_TIP,
	OUT_LOGIN
} from '../type.js'
// import { data } from '../../../components/uview-ui/libs/mixin/mixin.js'


const state = {
	token: tokenInfo.getToken(),
	refresh: tokenInfo.getRefresh(),
	expires_in:tokenInfo.getExpires_in(),
	info: userInfo.getInfo(),
	adminOutlet: userInfo.getAdminOutlet(),
	shopOutlet:userInfo.getShopOutlet(),
	investorOutlet:userInfo.getInvestorOutlet(),
	loginTip: false,
	phoneTip: false,
}

const mutations = {
	SET_TOKEN: (state, token) => {
		state.token = token
	},
	SET_INFO: (state, info) => {
		state.info = info
	},
	[LOGIN_TIP](state, data) {
		state.loginTip = data
	},
	[PHONE_TIP](state, data) {
		state.phoneTip = data
	},
	SET_REFRESH: (state, refresh) => {
		state.refresh = refresh
	},
	SET_EXPIRES_IN:(state, data) =>{
		state.expires_in = data
	},
	SET_ADMINOUTLET: (state, outlet) => {
		state.adminOutlet = outlet
	},
	SET_SHOPOUTLET:(state,shopOutlet)=>{
		state.shopOutlet=shopOutlet
	},
	SET_INVESTOROUTLET:(state,investorOutlet)=>{
		state.investorOutlet=investorOutlet
	},
}

const actions = {
	setToken({
		commit
	}, data) {
		const now = Date.now()
		let et=parseInt(data.expires_in)*1000+ now
		tokenInfo.setToken(data.access_token)
		tokenInfo.setRefresh(data.refresh_token)
		tokenInfo.setExpires_in(et)
		commit('LOGIN_TIP', false)
		commit('SET_TOKEN', data.access_token)
		commit('SET_REFRESH', data.refresh_token)
		commit('SET_EXPIRES_IN',et)
	},
	setUser({
		commit
	}, info) {
		userInfo.setInfo(info)
		commit('SET_INFO', info)
	},
	loginExit({
		commit
	}) {
		tokenInfo.removeToken()
		tokenInfo.removeRefresh()
		tokenInfo.removeExpires_in()
		userInfo.removeInfo()
		commit('LOGIN_TIP', true)
		commit('SET_TOKEN', '')
		commit('SET_REFRESH', '')
		commit('SET_INFO', '')
		commit('SET_EXPIRES_IN','')
	},
	setAdminOutlet({
		commit
	}, adminOutlet) {
		userInfo.setAdminOutlet(adminOutlet)
		commit('SET_ADMINOUTLET', adminOutlet)
	},
	setShopOutlet({
		commit
	}, shopOutlet) {
		userInfo.setShopOutlet(shopOutlet)
		commit('SET_SHOPOUTLET', shopOutlet)
	},
	setInvestorOutlet({
		commit
	}, investorOutlet) {
		userInfo.setInvestorOutlet(investorOutlet)
		commit('SET_INVESTOROUTLET', investorOutlet)
	},
	
	async getMyInfo({
		commit
	}) {
		return new Promise((resolve, reject	) => {
			request.api('comm/login/myinfo').then(res => {
				userInfo.setInfo(res.data)
				commit('SET_INFO', res.data)
				resolve(res.data)
			}).catch(e => {
				tokenInfo.removeToken()
				tokenInfo.removeRefresh()
				tokenInfo.removeExpires_in()
				userInfo.removeInfo()
				commit('SET_TOKEN', '')
				commit('SET_REFRESH', '')
				commit('SET_INFO', '')
				commit('SET_EXPIRES_IN','')
				reject('user info load failed')
			});
		})
	},
}

const getters = {
	token: state => state.token,
	getUserInfo: state => state.info
}

export default {
	state,
	mutations,
	actions,
	getters
}

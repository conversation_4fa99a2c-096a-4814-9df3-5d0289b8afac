import request from '@/common/request'
import * as initInfo from './initInfo.js'
import {
	getConfig,
	setConfig,
	getCart,
	getToken
} from '@/common/utils/auth.js'
import {
	CITY_DATA,
	TEMPLATE_DATA,
	SET_THEME,
	WECHAT_DATA,
	OSS_DATA,
	COMMUNITY_DATA,
	LOCATION_ADDRESS_DATA,
} from '../type.js'
import {INICOLOR} from '@/config'

const state = {
	city: initInfo.getCity(),
	template: initInfo.getTemplate(),
	theme: initInfo.getConfig() ? initInfo.getConfig() : {
		color: INICOLOR,
		name: 'default',
		title: '官方'
	},
	realAuthUrl: '',
	oss: initInfo.getUpload(),
	wechat: initInfo.getWeChat(),
	community: initInfo.getCommunity(),
	statusHeight:0,
	locationAddress:{},
	share: {}, // 分享配置
	shareInfo: {} // 默认分享数据
}

const mutations = {
	[TEMPLATE_DATA](state, data) {
		state.template = data
	},
	[CITY_DATA](state, data) {
		state.city = data
	},
	[SET_THEME](state, data) {
		state.theme = data
	},
	[WECHAT_DATA](state, data) {
		state.wechat = data
	},
	[COMMUNITY_DATA](state, data){
		state.community = data
	},
	saveRealAuthUrl(state, data) {
		state.realAuthUrl = data
	},
	// 设置模板菜单信息
	setTempMenu(state, data) {
		state.template.menu.menuMyOrders = data;
	},
	OSS: (state, data) => {
		state.oss = data
	},
	shareInfo(state, data) {
		state.shareInfo = data
	},
	statusHeight(state,data){
		state.statusHeight = data
	},
	[LOCATION_ADDRESS_DATA](state, data){
		state.locationAddress = data
	},
}

const actions = {
	async getTemplate({
		commit
	}, options = {}) {
		return new Promise((resolve, reject) => {
			request.api('comm/template', {}).then(res => {
				initInfo.setTemplate(res.data)
				commit('TEMPLATE_DATA', res.data)
				resolve(res.data)
			}).catch(e => {
				reject('template load failed')
			});
		})
	},
	async getAppInfo({
		commit
	}) {
		return new Promise((resolve, reject) => {
			request.api('comm/appInfo', {
				commKey: state.template.commKey
			}).then(res => {
				// #ifdef H5
				//设置标题
				uni.setNavigationBarTitle({
					title: res.data.name
				})
				// #endif
				resolve(res.data)
			}).catch(e => {
				reject('app信息无法得到')
			});
		})
	},
	setCommKey({
		commit
	}, commKey) {
		let obj={commKey:commKey}
		initInfo.setTemplate(obj)
		commit('TEMPLATE_DATA', obj)
	},
	async getWeChat({
		commit
	}, options = {}) {
		return new Promise((resolve, reject) => {
			request.api('comm/wechat/info', {
				commKey: state.template.commKey
			}).then(res => {
				initInfo.setWeChat(res.data)
				commit('WECHAT_DATA', res.data)
				resolve(res.data)
			}).catch(e => {
				reject('wechat load failed')
			});
		})
	},
	async getUpload({
		commit
	}, options = {}) {
		return new Promise((resolve, reject) => {
			request.api('comm/uploadInfo', {
				commKey: state.template.commKey
			}).then(res => {
				initInfo.setUpload(res.data)
				commit('OSS', res.data)
				resolve(res.data)
			}).catch(e => {
				reject('oss load failed')
			});
		})
	},
	setConfig({
		commit
	}, config) {
		commit('SET_THEME', config)
		initInfo.setConfig(config)
		var app = getApp();
		app.globalData.newColor = config.color;
	},
	setOss({
		commit
	}, data) {
		commit('OSS', data)
		initInfo.setUpload(data)
	},
	setStatusHeight({
		commit
	}, data) {
		commit('statusHeight', data)
	},
	setCity({
		commit
	}, data) {
		commit(CITY_DATA, data)
		initInfo.setCity(data)
	},
	setCommunity({
		commit
	}, data) {
		commit(COMMUNITY_DATA, data)
		initInfo.setCommunity(data)
	},
	setLocationAddress({
		commit
	}, data) {
		commit(LOCATION_ADDRESS_DATA, data)
	},
}

const getters = {
	initWechat: state => state.wechat,
	shareInfo: state => state.shareInfo,
	initShare: state => state.share,
}

export default {
	state,
	mutations,
	actions,
	getters
}
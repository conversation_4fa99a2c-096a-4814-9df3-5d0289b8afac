import websocket from '@/common/websocket';
import {API_URL} from '@/config'

const state = {
	socketTask: null,
	eventlist: {},
	unread: []
}

const mutations = {
	socketInit(state, url) {
		let newUrl=API_URL.replace('https://','wss://').replace('http://','ws://')
		state.socketTask = new websocket.socket()
		state.socketTask.connection(newUrl + url)
	},
}

const actions = {
	async socketInit({
		commit
	}, url) {
		commit('socketInit',url)
	},
	
}

export default {
	state,
	mutations,
	actions
}

import Vue from "vue"
import {
	RouterMount,
	createRouter
} from "uni-simple-router"
import * as Token from '@/common/store/modules/token.js'
import store from '@/common/store'

const router = createRouter({
	// history: createWebHistory(),
	platform: process.env.VUE_APP_PLATFORM,
	routes: [...ROUTES,
		{
			aliasPath: '',
			path: '/',
			name: ''
		}
	],
});

// //初始化
// const router = new Router({
// 	mode:'history',
// 	base:'/h5/',
// 	// scrollBehavior:() => ({y:0}),
// 	APP:{
// 		animation:{
// 			animationType:'pop-in',
// 			animationDuration:300
// 		}
// 	},
// 	encodeURL:false,
// 	routes:ROUTES
// })

// //全局路由前置守卫
router.beforeEach((to, from, next) => {
	// #ifdef H5
	const url = location.origin + (to.redirectedFrom || to.fullPath)

	if (uni.getSystemInfoSync().platform == 'ios') {
		if (!store.state.init.realAuthUrl) {
			store.commit('saveRealAuthUrl', url)
		}
	}else{
		store.commit('saveRealAuthUrl', url)
	}
	// #endif

	// 有两个个判断条件,一个是token,还有一个路由元信息
	let token = Boolean(Token.getToken());
	// 权限控制
	if (to.meta && to.meta.auth && !token) {
		store.commit('LOGIN_TIP', true)
	}
	// else if(to.meta && to.meta.community && !communityInfo){  
	//  store.commit('COMMUNITY_TIP', true);
	//  console.log('router communityInfo   sssssssss');
	// }
	else {
		next()
	}
})

// 全局路由后置守卫
router.afterEach((to, from) => {})

Vue.use(router)

export {
	router,
	RouterMount
}

import app_push from './app_push.js'
import h5Push from './h5_push.vue'

const appPush = {
	install: function(Vue) {
		Vue.prototype.$appPush = function(op = {}) {
			// #ifdef APP-PLUS
			new app_push({
				...op
			}).show();
			// #endif
			
			// #ifdef H5
			// 创建构造器
			const H5PushInstance = Vue.extend(h5Push)
			let instance = new H5PushInstance({
				data: op
			})
			instance.$mount()
			document.body.appendChild(instance.$el)
			Vue.nextTick(() => {
				instance.show = true
			})
			// #endif
			
			// #ifdef MP
			if(!op.messageType) return
			if(op.messageType.indexOf('mp')<0) return
			if(op.vibration) wx.vibrateLong()
			if(op.url){
				uni.showModal({
					title:op.messageTitle,
					content:op.messageContent,
					success: (res)=> {
						if (res.confirm) {
							uni.navigateTo({
								url: op.url,
								fail() {										
									uni.switchTab({
									  url: op.url
									})
								}
							})
						}
					}
				})
			}else{
				uni.showToast({
					title:op.messageTitle+'：'+op.messageContent,
					icon:'none',
					image:op.messageImage,
					duration:5000,
				})
			}
			// #endif
			
		}
	}
}

export default appPush
let isSocketClose = false; // 是否关闭socket
// let reconnectCount = 5; // 重连次数
let heartbeatInterval = ""; // 心跳定时器
let socketTask = null; // websocket对象

let againTimer = null; //断线重连定时器

let _events = new Array();

let url = null;
let onReFn = null;
let onSucFn = null;
let onErrFn = null;

let recordCode = 0x1e;
let recordString = String.fromCharCode(recordCode);
let isConnectioned = false;

//初始化相关事件
//消息发送事件
_events['send'] = function(obj) {
	console.log(obj);
};
//连接事件
_events['connected'] = function() {
	console.log('连接成功');
};
//连接关闭事件
_events['close'] = function() {
	console.log('连接已经关闭');
};
//连接异常处理事件
_events['error'] = function(ex) {
	console.log('连接异常', ex);
};

/**
 * sockeUrl：websocet的地址
 * onReceive：消息监听的回调
 * onErrorEvent：抛出错误的回调，且弹窗连接失败的提示框
 * onErrorSucceed：抛出成功回调，主要用于隐藏连接失败的提示框
 * */
const socket = () => {
	// url = sockeUrl;
	// onReFn = onReceive;
	// onErrFn = onErrorEvent;
	// onSucFn = onErrorSucceed;
	isSocketClose = false;
	//判断是否有websocet对象，有的话清空
	if (socketTask) {
		socketTask.close();
		socketTask = null;
		clearInterval(heartbeatInterval);
	}



	return {
		//事件绑定
		on: function(eventName, eventMethod) {
			if (_events[eventName] != null && _events[eventName] != undefined) {
				_events[eventName] = eventMethod;
			} else {
				_events[eventName] = eventMethod;
			}
		},
		//连接方法
		connection: function(url) {
			let self = this
			socketTask = uni.connectSocket({
				url: url,
				success(data) {
					//console.log("websocket对象创建成功");
					clearInterval(againTimer) //断线重连定时器
				},
				fail: (err) => {
					console.log("报错", err);
				}
			});

			// 连接打开
			socketTask.onOpen((res) => {
				//console.log('WebSocket打开');
				clearInterval(againTimer) //断线重连定时器
				isConnectioned = true;
				let handshakeRequest = {
					protocol: 'json',
					version: 1
				};
				let senddata = `${JSON.stringify(handshakeRequest)}${recordString}`;
				socketTask.send({
					data: senddata,
				});

				// 10秒发送一次心跳
				heartbeatInterval && clearInterval(heartbeatInterval);
				heartbeatInterval = setInterval(() => {
					socketTask.send({
						data: "hart",
					});
				}, 1000 * 10)
				_events['connected']("连接成功！");
				// Promise.resolve("连接成功！");
			})

			// 监听连接失败
			socketTask.onError((err) => {
				console.log('WebSocket连接失败', err);
				//停止发送心跳
				clearInterval(heartbeatInterval)
				//如果不是人为关闭的话，进行重连
				if (!isSocketClose) {
					reconnect(url, _events['error'])
				}
			})

			// // 监听连接关闭 -
			socketTask.onClose((e) => {
				console.log('WebSocket连接关闭！');
				clearInterval(heartbeatInterval)
				if (!isSocketClose) {
					reconnect(url, _events['close'])
				}
			})

			// 监听收到信息
			socketTask.onMessage((res) => {
				try {
					let reg = new RegExp(recordString,"g");
					let jsonstr = String(res.data).replace(reg, '')
					if (jsonstr.indexOf('{}{') > -1) {
						jsonstr = jsonstr.replace('{}', '');
					}
					if(jsonstr){
						jsonstr=jsonstr.replace(new RegExp('{}','gm'),'').replace(new RegExp('}{','gm'),'},{')
						let obj=JSON.parse('['+jsonstr+']')
						obj.forEach(item => {
							if (item.type == 1) {
								let target = item.target
								if (_events[target]) {
									_events[target](item.arguments[0]);
								}
							}
						})
					}

				} catch (ex) {
					console.log('异常：' + ex);
					// console.log('收到服务器内容：' + res.data);
				}
			});
		},
		invoke: function(method, params) {
			if (!isConnectioned) return
			socketTask.send({
				data: `${JSON.stringify({"arguments":[params],"invocationId":"0","streamIds":[],"target":method,"type": 1})}}${recordString}`
			})
		},
		// sendMsg: function(data) { //向后端发送命令
		// 	try {
		// 		//通过 WebSocket 连接发送数据
		// 		socketTask.send(data);
		// 	} catch (e) {
		// 		if (isSocketClose) {
		// 			return
		// 		} else {
		// 			reconnect(url, onErrFn)
		// 		}
		// 	}
		// },
		//关闭websocket【必须在实例销毁之前关闭,否则会是underfined错误】beforeDestroy() {websocetObj.stop();}
		stop: function() {
			isSocketClose = true
			clearInterval(heartbeatInterval);
			clearInterval(againTimer) //断线重连定时器
			socketTask.close(); // 确保已经关闭后再重新打开
			socketTask = null;
		}

	}

}

const reconnect = (url, onErrorEvent) => {
	console.log('进入断线重连', isSocketClose);
	clearInterval(againTimer) //断线重连定时器
	clearInterval(heartbeatInterval);
	socketTask && socketTask.close(); // 确保已经关闭后再重新打开
	socketTask = null;
	onErrorEvent({
		isShow: true,
		messge: '正在连接服务器...'
	})
	// 连接  重新调用创建websocet方法
	againTimer = setInterval(() => {
		console.log('在重新连接中...');
		let conn = new socket();
		conn.connection(url)
	}, 1000 * 5)


}


module.exports = {
	socket: socket
}

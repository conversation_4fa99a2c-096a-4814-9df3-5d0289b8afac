@import "@/common/colorui/main.css";
@import "@/common/colorui/icon.css";
@import '@/components/uview-ui/theme.scss';

page,.ut-page-bg {
	background-color: #F3F5F6;
}

.spacer{
	flex: 1 !important;
}

.uni-tabbar__icon { // tabBar 图标
	width: 40rpx!important;
	height: 40rpx!important;
}

.uni-tabbar__label { // tabBar 图标
	font-size: 24rpx;
}

.u-badge{
	z-index: 1;
}


.x-bc {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pad{
    padding: 0 15px;
}

.x-c{
    display: flex;
    justify-content: center;
    align-items: center;
}

.x-f{
    display: flex;
    align-items: center;
}

.p-f {
	position: fixed;
	bottom: 0;
	padding: 20rpx;
	width: 100%;
	display: flex;
	justify-items: center;
	align-items: center;
	justify-content: center;
	z-index: 100;
	.btn {
		width: 100%;
		padding: 4rpx 10rpx;
		letter-spacing: 4rpx;
		border-radius: 35rpx;
		color: rgba(#fff, 0.9);
		text-align: center;
		font-size: 28rpx;
		padding: 10rpx;
	}
}

.post-btn {
	flex: 1;
	height: 74rpx;
	box-shadow: 0px 7rpx 6rpx 0rpx rgba(229, 138, 0, 0.22);
	border-radius: 37rpx;
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
}

//这部份为重新定义的
.tool-menu {
		justify-content: space-between;
		border-radius: 100px;
		padding: 3px 7px;
		opacity: .8;
		border: 0.5px solid #dadbde;
		font-size: 20rpx;
	}

	.list-box {
		flex: 1;
		padding: 15rpx;
		box-sizing: border-box;
		overflow: hidden;
		/* #ifdef MP */
		padding-bottom: 50upx;
		/* #endif */
		width: 100%;
		height: 100%;

		.disabled {
			height: 0;
			overflow: hidden;
		}

		.list-item {
			margin: 0 auto;
			padding: 20rpx 30rpx;
			margin-top: 5upx;
			background-color: #ffffff;
			border-radius: 10upx;
			margin-bottom: 20upx;
			box-shadow: 0upx 0upx 10upx #EDEDED;
			box-sizing: border-box;
			width: 98%;
		}
	}

	.img-text-box {
		display: flex;
		align-content: center;

		.right {
			margin-left: 20upx;
			width: 100%;
		}
	}



	.text-box {
		.main {
			font-weight: bold;
			font-size: 28rpx;
			color: #4d4d4d;
			line-height: 1.5;
			letter-spacing: 4rpx;
		}
	}

	.text-font-sub {
		color: #a0a0a0;
		font-size: 20rpx;
		line-height: 2;
	}

	.line-box {
		margin-top: 5rpx;
		padding: 5rpx;
		height: 2rpx;
		width: 100%;
		border-top: 2rpx dashed #a0a0a0;
	}

	.row-box {
		display: flex;
		justify-content: space-between;
		font-weight: normal;
		font-size: 24rpx;
		line-height: 2;
	}

	.fs-c1 {
		color: #a0a0a0;
	}

	.fs-xs {
		font-size: 20rpx;
	}
//


.ut-box-card{
	padding: 16rpx;
	height: auto;
	background-color: #ffffff;
	overflow: hidden;
}

.ut-box{
	padding: 12rpx;
	.ut-box-card{
		padding: 8rpx 4rpx;
		background-color:transparent;
	}
	&-card:first-of-type{
		padding-top: 4rpx;
	}
	&-card:last-of-type{
		padding-bottom: 4rpx;
	}
}

.ut-box-shadow,.box-shadow {
	height: auto;
	background-color: #ffffff;
	padding: 16rpx;
	border-radius: 5rpx;
	box-shadow: 0 3rpx 6rpx 0 rgba(138, 138, 138, 0.34) !important;
	overflow: hidden;
}



.f1 {
	color: #000000;
}

.f2 {
	color: #303030;
}

.f3 {
	color: #757575;
}

.padding {
	padding: 0 20rpx;
}

.margin-top {
	margin-top: 20rpx;
}

.margin-top-min {
	margin-top: 10rpx;
}

.full-width {
	width: 100%;
}

/* 标签 */
.ut-tag {
	font-size: 20rpx;
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 5rpx 15rpx;
	border-radius: 5rpx;
	white-space: nowrap;
}

.ut-tag::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1rpx solid currentColor;
	-webkit-transform: scale(0.5);
	transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: inherit;
	z-index: 1;
	pointer-events: none;
}

.ut-font-xs {
	font-size: 22rpx;
}

.ut-font-sm {
	font-size: 26rpx;
}

.ut-font-md {
	font-size: 28rpx;
}

.ut-font-lg {
	font-size: 30rpx;
}

.ut-font-xl {
	font-size: 34rpx;
}

.ut-flex {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	align-items: center;
}

.ut-flex-wrap {
	flex-wrap: wrap;
}

.ut-flex-nowrap {
	flex-wrap: nowrap;
}

.ut-col-center {
	align-items: center;
}

.ut-col-top {
	align-items: flex-start;
}

.ut-col-bottom {
	align-items: flex-end;
}

.ut-row-center {
	justify-content: center;
}

.ut-row-left {
	justify-content: flex-start;
}

.ut-row-right {
	justify-content: flex-end;
}

.ut-row-between {
	justify-content: space-between;
}

.ut-row-around {
	justify-content: space-around;
}

.ut-text-left {
	text-align: left;
}

.ut-text-center {
	text-align: center;
}

.ut-text-right {
	text-align: right;
}

.ut-flex-col {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
}

.ut-card {
	width: 100%;
	height: auto;
	background: #fff;
	box-shadow: 4rpx 8rpx 10rpx 10rpx rgba(199, 199, 199, 0.3);
	border-radius: 0 0 10rpx 10rpx;
	overflow: hidden;
}


.ut-radius-top {
	border-radius: 10rpx 10rpx 0 0;
}

.ut-radius-bottom {
	border-radius: 0 0 10rpx 10rpx;
}

.ut-radius {
	border-radius: 10rpx;
}

.ut-margin-top {
	margin-top: 16rpx;
}

.ut-popup {
	.s-popup__wrap{
			border-radius: 10rpx 10rpx 0 0;
	}
	&-title {
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 30rpx;
		font-weight: bold;
		letter-spacing: 2rpx;
		border-bottom: 2rpx solid #eee;
		position: relative;
		.close {
			position: absolute;
			right: 30rpx;
			top: 30rpx;
		}
	}

	&-content {
		padding: 20rpx;
	}
	
	&-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}

}


@font-face {
	font-family: 'iconfont';
	src: url('@/static/font/ut.woff2') format("woff2");
	font-weight: normal;
	font-style: normal;
	font-display: swap;
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* 加的 */
.icon-location:before {
	content: "\a054";
}

.icon-positioning:before {
	content: "\e611";
}

.icon-settings:before {
	content: "\e690";
}

.icon-balance:before {
	content: "\e631";
}

.icon-redpacket:before {
	content: "\a039";
}

.icon-phone:before {
	content: "\a037";
}

.icon-vip:before {
	content: "\a055";
}

.icon-chat:before {
	content: "\e858";
}

.icon-service:before {
	content: "\e602";
}

.icon-photo:before {
	content: "\a047";
}

.icon-photo-back:before {
	content: "\a030";
}

.icon-code:before {
	content: "\a031";
}

.icon-outletfill:before {
	content: "\a018";
}

.icon-outlet:before {
	content: "\a019";
}

.icon-good:before {
	content: "\e60f";
}

.icon-give:before {
	content: "\a003";
}

.icon-yes:before {
	content: "\a004";
}

.icon-advice:before {
	content: "\a006";
}

.icon-arrow-left:before {
	content: "\e66d";
}

.icon-home:before {
	content: "\e603";
}

.icon-newshot:before {
	content: "\a041";
}

.icon-presentfill:before {
	content: "\a044";
}

.icon-present:before {
	content: "\a045";
}

.icon-coupon:before {
	content: "\a056";
}

.icon-close:before {
	content: "\a005";
}

.icon-man:before {
	content: "\a057";
}

.icon-manfill:before {
	content: "\a058";
}

.icon-woman:before {
	content: "\a059";
}

.icon-womanfill:before {
	content: "\a060";
}

.icon-child:before {
	content: "\a061";
}

.icon-childfill:before {
	content: "\a062";
}

.icon-management:before {
	content: "\a020";
}

.icon-left::before {
	content: "\a063";
}

.icon-right::before {
	content: "\a064";
}

.icon-top::before {
	content: "\a065";
}

.icon-bottom::before {
	content: "\a066";
}

.icon-tag::before {
	content: "\a036";
}

.icon-diamond::before {
	content: "\a043";
}

.icon-investor::before {
	content: "\a027";
}

.icon-shopping::before {
	content: "\a025";
}

/*  */
.icon-smile:before {
	content: "\a007";
}

.icon-pentagram-fill:before {
	content: "\a008";
}

.icon-pentagram:before {
	content: "\a009";
}

.icon-fenxiang:before {
	content: "\e621";
}

.icon-CombinedShape:before {
	content: "\e66f";
}

.icon-shoucang1:before {
	content: "\e620";
}

.icon-fanhui:before {
	content: "\e66d";
}

.icon-erji:before {
	content: "\e651";
}

.icon-sanji:before {
	content: "\e652";
}

.icon-gongge:before {
	content: "\e61f";
}

.icon-liebiao:before {
	content: "\e78f";
}

.icon-duihao:before {
	content: "\e733";
}

.icon-daishouhuo1:before {
	content: "\e61d";
}

.icon-yiguoqi:before {
	content: "\e734";
}

.icon-daihexiao:before {
	content: "\e61e";
}

.icon-jingyin:before {
	content: "\e61c";
}

.icon-shengyin:before {
	content: "\e6e8";
}

.icon-dianyuanguanli:before {
	content: "\e659";
}

.icon-jiameng:before {
	content: "\e6b5";
}

.icon-huiyuan:before {
	content: "\e61a";
}

.icon-moren:before {
	content: "\e619";
}

.icon-bianji:before {
	content: "\e644";
}

.icon-shanchu:before {
	content: "\e627";
}

.icon-chenggong:before {
	content: "\e618";
}

.icon-gouwuche1:before {
	content: "\e613";
}

.icon-shouye:before {
	content: "\e617";
}

.icon-ruzhu:before {
	content: "\e632";
}

.icon-zhongduan:before {
	content: "\e649";
}

.icon-waishengdailijigoushouli:before {
	content: "\e66e";
}

.icon-fenxiao:before {
	content: "\e63d";
}

.icon-shangsanjiao:before {
	content: "\e626";
}

.icon-xiasanjiao:before {
	content: "\e622";
}

.icon-daipingjia:before {
	content: "\e671";
}

.icon-daipingjia1:before {
	content: "\e60d";
}

.icon-kefu1:before {
	content: "\e635";
}

.icon-meiye:before {
	content: "\e602";
}

.icon-shezhi2:before {
	content: "\e690";
}

.icon-shezhi1:before {
	content: "\e60c";
}

.icon-shoucang:before {
	content: "\e60b";
}

.icon-fenxiang1:before {
	content: "\e615";
}

.icon-remen:before {
	content: "\a024";
}

.icon-remenFill:before {
	content: "\e628";
}

.icon-fuwu:before {
	content: "\e608";
}

.icon-dingdanweikong:before {
	content: "\e6e4";
}

.icon-huangguan:before {
	content: "\e61b";
}

.icon-none:before {
	content: "\e600";
}

.icon-qianbao:before {
	content: "\e612";
}

.icon-zanwu:before {
	content: "\e6f7";
}

.icon-daifahuo:before {
	content: "\e623";
}

.icon-sousou:before {
	content: "\e60e";
}

.icon-pingjia:before {
	content: "\e6d7";
}

.icon-daifukuan:before {
	content: "\e6ed";
}

.icon-zuanshi:before {
	content: "\e631";
}

.icon-kefu:before {
	content: "\e6a1";
}

.icon-wode:before {
	content: "\e609";
}

.icon-zhanghu:before {
	content: "\e601";
}

.icon-xiaoxi:before {
	content: "\e858";
}

.icon-shouhou:before {
	content: "\e62e";
}

.icon-zanwushuju:before {
	content: "\e6d1";
}

.icon-xinbaniconshangchuan-:before {
	content: "\e616";
}

.icon-dizhi:before {
	content: "\e611";
}

.icon-zanwu1:before {
	content: "\e7d1";
}

.icon-shouye-yes:before {
	content: "\e603";
}

.icon-daishouhuo:before {
	content: "\e604";
}

.icon-tuijian:before {
	content: "\e60f";
}

.icon-zuji:before {
	content: "\e605";
}

.icon-lingquan-:before {
	content: "\e614";
}

.icon-shezhi:before {
	content: "\e610";
}

.icon-icon_type:before {
	content: "\e606";
}

.icon-gouwuche:before {
	content: "\e607";
}

.icon-order:before {
	content: "\e60a";
}

.icon-exclusive:before {
	content: "\a042";
}

.icon-bexyi:before{
	content: "\a028";
}
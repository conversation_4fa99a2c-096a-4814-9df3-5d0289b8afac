// 加载动画
.sk-roller {
  display: inline-block;
  position: relative;
  width: 64px;
  height: 64px;
}
.sk-roller div {
  animation: sk-roller 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  transform-origin: 32px 32px;
}
.sk-roller div:after {
  content: " ";
  display: block;
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  margin: -3px 0 0 -3px;
}
.sk-roller div:nth-child(1) {
  animation-delay: -0.036s;
}
.sk-roller div:nth-child(1):after {
  top: 50px;
  left: 50px;
}
.sk-roller div:nth-child(2) {
  animation-delay: -0.072s;
}
.sk-roller div:nth-child(2):after {
  top: 54px;
  left: 45px;
}
.sk-roller div:nth-child(3) {
  animation-delay: -0.108s;
}
.sk-roller div:nth-child(3):after {
  top: 57px;
  left: 39px;
}
.sk-roller div:nth-child(4) {
  animation-delay: -0.144s;
}
.sk-roller div:nth-child(4):after {
  top: 58px;
  left: 32px;
}
.sk-roller div:nth-child(5) {
  animation-delay: -0.18s;
}
.sk-roller div:nth-child(5):after {
  top: 57px;
  left: 25px;
}
.sk-roller div:nth-child(6) {
  animation-delay: -0.216s;
}
.sk-roller div:nth-child(6):after {
  top: 54px;
  left: 19px;
}
.sk-roller div:nth-child(7) {
  animation-delay: -0.252s;
}
.sk-roller div:nth-child(7):after {
  top: 50px;
  left: 14px;
}
.sk-roller div:nth-child(8) {
  animation-delay: -0.288s;
}
.sk-roller div:nth-child(8):after {
  top: 45px;
  left: 10px;
}
@keyframes sk-roller {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ut-popIn  {
0% {
	   -webkit-transform: scale3d(0, 0, 0);
	   transform: scale3d(0.5, 0.5, 0.5);
	   opacity: 0;
   }
   50% {
	   -webkit-animation-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
	   animation-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
   }
   100% {
	   -webkit-transform: scale3d(1, 1, 1);
	   transform: scale3d(1, 1, 1);
	   -webkit-animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
	   animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
	   opacity: 1;
   }
}

.margin-top10{
	margin-top: 10rpx;
}

.gray {
	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);

	filter: gray;
	opacity: 0.6;
}

//swipe常用
.swipe-box{
	padding: 12rpx 12rpx; 
	background-color: white; 
	margin-bottom: 10rpx;
	
	.title{
		font-size: 28rpx;
		font-weight: bold;
		line-height: 1.8;
	}
	.title-sub{
		font-size: 22rpx;
		color: #999;
		line-height: 1.6;
	}
	
	.valid{
		height: 100%;
		display: flex;
		align-items: center;
		&.yes{
			color: var(--colors);
		}
	}
	
}

.swipe-box.active{
	color: #fff;
	background: var(--colors2) !important;
	
	.title-sub{
		color: #efefef;
	}
}

.swipe-box.active .valid{
	color: #fff;
}



.swipe-right{
	height: 100%;
	.s-button{
		height: 100% !important;
	}
}

//
.mang-footer{
	width: 100%;
	height: 80rpx;
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	box-sizing: border-box;
	border-top: 1rpx #999 solid;

}

.popup-box{
	padding: 16rpx;
	border-radius: 20rpx 20rpx 0 0;
	background: #fff;
	height: 100%;
		
	.title{
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		line-height: 1.8;
		border-bottom: 2rpx solid #F3F5F6;
		.iconfont{
			position: absolute;
			top:-10rpx;
			right: 10rpx;
			color: #999;
			font-size: 30rpx;
		}
	}
		
	.scroll-view{
		overflow-y: scroll;
		white-space: nowrap;
		padding-top: 10rpx;
		height:100%;
		padding-bottom: 50rpx;
		
	}
	
}


/* 评晔 */
.evaluate{
	padding:  20rpx;
	background: #fff;
	.title{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-bottom: 10rpx;
		.title-left{
			font-size: 30rpx;
			font-weight: bold;
			display: flex;
			align-items: center;
			
			.title-left-icon{
				width: 10rpx;
				height: 30rpx;
				display: inline-block;
				border-radius: 50rpx;
				background: var(--colors);
				margin-right: 10rpx;
			}
		}
		.title-right{
			min-width: 200rpx;
			text-align: right;
			font-size: 18rpx;
			color: #767676;
		}
	}
	

}

.tag-box{
	display: flex;
	flex-wrap: wrap;
	padding:  0 20rpx;
	.tag-item{
		padding: 6rpx 16rpx;
		background: #fff6e4;
		color: #fa9e17;
		font-size: 18rpx;
		margin: 4rpx 10rpx 4rpx 0;
		border-radius: 50rpx;
		display: inline-block;
	}
}

.user-box{
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	.user-head{
		
	}
	.user-name{
		flex: 1;
		padding-left: 10rpx;
	}
}
/* 以上为整理过的 */
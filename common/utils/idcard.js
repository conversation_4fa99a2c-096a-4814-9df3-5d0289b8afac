export default{
	test(value){
		var ereg
		if (parseInt(value.substr(6, 4)) % 4 == 0 || (parseInt(value.substr(6, 4)) % 100 == 0 && parseInt(value.substr(6, 4)) % 4 == 0)) {
		    ereg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}[0-9Xx]$/;
			//闰年出生日期的合法性正则表达式
		}
		else {
		    ereg = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}[0-9Xx]$/;
		    //平年出生日期的合法性正则表达式
		}
		var idcard_array = value.split('')
		if (ereg.test(value)) {
		    var S = (parseInt(idcard_array[0]) + parseInt(idcard_array[10])) * 7 +
		            (parseInt(idcard_array[1]) + parseInt(idcard_array[11])) * 9 +
		            (parseInt(idcard_array[2]) + parseInt(idcard_array[12])) * 10 +
		            (parseInt(idcard_array[3]) + parseInt(idcard_array[13])) * 5 +
		            (parseInt(idcard_array[4]) + parseInt(idcard_array[14])) * 8 +
		            (parseInt(idcard_array[5]) + parseInt(idcard_array[15])) * 4 +
		            (parseInt(idcard_array[6]) + parseInt(idcard_array[16])) * 2 +
		            parseInt(idcard_array[7]) * 1 +
		            parseInt(idcard_array[8]) * 6 +
		            parseInt(idcard_array[9]) * 3;
		    var Y = S % 11;
			var M = 'F';
			var JYM = '10X98765432';
			M = JYM.substr(Y, 1);
			if (M == idcard_array[17])
				return true;
			else
				return false;
		}
		return false
	}
}
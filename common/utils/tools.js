import {API_URL} from '@/config'
import {
	router
} from '@/common/router/index.js'
export default {
	/**
	 * 跳转再封装，不支持复杂传参。
	 */
	routerTo(path, params = {}, isLogin) {
		let objParams = params;
		// 是否跳转外部链接
		if (~path.indexOf('http')) {
			// #ifdef H5
			window.location = path;
			// #endif

			// #ifndef  H5
			router.push({
				path: '/pages/public/webview',
				query: {
					'webviewPath': path
				}
			})
			// #endif
			return false
		}
		// 判断是否有参数
		if (path.indexOf('?') !== -1) {
			let index = path.lastIndexOf('?');
			let query = path.substring(index + 1, path.length);
			let arr = query.split('&')
			path = path.slice(0, index);
			arr.forEach(item => {
				let mArr = []
				let obj = {}
				mArr = item.split('=');
				obj[mArr[0]] = mArr[1];
				objParams = {
					...objParams,
					...obj
				}

			})
		}
		// 判断是否是tabbar
		if (isLogin) {
			router.replaceAll({
				path: path,
				query: objParams
			})
		} else {
			router.push({
				path: path,
				query: objParams,
				animation: {
					animationType: 'slide-in-right',
					animationDuration: 200
				}

			})
		}

	},
	/**
	 * fn：检测图片协议，主要用于检测海报图片协议。
	 * param(imgPath): 图片地址。
	 */

	checkImgHttp(imgPath) {
		let newPath = '';
		let pathArr = imgPath.split('://');
		// #ifdef H5
		let ishttps = 'https:' == window.location.protocol ? true : false;
		ishttps ? (pathArr[0] = 'https') : (pathArr[0] = 'http');
		// #endif
		// #ifdef MP-WEIXIN
		pathArr[0] = 'https'
		// #endif
		newPath = pathArr.join('://');
		return newPath;
	},
	// 打电话
	callPhone(phoneNumber = '') {
		let num = phoneNumber.toString()
		uni.makePhoneCall({
			phoneNumber: num,
			fail(err) {
				console.log('makePhoneCall出错', err)
			},
		});
	},
	// 图片处理-选择图片
	chooseImage(count = 1) {
		return new Promise((resolve, reject) => {
			uni.chooseImage({
				count: count, //默认9
				sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ['camera', 'album'], //从相册选择
				success: res => {
					resolve(res.tempFilePaths);
				}
			});
		}).catch(e => {
			reject(e)
		})
	},
	getWxImageFileToBase64(path){
		return new Promise(resolve => {
			//获取全局唯一的文件管理器
			uni.getFileSystemManager()
				.readFile({ //读取本地文件内容
					filePath: path, // 文件路径
					encoding: 'base64', // 返回格式
					success: ({
						data
					}) => {
						return resolve('data:image/png;base64,' + data);
					},
					fail(res) {
						uni.showToast({
							title: '上传出错，请重试。'
						})
					}
				});
		});
	},
	uploadImageBase64(url,header,data){
		return new Promise((resolve, reject) => {
			uni.showLoading({
				title: '上传中'
			});
			
			uni.request({
				url: url,
				method: 'POST',
				header:header,
				data: {
					file : data
				},
				success: res => {
					uni.hideLoading()
					return resolve(res.data);
				},
				fail: () => {
					uni.hideLoading()
					reject(err);
				},
				complete: () => {}
			});
		})
	},
	// 图片处理-上传图片
	uploadImage(url, header, path) {
		return new Promise((resolve, reject) => {
			uni.showLoading({
				title: '上传中'
			});

			uni.uploadFile({
				url: url,
				filePath: path,
				name: 'file',
				header: header,
				success: function(uploadFileRes) {
					uni.hideLoading()
					return resolve(JSON.parse(uploadFileRes.data));
				},
				fail: (err) => {
					uni.hideLoading()
					reject(err);
				},
			});
		})
	},
	// 图片处理-预览图片
	previewImage(urls = [], current = 0,size,quality) {
		let newUrls=[]
		urls.forEach(item=>{
			if (item.indexOf('oss.') > 0) {
				let str = item
				if (size) str += "&width=" + size;
				if (quality) str += "&quality=" + quality
				newUrls.push(str)
			} else {
				newUrls.push(item)
			}
		})
		uni.previewImage({
			urls: newUrls,
			current: current,
			indicator: 'default',
			loop: true,
			fail(err) {
				console.log('previewImage出错', urls, err)
			},
		})
	},
	// 图片处理-获取图片信息
	getImageInfo(src = '') {
		return new Promise((resolve, reject) => {
			uni.getImageInfo({
				src: src,
				success: (image) => {
					resolve(image)
				},
				fail(err) {
					console.log('getImageInfo出错', src, err)
				},
			})
		}).catch(e => {
			reject(e)
		})

	},
	/**
	 * 格式化时间
	 */
	//时间格式化 天时分秒
	format(t) {
		let format = {
			d: '00',
			h: '00',
			m: '00',
			s: '00',
		}
		if (t > 0) {
			let d = Math.floor(t / 86400)
			let h = Math.floor((t / 3600) % 24)
			let m = Math.floor((t / 60) % 60)
			let s = Math.floor(t % 60)
			format.d = d < 10 ? '0' + d : d
			format.h = h < 10 ? '0' + h : h
			format.m = m < 10 ? '0' + m : m
			format.s = s < 10 ? '0' + s : s
		}
		return format
	},
	//时间格式化(格式化最大为小时)
	formatToHours(t) {
		let format = {
			d: '00',
			h: '00',
			m: '00',
			s: '00',
		}
		if (t > 0) {
			let h = Math.floor(t / 3600)
			let m = Math.floor((t / 60) % 60)
			let s = Math.floor(t % 60)

			format.h = h < 10 ? '0' + h : h
			format.m = m < 10 ? '0' + m : m
			format.s = s < 10 ? '0' + s : s
		}
		return format
	},
	// 年月日
	timestamp(timestamp) {
		let date = new Date(timestamp * 1000); //根据时间戳生成的时间对象
		let y = date.getFullYear();
		let m = date.getMonth() + 1;
		let d = date.getDate();

		m = m < 10 ? '0' + m : m;
		d = d < 10 ? '0' + d : d

		let dateText = y + "-" + m + "-" + d
		return dateText
	},
	// 年月日，时分秒
	// "YYYY-mm-dd HH:MM"
	dateFormat(fmt, date) {
		let ret;
		const opt = {
			"Y+": date.getFullYear().toString(), // 年
			"m+": (date.getMonth() + 1).toString(), // 月
			"d+": date.getDate().toString(), // 日
			"H+": date.getHours().toString(), // 时
			"M+": date.getMinutes().toString(), // 分
			"S+": date.getSeconds().toString() // 秒
			// 有其他格式化字符需求可以继续添加，必须转化成字符串
		};
		for (let k in opt) {
			ret = new RegExp("(" + k + ")").exec(fmt);
			if (ret) {
				fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
			};
		};
		return fmt;
	},
	/**
	 *  @fn  时间间隔格式化
	 *  @param {*} startTime 开始时间的时间戳
	 *  @param {*} endTime 结束时间的时间戳
	 *  @return {string} str 返回时间字符串
	 */
	getTimeInterval(startTime, endTime) {
		let runTime = parseInt((endTime - startTime) / 1000);
		let year = Math.floor(runTime / 86400 / 365);
		runTime = runTime % (86400 * 365);
		let month = Math.floor(runTime / 86400 / 30);
		runTime = runTime % (86400 * 30);
		let day = Math.floor(runTime / 86400);
		runTime = runTime % 86400;
		let hour = Math.floor(runTime / 3600);
		runTime = runTime % 3600;
		let minute = Math.floor(runTime / 60);
		runTime = runTime % 60;
		let second = runTime;
		let str = '';
		if (year > 0) {
			str = year + '年';
		}
		if (year <= 0 && month > 0) {
			str = month + '月';
		}
		if (year <= 0 && month <= 0 && day > 0) {
			str = day + '天';
		}
		if (year <= 0 && month <= 0 && day <= 0 && hour > 0) {
			str = hour + '小时';
		}
		if (year <= 0 && month <= 0 && day <= 0 && hour <= 0 && minute > 0) {
			str = minute + '分钟';
		}
		if (year <= 0 && month <= 0 && day <= 0 && hour <= 0 && minute <= 0 && second > 0) {
			str += second + '秒';
		}
		str += '前';
		return str;
	},


	/**提示框
	 *title(标题)
	 *icon(图标):  success，loading，none
	 *duration(延时): 0为不关闭, 毫秒数
	 *options(其它参数)
	 */
	toast(title, icon = 'none', options) {
		wx.showToast({
			title: title || '',
			icon: icon,
			duration: (options && options.duration) || 1500,
			image: (options && options.image) || '',
			mask: (options && options.mask) || true,
		});
	},

	/**
	 *@alias 节流
	 *@param {function} fn 节流被执行函数 
	 *@param {Number}  delay 时间单位内
	 */
	throttle(fn, delay) {
		let flag = true,
			timer = null;
		return function(...args) {
			let context = this
			if (!flag) return
			flag = false
			clearTimeout(timer)
			timer = setTimeout(() => {
				fn.apply(context, args)
				flag = true
			}, delay)
		}
	},

	/**
	 *@alias 防抖
	 *@param {function} fn 防抖被执行函数 
	 *@param {Number}  delay 时间单位内
	 */
	debounce(fn, delay) {
		let timer = null
		return function(...args) {
			let context = this
			if (timer) clearTimeout(timer)
			timer = setTimeout(function() {
				fn.apply(context, args)
			}, delay)
		}
	},

	back(fn_name) {
		var pages = getCurrentPages();
		if(pages.length<=1) return
		let newFnName=fn_name.replace('()','')
		var currPage = pages[pages.length - 1]; //当前页面
		var prevPage = pages[pages.length - 2]; //上一个页面
	　　uni.navigateBack({
		　　success: function() {
				prevPage.$vm[newFnName]()
		　　}
	　　});
		
		
	},

	showImg(url, size = 150, quality = 70) {
		if (!url) return
		if (url.indexOf('oss.') > 0) {
			let str = url
			if (size) str += "&width=" + size;
			if (quality) str += "&quality=" + quality
			return str
		} else {
			return url
		}
	},
	//得到实际高度
	getClientHeight(deduct) {
		const res = uni.getSystemInfoSync();
		let w = res.windowHeight
		if (deduct) w -= deduct
		const system = res.platform;
		if (system === 'ios') {
			w = w - 44 - res.statusBarHeight;
		} else if (system === 'android') {
			w = w - 48 - res.statusBarHeight;
		}
		return w - res.windowBottom - 5
	},

	//得到字符串长度，英文1，中文2
	strLength(str) {
		var realLength = 0,
			len = str.length,
			charCode = -1;
		for (var i = 0; i < len; i++) {
			charCode = str.charCodeAt(i);
			if (charCode >= 0 && charCode <= 128) realLength += 1;
			else realLength += 2;
		}
		return realLength;
	},
	
	
}

import http from '@/common/request'

// #ifdef H5
import wxsdk from '@/common/wechat/sdk';
// #endif

// #ifdef APP-PLUS
import permision from "@/common/permission/permission.js"

async function checkPermission(code) {
		let status = permision.isIOS ? await permision.requestIOS('camera') :
			await permision.requestAndroid('android.permission.CAMERA')

		if (status === null || status === 1) {
			status = 1
		} else {
			uni.showModal({
				content: "需要相机权限",
				confirmText: "设置",
				success: function(res) {
					if (res.confirm) {
						permision.gotoAppSetting()
					}
				}
			})
		}
		return status
	},
	// #endif

async function scan() {
	return new Promise((resolve, reject) => {

		// #ifdef APP-PLUS
		let status = await this.checkPermission()
		if (status !== 1) {
			reject("没有获得摄像头权限")
			return
		}
		// #endif

		// #ifndef H5
		uni.scanCode({
			success: (res) => {
				let code = res.result
				if (!code) {
					reject("没有扫描到内容")
					return
				}

				resolve(code)
			},
			fail: (err) => {
				// 需要注意的是小程序扫码不需要申请相机权限
			}
		});
		// #endif

		// #ifdef H5
		wxsdk.scanQRCode().then((codeData) => {
			//codeData={"resultStr":"EAN_13,XXXXXX","errMsg":"scanQRCode:ok"} //条码
			//codeData={"resultStr":"xxxxx","errMsg":"scanQRCode:ok"}  //二维码
			if (codeData && codeData.resultStr) {
				let arr = codeData.resultStr.split(',')
				let code = ''
				if (arr.length > 1) {
					code = arr[1]
				} else {
					code = codeData.resultStr
				}
				if (!code) {
					reject("没有扫描到内容")
					return
				}

				resolve(code)

			}
		})
		// #endif
	})
}

export default {
	scan,
}
